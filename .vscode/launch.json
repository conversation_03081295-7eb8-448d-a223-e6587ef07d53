{"version": "0.2.0", "configurations": [{"name": "Python: <PERSON><PERSON><PERSON><PERSON><PERSON> Debug", "type": "python", "request": "launch", "program": "${workspaceFolder}/tools/dataset_converters/simplified_projection_processor.py", "args": ["--calibration", "output/tmp/annotations/calibration.json", "--input", "/home/<USER>/data/RS10_data/hc_rs10_q2_wo_floor_2_0718_v2/train/", "--output-dir", "/home/<USER>/data/RS10_data/04_to_yuhui/", "--direct-inference", "--model-config", "configs/mask2former_config.py", "--model-checkpoint", "checkpoints/hc_rs10_q2_wo_floor_2_0718_v2_best_coco_segm_mAP_85_epoch_539.pth", "--selected-filelist", "output/tmp/selected_filelist.txt", "--batch", "--max-images", "10"], "cwd": "${workspaceFolder}", "console": "integratedTerminal", "justMyCode": false, "python": "/opt/conda/bin/python3", "env": {"PYTHONPATH": "${workspaceFolder}"}}]}