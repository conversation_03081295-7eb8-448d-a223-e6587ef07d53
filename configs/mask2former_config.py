dataset_type = 'hc3dCocoDataset'
# data_root = '/home/<USER>/data/RS10_data/hc_rs10_q2_wo_floor_2_0722_v1/'
# data_root = '/home/<USER>/data/RS10_data/hc_rs10_q2_wo_floor_2_0718_v2/'
# data_root = '/home/<USER>/data/RS10_data/hc_rs10_q2_wo_floor_2_0731_512x512/'
# data_root = '/home/<USER>/data/RS10_data/hc_rs10_q2_wo_floor_2_0731_1024x1024/'

# data_root = '/home/<USER>/data/RS10_data/04_to_yuhui_densitymap/0813_All_512x512/'
data_root = '/home/<USER>/data/RS10_data/04_to_yuhui_densitymap/0814_All_1024x1024/'

# mean_rgb = [10.78, 10.78, 10.78], # BGR mean # hc_rs10_q2_wo_floor_2_0721
# std_rgb = [50.98, 50.98, 50.98], # BGR std  # hc_rs10_q2_wo_floor_2_0721

# mean_rgb = [58.10, 58.10, 58.10], # BGR mean # hc_rs10_q2_wo_floor_2_0721_v2
# std_rgb = [106.95, 106.95, 106.95], # BGR std  # hc_rs10_q2_wo_floor_2_0721_v2

# mean_rgb = [20.20, 20.20, 20.20], # BGR mean # hc_rs10_q2_wo_floor_2_0721_v3
# std_rgb = [59.04, 59.04, 59.04], # BGR std  # hc_rs10_q2_wo_floor_2_0721_v3

# mean_rgb = [25.45, 25.45, 25.45], # BGR mean # hc_rs10_q2_wo_floor_2_0722_v1
# std_rgb  = [59.24, 59.24, 59.24], # BGR std  # hc_rs10_q2_wo_floor_2_0722_v1

# mean_rgb = [2.89, 2.89, 2.89], # BGR mean # hc_rs10_q2_wo_floor_2_0718_v2
# std_rgb  = [15.93, 15.93, 15.93], # BGR std  # hc_rs10_q2_wo_floor_2_0718_v2

# mean_rgb = [15.16, 15.16, 15.16], # BGR mean # hc_rs10_q2_wo_floor_2_0730_512x512
# std_rgb  = [44.50, 44.50, 44.50], # BGR std  # hc_rs10_q2_wo_floor_2_0730_512x512

mean_rgb = [15.28, 15.28, 15.28], # BGR mean   # 0813_All_512x512
std_rgb  = [44.13, 44.13, 44.13], # BGR std    # 0813_All_512x512



train_ann_file = data_root + 'annotations/train.json'
val_ann_file = data_root + 'annotations/val.json'
test_ann_file = data_root + 'annotations/val.json'

train_data_prefix = dict(img='train/')
val_data_prefix = dict(img='val/')
test_data_prefix = dict(img='val/')


set_img_size=512
img_scale=(set_img_size, set_img_size)
img_size=(set_img_size, set_img_size)
img_crop_size=(set_img_size, set_img_size)
batch_augments_size=(set_img_size, set_img_size)
panoptic_head_feat_channels=set_img_size
panoptic_head_out_channels=set_img_size
self_attn_embed_dims=set_img_size
positional_encoding_num_feats=self_attn_embed_dims//2

backend_args = None
train_pipeline = [
    dict(type='LoadImageFromFile', to_float32=True, backend_args=None),
    dict(type='LoadAnnotations', with_bbox=True, with_mask=True),
    dict(type='RandomFlip', prob=0.5),
    dict(
        type='RandomResize',
        scale=img_scale,
        ratio_range=(0.1, 2.0),
        resize_type='Resize',
        keep_ratio=True),
    dict(
        type='RandomCrop',
        crop_size=img_crop_size,
        crop_type='absolute',
        recompute_bbox=True,
        allow_negative_crop=True),
    dict(
        type='FilterAnnotations', min_gt_bbox_wh=(1e-05, 1e-05), by_mask=True),
    dict(type='PackDetInputs')
]
test_pipeline = [
    dict(type='LoadImageFromFile', to_float32=True, backend_args=None),
    dict(type='Resize', scale=img_scale, keep_ratio=True),
    dict(type='LoadAnnotations', with_bbox=True, with_mask=True),
    dict(
        type='PackDetInputs',
        meta_keys=('img_id', 'img_path', 'ori_shape', 'img_shape',
                   'scale_factor'))
]
train_dataloader = dict(
    batch_size=1,
    num_workers=1,
    persistent_workers=True,
    sampler=dict(type='DefaultSampler', shuffle=True),
    batch_sampler=dict(type='AspectRatioBatchSampler'),
    dataset=dict(
        type='hc3dCocoDataset',
        data_root=data_root,
        ann_file=train_ann_file,
        data_prefix=train_data_prefix,
        filter_cfg=dict(filter_empty_gt=True, min_size=32),
        pipeline=[
            dict(type='LoadImageFromFile', to_float32=True, backend_args=None),
            dict(type='LoadAnnotations', with_bbox=True, with_mask=True),
            dict(type='RandomFlip', prob=0.5),
            dict(
                type='RandomResize',
                scale=img_scale,
                ratio_range=(0.1, 2.0),
                resize_type='Resize',
                keep_ratio=True),
            dict(
                type='RandomCrop',
                crop_size=img_crop_size,
                crop_type='absolute',
                recompute_bbox=True,
                allow_negative_crop=True),
            dict(
                type='FilterAnnotations',
                min_gt_bbox_wh=(1e-05, 1e-05),
                by_mask=True),
            dict(type='PackDetInputs')
        ],
        backend_args=None))
val_dataloader = dict(
    batch_size=1,
    num_workers=1,
    persistent_workers=True,
    drop_last=False,
    sampler=dict(type='DefaultSampler', shuffle=False),
    dataset=dict(
        type='hc3dCocoDataset',
        data_root=data_root,
        ann_file=val_ann_file,
        data_prefix=val_data_prefix,
        test_mode=True,
        pipeline=[
            dict(type='LoadImageFromFile', to_float32=True, backend_args=None),
            dict(type='Resize', scale=img_scale, keep_ratio=True),
            dict(type='LoadAnnotations', with_bbox=True, with_mask=True),
            dict(
                type='PackDetInputs',
                meta_keys=('img_id', 'img_path', 'ori_shape', 'img_shape',
                           'scale_factor'))
        ],
        backend_args=None))
test_dataloader = dict(
    batch_size=1,
    num_workers=1,
    persistent_workers=True,
    drop_last=False,
    sampler=dict(type='DefaultSampler', shuffle=False),
    dataset=dict(
        type='hc3dCocoDataset',
        data_root=data_root,
        ann_file=test_ann_file,
        data_prefix=test_data_prefix,
        test_mode=True,
        pipeline=[
            dict(type='LoadImageFromFile', to_float32=True, backend_args=None),
            dict(type='Resize', scale=img_scale, keep_ratio=True),
            dict(type='LoadAnnotations', with_bbox=True, with_mask=True),
            dict(
                type='PackDetInputs',
                meta_keys=('img_id', 'img_path', 'ori_shape', 'img_shape',
                           'scale_factor'))
        ],
        backend_args=None))

# val_evaluator = dict(
#     type='CocoMetricIoU85',
#     ann_file=val_ann_file,
#     metric=['bbox', 'segm'],
#     format_only=False)

# val_evaluator = dict(
#     type='CocoMetricIoU85',
#     ann_file=val_ann_file,
#     metric='segm',  # 或 'bbox'
#     metric_items=['mAP', 'mAP_85'],
#     classwise=True
# )

# 修改val_evaluator，使用列表形式支持多个评价器
val_evaluator = [
    # 1. 保留原有的COCO IoU@85评价（用于对比）
    dict(
        type='CocoMetricIoU85',
        ann_file=val_ann_file,
        metric='segm',
        metric_items=['mAP', 'mAP_85'],
        classwise=True,
        prefix='coco'  # 添加前缀以区分不同评价器的输出
    ),
    # 2. 添加改进的像素级评价器（支持二分类模式）
    dict(
        type='ImprovedPixelMetric',
        ann_file=val_ann_file,
        evaluation_mode='pure_pixel',  # 评价模式：pure_pixel, instance_aware, weighted_pixel
        num_classes=16,  # 根据您的实际类别数调整
        binary_classification=True,  # 启用二分类模式
        room_classes=list(range(13)),  # 房间类别ID列表
        non_room_classes=[13, 14, 15],  # 非房间类别ID列表
        iou_threshold=0.5,  # 实例匹配的IoU阈值
        score_threshold=0.3,  # 预测置信度阈值
        nms_pre=1000,  # NMS前保留的最大数量
        output_dir='work_dirs/improved_pixel_metrics',  # 保存详细结果的目录
        prefix='improved_pixel'  # 指标前缀
    )
]

# 测试评价器使用相同配置
test_evaluator = [
    # 1. 保留原有的COCO IoU@85评价（用于对比）
    dict(
        type='CocoMetricIoU85',
        ann_file=test_ann_file,
        metric='segm',
        metric_items=['mAP', 'mAP_85'],
        classwise=True,
        prefix='coco'  # 添加前缀以区分不同评价器的输出
    ),
    # 2. 添加改进的像素级评价器（支持二分类模式）
    dict(
        type='ImprovedPixelMetric',
        ann_file=test_ann_file,
        evaluation_mode='pure_pixel',  # 评价模式：pure_pixel, instance_aware, weighted_pixel
        num_classes=16,  # 根据您的实际类别数调整
        binary_classification=True,  # 启用二分类模式
        room_classes=list(range(13)),  # 房间类别ID列表
        non_room_classes=[13, 14, 15],  # 非房间类别ID列表
        iou_threshold=0.5,  # 实例匹配的IoU阈值
        score_threshold=0.3,  # 预测置信度阈值
        nms_pre=1000,  # NMS前保留的最大数量
        output_dir='work_dirs/improved_pixel_metrics',  # 保存详细结果的目录
        prefix='improved_pixel'  # 指标前缀
    )
]

default_scope = 'mmdet'
# Automatically import custom metric modules so MMEngine/MMDetection registry can find them
custom_imports = dict(
    imports=['tools.custom_metrics.coco_metric_iou85', 'tools.custom_metrics.improved_pixel_metric'],
    allow_failed_imports=False)
default_hooks = dict(
    timer=dict(type='IterTimerHook'),
    logger=dict(type='LoggerHook', interval=50),
    param_scheduler=dict(type='ParamSchedulerHook'),
    checkpoint=dict(
        type='CheckpointHook',
        interval=50,
        by_epoch=True,
        save_last=True,
        # Use segmentation AP@85 as criterion
        save_best='coco/segm_mAP_85',
        # save_best='pixel/recall',  # 注意前缀要与评价器的prefix一致
        # save_best='pixel/precision',  # 注意前缀要与评价器的prefix一致
        rule='greater',
        max_keep_ckpts=1),
    sampler_seed=dict(type='DistSamplerSeedHook'),
    visualization=dict(type='DetVisualizationHook'))
env_cfg = dict(
    cudnn_benchmark=False,
    mp_cfg=dict(mp_start_method='fork', opencv_num_threads=0),
    dist_cfg=dict(backend='nccl'))
vis_backends = [dict(type='LocalVisBackend')]
visualizer = dict(
    type='DetLocalVisualizer',
    vis_backends=[dict(type='LocalVisBackend')],
    name='visualizer')
log_processor = dict(type='LogProcessor', window_size=50, by_epoch=True)
log_level = 'INFO'
load_from = None
resume = False
image_size = img_size
batch_augments = [
    dict(
        type='BatchFixedSizePad',
        size=batch_augments_size,
        img_pad_value=0,
        pad_mask=True,
        mask_pad_value=0,
        pad_seg=False)
]
data_preprocessor = dict(
    type='DetDataPreprocessor',
    mean = mean_rgb,
    std = std_rgb,
    bgr_to_rgb=True,
    pad_size_divisor=32,
    pad_mask=True,
    mask_pad_value=0,
    pad_seg=False,
    seg_pad_value=255,
    batch_augments=[
        dict(
            type='BatchFixedSizePad',
            size=batch_augments_size,
            img_pad_value=0,
            pad_mask=True,
            mask_pad_value=0,
            pad_seg=False)
    ])
num_things_classes = 16
num_stuff_classes = 0
num_classes = 16
model = dict(
    type='Mask2Former',
    data_preprocessor=dict(
        type='DetDataPreprocessor',
        mean=mean_rgb,
        std=std_rgb,
        bgr_to_rgb=True,
        pad_size_divisor=32,
        pad_mask=True,
        mask_pad_value=0,
        pad_seg=False,
        seg_pad_value=255,
        batch_augments=[
            dict(
                type='BatchFixedSizePad',
                size=batch_augments_size,
                img_pad_value=0,
                pad_mask=True,
                mask_pad_value=0,
                pad_seg=False)
        ]),
    backbone=dict(
        type='SwinTransformer',
        embed_dims=192,
        depths=[2, 2, 18, 2],
        num_heads=[6, 12, 24, 48],
        window_size=12,
        mlp_ratio=4,
        qkv_bias=True,
        qk_scale=None,
        drop_rate=0.0,
        attn_drop_rate=0.0,
        drop_path_rate=0.3,
        patch_norm=True,
        out_indices=(0, 1, 2, 3),
        with_cp=False,
        convert_weights=True,
        frozen_stages=-1,
        init_cfg=dict(
            type='Pretrained',
            checkpoint=
            'pretrained_model/swin_large_patch4_window12_384_22k.pth'
        ),
        pretrain_img_size=384),
    panoptic_head=dict(
        type='Mask2FormerHead',
        in_channels=[192, 384, 768, 1536],
        strides=[4, 8, 16, 32],
        feat_channels=panoptic_head_feat_channels,
        out_channels=panoptic_head_out_channels,
        num_things_classes=16,
        num_stuff_classes=0,
        num_queries=200,
        num_transformer_feat_level=3,
        pixel_decoder=dict(
            type='MSDeformAttnPixelDecoder',
            num_outs=3,
            norm_cfg=dict(type='GN', num_groups=32),
            act_cfg=dict(type='ReLU'),
            encoder=dict(
                num_layers=6,
                layer_cfg=dict(
                    self_attn_cfg=dict(
                        embed_dims=self_attn_embed_dims,
                        num_heads=8,
                        num_levels=3,
                        num_points=4,
                        dropout=0.0,
                        batch_first=True),
                    ffn_cfg=dict(
                        embed_dims=self_attn_embed_dims,
                        feedforward_channels=1024,
                        num_fcs=2,
                        ffn_drop=0.0,
                        act_cfg=dict(type='ReLU', inplace=True)))),
            positional_encoding=dict(num_feats=positional_encoding_num_feats, normalize=True)),
        enforce_decoder_input_project=False,
        positional_encoding=dict(num_feats=positional_encoding_num_feats, normalize=True),
        transformer_decoder=dict(
            return_intermediate=True,
            num_layers=9,
            layer_cfg=dict(
                self_attn_cfg=dict(
                    embed_dims=self_attn_embed_dims, num_heads=8, dropout=0.0,
                    batch_first=True),
                cross_attn_cfg=dict(
                    embed_dims=self_attn_embed_dims, num_heads=8, dropout=0.0,
                    batch_first=True),
                ffn_cfg=dict(
                    embed_dims=self_attn_embed_dims,
                    feedforward_channels=2048,
                    num_fcs=2,
                    ffn_drop=0.0,
                    act_cfg=dict(type='ReLU', inplace=True))),
            init_cfg=None),
        loss_cls=dict(
            type='CrossEntropyLoss',
            use_sigmoid=False,
            loss_weight=2.0,
            reduction='mean',
            class_weight=[
                1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
                1.0, 1.0, 1.0, 1.0, 0.1
            ]),
        loss_mask=dict(
            type='CrossEntropyLoss',
            use_sigmoid=True,
            reduction='mean',
            loss_weight=5.0),
        loss_dice=dict(
            type='DiceLoss',
            use_sigmoid=True,
            activate=True,
            reduction='mean',
            naive_dice=True,
            eps=1.0,
            loss_weight=5.0)),
    panoptic_fusion_head=dict(
        type='MaskFormerFusionHead',
        num_things_classes=16,
        num_stuff_classes=0,
        loss_panoptic=None,
        init_cfg=None),
    train_cfg=dict(
        num_points=12544,
        oversample_ratio=3.0,
        importance_sample_ratio=0.75,
        assigner=dict(
            type='HungarianAssigner',
            match_costs=[
                dict(type='ClassificationCost', weight=2.0),
                dict(
                    type='CrossEntropyLossCost', weight=5.0, use_sigmoid=True),
                dict(type='DiceCost', weight=5.0, pred_act=True, eps=1.0)
            ]),
        sampler=dict(type='MaskPseudoSampler')),
    test_cfg=dict(
        panoptic_on=False,
        semantic_on=False,
        instance_on=True,
        max_per_image=100,
        iou_thr=0.8,
        filter_low_score=True),
    init_cfg=None)
embed_multi = dict(lr_mult=1.0, decay_mult=0.0)
optim_wrapper = dict(
    type='OptimWrapper',
    optimizer=dict(
        type='AdamW',
        lr=0.0001,
        weight_decay=0.05,
        eps=1e-08,
        betas=(0.9, 0.999)),
    paramwise_cfg=dict(
        custom_keys=dict({
            'backbone':
            dict(lr_mult=0.1, decay_mult=1.0),
            'query_embed':
            dict(lr_mult=1.0, decay_mult=0.0),
            'query_feat':
            dict(lr_mult=1.0, decay_mult=0.0),
            'level_embed':
            dict(lr_mult=1.0, decay_mult=0.0),
            'backbone.patch_embed.norm':
            dict(lr_mult=0.1, decay_mult=0.0),
            'backbone.norm':
            dict(lr_mult=0.1, decay_mult=0.0),
            'absolute_pos_embed':
            dict(lr_mult=0.1, decay_mult=0.0),
            'relative_position_bias_table':
            dict(lr_mult=0.1, decay_mult=0.0),
            'backbone.stages.0.blocks.0.norm':
            dict(lr_mult=0.1, decay_mult=0.0),
            'backbone.stages.0.blocks.1.norm':
            dict(lr_mult=0.1, decay_mult=0.0),
            'backbone.stages.1.blocks.0.norm':
            dict(lr_mult=0.1, decay_mult=0.0),
            'backbone.stages.1.blocks.1.norm':
            dict(lr_mult=0.1, decay_mult=0.0),
            'backbone.stages.2.blocks.0.norm':
            dict(lr_mult=0.1, decay_mult=0.0),
            'backbone.stages.2.blocks.1.norm':
            dict(lr_mult=0.1, decay_mult=0.0),
            'backbone.stages.2.blocks.2.norm':
            dict(lr_mult=0.1, decay_mult=0.0),
            'backbone.stages.2.blocks.3.norm':
            dict(lr_mult=0.1, decay_mult=0.0),
            'backbone.stages.2.blocks.4.norm':
            dict(lr_mult=0.1, decay_mult=0.0),
            'backbone.stages.2.blocks.5.norm':
            dict(lr_mult=0.1, decay_mult=0.0),
            'backbone.stages.3.blocks.0.norm':
            dict(lr_mult=0.1, decay_mult=0.0),
            'backbone.stages.3.blocks.1.norm':
            dict(lr_mult=0.1, decay_mult=0.0),
            'backbone.stages.0.downsample.norm':
            dict(lr_mult=0.1, decay_mult=0.0),
            'backbone.stages.1.downsample.norm':
            dict(lr_mult=0.1, decay_mult=0.0),
            'backbone.stages.2.downsample.norm':
            dict(lr_mult=0.1, decay_mult=0.0),
            'backbone.stages.2.blocks.6.norm':
            dict(lr_mult=0.1, decay_mult=0.0),
            'backbone.stages.2.blocks.7.norm':
            dict(lr_mult=0.1, decay_mult=0.0),
            'backbone.stages.2.blocks.8.norm':
            dict(lr_mult=0.1, decay_mult=0.0),
            'backbone.stages.2.blocks.9.norm':
            dict(lr_mult=0.1, decay_mult=0.0),
            'backbone.stages.2.blocks.10.norm':
            dict(lr_mult=0.1, decay_mult=0.0),
            'backbone.stages.2.blocks.11.norm':
            dict(lr_mult=0.1, decay_mult=0.0),
            'backbone.stages.2.blocks.12.norm':
            dict(lr_mult=0.1, decay_mult=0.0),
            'backbone.stages.2.blocks.13.norm':
            dict(lr_mult=0.1, decay_mult=0.0),
            'backbone.stages.2.blocks.14.norm':
            dict(lr_mult=0.1, decay_mult=0.0),
            'backbone.stages.2.blocks.15.norm':
            dict(lr_mult=0.1, decay_mult=0.0),
            'backbone.stages.2.blocks.16.norm':
            dict(lr_mult=0.1, decay_mult=0.0),
            'backbone.stages.2.blocks.17.norm':
            dict(lr_mult=0.1, decay_mult=0.0)
        }),
        norm_decay_mult=0.0),
    clip_grad=dict(max_norm=0.01, norm_type=2))
max_iters = 368750
param_scheduler = dict(
    type='MultiStepLR',
    begin=0,
    end=368750,
    by_epoch=False,
    milestones=[327778, 355092],
    gamma=0.1)
interval = 5000
dynamic_intervals = [(365001, 368750)]
train_cfg = dict(type='EpochBasedTrainLoop', max_epochs=2000, val_interval=1)
val_cfg = dict(type='ValLoop')
test_cfg = dict(type='TestLoop')
auto_scale_lr = dict(enable=False, base_batch_size=16)
pretrained = 'https://github.com/SwinTransformer/storage/releases/download/v1.0.0/swin_large_patch4_window12_384_22k.pth'
depths = [2, 2, 18, 2]
backbone_norm_multi = dict(lr_mult=0.1, decay_mult=0.0)
backbone_embed_multi = dict(lr_mult=0.1, decay_mult=0.0)
custom_keys = dict({
    'backbone':
    dict(lr_mult=0.1, decay_mult=1.0),
    'backbone.patch_embed.norm':
    dict(lr_mult=0.1, decay_mult=0.0),
    'backbone.norm':
    dict(lr_mult=0.1, decay_mult=0.0),
    'absolute_pos_embed':
    dict(lr_mult=0.1, decay_mult=0.0),
    'relative_position_bias_table':
    dict(lr_mult=0.1, decay_mult=0.0),
    'query_embed':
    dict(lr_mult=1.0, decay_mult=0.0),
    'query_feat':
    dict(lr_mult=1.0, decay_mult=0.0),
    'level_embed':
    dict(lr_mult=1.0, decay_mult=0.0),
    'backbone.stages.0.blocks.0.norm':
    dict(lr_mult=0.1, decay_mult=0.0),
    'backbone.stages.0.blocks.1.norm':
    dict(lr_mult=0.1, decay_mult=0.0),
    'backbone.stages.1.blocks.0.norm':
    dict(lr_mult=0.1, decay_mult=0.0),
    'backbone.stages.1.blocks.1.norm':
    dict(lr_mult=0.1, decay_mult=0.0),
    'backbone.stages.2.blocks.0.norm':
    dict(lr_mult=0.1, decay_mult=0.0),
    'backbone.stages.2.blocks.1.norm':
    dict(lr_mult=0.1, decay_mult=0.0),
    'backbone.stages.2.blocks.2.norm':
    dict(lr_mult=0.1, decay_mult=0.0),
    'backbone.stages.2.blocks.3.norm':
    dict(lr_mult=0.1, decay_mult=0.0),
    'backbone.stages.2.blocks.4.norm':
    dict(lr_mult=0.1, decay_mult=0.0),
    'backbone.stages.2.blocks.5.norm':
    dict(lr_mult=0.1, decay_mult=0.0),
    'backbone.stages.3.blocks.0.norm':
    dict(lr_mult=0.1, decay_mult=0.0),
    'backbone.stages.3.blocks.1.norm':
    dict(lr_mult=0.1, decay_mult=0.0),
    'backbone.stages.0.downsample.norm':
    dict(lr_mult=0.1, decay_mult=0.0),
    'backbone.stages.1.downsample.norm':
    dict(lr_mult=0.1, decay_mult=0.0),
    'backbone.stages.2.downsample.norm':
    dict(lr_mult=0.1, decay_mult=0.0),
    'backbone.stages.2.blocks.6.norm':
    dict(lr_mult=0.1, decay_mult=0.0),
    'backbone.stages.2.blocks.7.norm':
    dict(lr_mult=0.1, decay_mult=0.0),
    'backbone.stages.2.blocks.8.norm':
    dict(lr_mult=0.1, decay_mult=0.0),
    'backbone.stages.2.blocks.9.norm':
    dict(lr_mult=0.1, decay_mult=0.0),
    'backbone.stages.2.blocks.10.norm':
    dict(lr_mult=0.1, decay_mult=0.0),
    'backbone.stages.2.blocks.11.norm':
    dict(lr_mult=0.1, decay_mult=0.0),
    'backbone.stages.2.blocks.12.norm':
    dict(lr_mult=0.1, decay_mult=0.0),
    'backbone.stages.2.blocks.13.norm':
    dict(lr_mult=0.1, decay_mult=0.0),
    'backbone.stages.2.blocks.14.norm':
    dict(lr_mult=0.1, decay_mult=0.0),
    'backbone.stages.2.blocks.15.norm':
    dict(lr_mult=0.1, decay_mult=0.0),
    'backbone.stages.2.blocks.16.norm':
    dict(lr_mult=0.1, decay_mult=0.0),
    'backbone.stages.2.blocks.17.norm':
    dict(lr_mult=0.1, decay_mult=0.0)
})
launcher = 'none'
work_dir = 'work_dirs'
