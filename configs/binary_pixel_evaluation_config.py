# Binary Pixel-Level Evaluation Configuration
# 二分类像素级评价配置文件
# 
# 用法示例:
# python tools/inference_with_evaluators.py \
#   --config configs/mask2former_config.py \
#   --checkpoint checkpoints/your_model.pth \
#   --input /path/to/images \
#   --output output/binary_evaluation \
#   --ann-file /path/to/annotations.json \
#   --score-thr 0.3 \
#   --device cuda:0 \
#   --evaluators improved_pixel \
#   --pixel-mode pure_pixel

# 二分类配置
BINARY_CLASSIFICATION = True

# 房间类别定义（前15个类别）
ROOM_CLASSES = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14]
# 对应类别名称:
# 0: living room, 1: kitchen, 2: bedroom, 3: bathroom, 4: balcony
# 5: corridor, 6: dining room, 7: study, 8: studio, 9: store room
# 10: garden, 11: laundry room, 12: office, 13: basement, 14: garage

# 非房间类别定义（后3个类别，将被视为背景）
NON_ROOM_CLASSES = [15, 16, 17]
# 对应类别名称:
# 15: undefined, 16: door, 17: window

# 评价模式
PIXEL_EVALUATION_MODES = [
    'pure_pixel',      # 纯像素级评价
    'instance_aware',  # 实例感知像素级评价
    'weighted_pixel'   # 加权像素级评价
]

# 默认参数
DEFAULT_SCORE_THRESHOLD = 0.3
DEFAULT_IOU_THRESHOLD = 0.5
DEFAULT_NMS_PRE = 1000

# 输出指标说明
METRICS_DESCRIPTION = {
    'room_precision': '预测为房间的像素中，实际为房间的比例',
    'room_recall': '实际房间像素中，被正确预测为房间的比例',
    'room_f1': '房间类别的F1分数',
    'background_precision': '预测为背景的像素中，实际为背景的比例',
    'background_recall': '实际背景像素中，被正确预测为背景的比例',
    'background_f1': '背景类别的F1分数',
    'avg_precision': '两个类别精确率的平均值',
    'avg_recall': '两个类别召回率的平均值',
    'avg_f1': '两个类别F1分数的平均值'
}

# 使用说明
USAGE_INSTRUCTIONS = """
二分类像素级评价使用说明:

1. 房间类别 (前景): 包含所有房间类型 (living room, kitchen, bedroom, 等)
2. 背景类别 (背景): 包含门窗、未定义区域和真正的背景

3. 主要指标:
   - 房间类别指标: 评价房间区域的分割精度
   - 背景类别指标: 评价背景区域的分割精度
   - 平均指标: 两个类别的平均性能

4. 输出文件:
   - pure_pixel_metrics_summary.json: 总体指标摘要
   - pure_pixel_detailed_results.json: 每张图像的详细结果
   - 可视化结果图像
"""
