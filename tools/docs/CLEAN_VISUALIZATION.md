# 简洁可视化功能说明

## 概述

根据您的要求，我已经将 `inference_with_evaluators.py` 的可视化功能简化为最简洁的形式：
- ✅ **只保存一个图片文件**
- ✅ **使用鲜艳的随机颜色**
- ✅ **无文字标签**
- ✅ **无边界框**
- ✅ **清晰简洁的效果**

## 主要特性

### 🎨 鲜艳颜色方案
- 使用20种预定义的高饱和度、高亮度颜色
- 颜色包括：黄色、品红色、青色、绿色、蓝色、红色、橙色等
- 当实例数量超过20个时，自动生成更多鲜艳颜色

### 📁 简洁输出
- 每张图片只生成一个文件：`{image_name}_result.png`
- 如果没有预测结果，保存原图作为 `{image_name}_result.png`

### 🎯 视觉效果
- 透明度设置为0.6，让颜色更加突出
- 原图透明度0.4，保持背景可见
- 无任何文字、标签或边界框干扰

## 使用方法

### 基本使用
```bash
python tools/inference_with_evaluators.py \
    --config configs/mask2former_config.py \
    --checkpoint path/to/checkpoint.pth \
    --input path/to/images \
    --output output/results \
    --ann-file path/to/annotations.json \
    --evaluators coco
```

### 禁用可视化（仅评估）
```bash
python tools/inference_with_evaluators.py \
    --config configs/mask2former_config.py \
    --checkpoint path/to/checkpoint.pth \
    --input path/to/images \
    --output output/results \
    --ann-file path/to/annotations.json \
    --evaluators coco improved_pixel \
    --disable-visualization
```

## 输出文件

### 可视化文件
- `{image_name}_result.png` - 简洁的彩色掩码可视化

### 评估结果文件
- `coco_evaluation_results.json` - 主要评估指标
- `per_image_results.json` - 每张图像的详细结果
- `inference_log.txt` - 完整的运行日志

## 技术实现

### 颜色生成
```python
def generate_bright_colors(num_colors):
    """生成鲜艳且易于区分的颜色列表"""
    # 预定义的鲜艳颜色 (BGR格式)
    bright_colors = [
        (0, 255, 255),    # 黄色
        (255, 0, 255),    # 品红色
        (255, 255, 0),    # 青色
        (0, 255, 0),      # 绿色
        (255, 0, 0),      # 蓝色
        (0, 0, 255),      # 红色
        # ... 更多颜色
    ]
    return bright_colors[:num_colors]
```

### 可视化渲染
```python
def save_clean_visualization(img_path, result, output_dir):
    """保存简洁的可视化结果"""
    # 1. 读取原图
    original_img = cv2.imread(img_path)
    
    # 2. 生成鲜艳颜色
    colors = generate_bright_colors(len(masks))
    
    # 3. 创建彩色overlay
    overlay = original_img.copy()
    for i, mask in enumerate(masks):
        color = colors[i % len(colors)]
        overlay[mask > 0] = color
    
    # 4. 混合图像（alpha=0.6）
    result_img = cv2.addWeighted(original_img, 0.4, overlay, 0.6, 0)
    
    # 5. 保存结果
    cv2.imwrite(output_path, result_img)
```

## 颜色列表

预定义的20种鲜艳颜色（BGR格式）：

| 序号 | 颜色名称 | BGR值 | 视觉效果 |
|------|----------|-------|----------|
| 0 | 黄色 | (0, 255, 255) | 🟡 |
| 1 | 品红色 | (255, 0, 255) | 🟣 |
| 2 | 青色 | (255, 255, 0) | 🔵 |
| 3 | 绿色 | (0, 255, 0) | 🟢 |
| 4 | 蓝色 | (255, 0, 0) | 🔵 |
| 5 | 红色 | (0, 0, 255) | 🔴 |
| 6 | 橙色 | (255, 165, 0) | 🟠 |
| 7 | 紫色 | (128, 0, 128) | 🟣 |
| 8 | 粉色 | (255, 192, 203) | 🩷 |
| 9 | 春绿色 | (0, 255, 127) | 🟢 |
| ... | ... | ... | ... |

## 与原版本对比

| 特性 | 原版本 | 新版本 |
|------|--------|--------|
| 输出文件数量 | 1个 | 1个 ✅ |
| 颜色方案 | 随机暗色 | 鲜艳预定义色 ✅ |
| 文字标签 | 无 | 无 ✅ |
| 边界框 | 无 | 无 ✅ |
| 透明度 | 0.3 | 0.6 ✅ |
| 易区分性 | 一般 | 优秀 ✅ |

## 最佳实践

1. **日常使用**：直接运行，会自动生成简洁的可视化
2. **批量处理**：使用 `--disable-visualization` 跳过可视化，节省时间
3. **调试分析**：查看生成的 `per_image_results.json` 了解详细指标
4. **颜色效果**：如果需要更多颜色，系统会自动生成高饱和度随机色

## 兼容性

- ✅ 完全兼容现有的评估功能
- ✅ 支持所有评估器（COCO、Pixel等）
- ✅ 支持房屋类型分类评估
- ✅ 保持原有的命令行接口
- ✅ 向后兼容，不影响其他功能

现在您可以获得完全符合要求的简洁、鲜艳、清晰的可视化效果！
