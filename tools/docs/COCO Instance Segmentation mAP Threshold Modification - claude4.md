# Modifying COCO Instance Segmentation Metrics: Implementing mAP@85 in MMDetection

This comprehensive guide provides everything you need to modify COCO instance segmentation evaluation metrics to include an 85% IoU threshold, with specific focus on MMDetection implementation.

## COCO evaluation metrics fundamentally measure detection quality through IoU thresholds

The COCO evaluation system uses Intersection over Union (IoU) as the core metric for matching predictions to ground truth. By default, COCO computes mean Average Precision (mAP) across **10 IoU thresholds**: 0.50, 0.55, 0.60, 0.65, 0.70, 0.75, 0.80, 0.85, 0.90, and 0.95. The standard mAP is the average of AP values across all these thresholds, while AP@75 specifically uses the 0.75 threshold.

For instance segmentation, IoU is calculated at the pixel level:
```
IoU = |Predicted_Mask ∩ Ground_Truth_Mask| / |Predicted_Mask ∪ Ground_Truth_Mask|
```

The significance of moving from 75% to 85% IoU is substantial—it requires significantly more precise mask boundaries and will result in lower AP scores, but provides a much stricter quality assessment useful for applications requiring high precision like medical imaging or autonomous driving.

## MMDetection's CocoMetric class provides flexible customization mechanisms

The `CocoMetric` class in MMDetection inherits from `BaseMetric` and offers multiple approaches for customizing IoU thresholds. The class accepts an `iou_thrs` parameter that can be modified through configuration files, subclassing, or direct instantiation.

**Key implementation detail**: The default IoU thresholds are generated using NumPy's linspace function:
```python
if iou_thrs is None:
    iou_thrs = np.linspace(.5, 0.95, int(np.round((0.95 - .5) / .05)) + 1, endpoint=True)
```

The evaluation pipeline passes these thresholds directly to the COCO API's `COCOeval` class, which performs the actual metric computation.

## Implementation approach for adding mAP@85 support

### Method 1: Configuration File Modification (Simplest)

Modify your MMDetection config file to include custom IoU thresholds:

```python
# In your config file (e.g., mask_rcnn_r50_fpn_1x_coco.py)
val_evaluator = dict(
    type='CocoMetric',
    ann_file='data/coco/annotations/instances_val2017.json',
    metric='segm',  # For instance segmentation
    iou_thrs=[0.5, 0.55, 0.6, 0.65, 0.7, 0.75, 0.8, 0.85, 0.9, 0.95],  # Already includes 0.85
    metric_items=['mAP', 'mAP_50', 'mAP_75', 'mAP_85', 'mAP_s', 'mAP_m', 'mAP_l'],
    classwise=True
)

test_evaluator = val_evaluator
```

### Method 2: Custom CocoMetric Subclass (More Control)

Create a custom metric class that explicitly handles mAP@85:

```python
from mmdet.evaluation.metrics import CocoMetric
from mmdet.registry import METRICS
import numpy as np
from pycocotools.cocoeval import COCOeval

@METRICS.register_module()
class CocoMetricWith85(CocoMetric):
    """Extended COCO metric that includes mAP@85 calculation."""
    
    def __init__(self, **kwargs):
        # Ensure 0.85 is in the IoU thresholds
        if 'iou_thrs' not in kwargs:
            kwargs['iou_thrs'] = [0.5, 0.55, 0.6, 0.65, 0.7, 0.75, 0.8, 0.85, 0.9, 0.95]
        
        # Add mAP_85 to metric items if not present
        if 'metric_items' not in kwargs:
            kwargs['metric_items'] = ['mAP', 'mAP_50', 'mAP_75', 'mAP_85', 'mAP_s', 'mAP_m', 'mAP_l']
        elif 'mAP_85' not in kwargs['metric_items']:
            kwargs['metric_items'].append('mAP_85')
            
        super().__init__(**kwargs)
        
    def compute_metrics(self, results: list) -> dict:
        """Compute metrics with additional mAP@85 extraction."""
        # Call parent compute_metrics
        eval_results = super().compute_metrics(results)
        
        # Extract mAP@85 from the COCO evaluation results
        for metric in self.metrics:
            key = f'{metric}_mAP_85'
            
            # Find the index of 0.85 in IoU thresholds
            iou_85_idx = None
            for idx, iou in enumerate(self.iou_thrs):
                if abs(iou - 0.85) < 1e-5:  # Handle floating point comparison
                    iou_85_idx = idx
                    break
                    
            if iou_85_idx is not None and hasattr(self, '_coco_eval'):
                # Extract AP@85 from precision array
                # Precision dimensions: [T, R, K, A, M] 
                # T=IoU thresholds, R=recall thresholds, K=categories, A=areas, M=max detections
                precision = self._coco_eval.eval['precision']
                
                # Average over recall thresholds and categories for area=all, maxDets=100
                ap_85 = np.mean(precision[iou_85_idx, :, :, 0, 2])
                eval_results[key] = float(ap_85)
                
        return eval_results
```

### Method 3: Direct COCO API Modification

For maximum control, directly modify the COCO evaluation parameters:

```python
from pycocotools.coco import COCO
from pycocotools.cocoeval import COCOeval
import numpy as np

def evaluate_with_custom_iou(gt_file, pred_file, include_85=True):
    """Evaluate with custom IoU thresholds including 0.85."""
    
    # Load ground truth and predictions
    cocoGt = COCO(gt_file)
    cocoDt = cocoGt.loadRes(pred_file)
    
    # Initialize evaluator for instance segmentation
    cocoEval = COCOeval(cocoGt, cocoDt, 'segm')
    
    # Set custom IoU thresholds
    if include_85:
        # Standard COCO thresholds already include 0.85
        cocoEval.params.iouThrs = np.array([0.5, 0.55, 0.6, 0.65, 0.7, 0.75, 0.8, 0.85, 0.9, 0.95])
    
    # Run evaluation
    cocoEval.evaluate()
    cocoEval.accumulate()
    
    # Custom summarize function to extract mAP@85
    def get_ap_at_iou(cocoEval, iou_threshold):
        """Extract AP at specific IoU threshold."""
        iou_idx = None
        for idx, iou in enumerate(cocoEval.params.iouThrs):
            if abs(iou - iou_threshold) < 1e-5:
                iou_idx = idx
                break
                
        if iou_idx is None:
            raise ValueError(f"IoU threshold {iou_threshold} not found")
            
        # Extract precision at this IoU threshold
        precision = cocoEval.eval['precision']
        # Average over all recall thresholds, categories, areas=all, maxDets=100
        ap = np.mean(precision[iou_idx, :, :, 0, 2])
        return ap
    
    # Standard summary
    cocoEval.summarize()
    
    # Extract mAP@85
    ap_85 = get_ap_at_iou(cocoEval, 0.85)
    print(f'Average Precision (AP) @[ IoU=0.85 | area=all | maxDets=100 ] = {ap_85:.3f}')
    
    return cocoEval, ap_85
```

## Modifying the coco_metric.py file in MMDetection

To modify the existing `coco_metric.py` file in MMDetection to support mAP@85, make these key changes:

1. **Update the metric names mapping** (around line 50):
```python
coco_metric_names = {
    'mAP': 0, 'mAP_50': 1, 'mAP_75': 2, 'mAP_85': None,  # Add mAP_85
    'mAP_s': 3, 'mAP_m': 4, 'mAP_l': 5,
    'AR@100': 6, 'AR@300': 7, 'AR@1000': 8,
    'AR_s@1000': 9, 'AR_m@1000': 10, 'AR_l@1000': 11
}
```

2. **Modify the results extraction logic** in `compute_metrics()` (around line 300):
```python
# After the standard COCO summarize
coco_eval.summarize()

# Extract standard metrics
if metric_items is not None:
    for metric_item in metric_items:
        if metric_item == 'mAP_85':
            # Special handling for mAP@85
            iou_85_idx = list(self.iou_thrs).index(0.85) if 0.85 in self.iou_thrs else None
            if iou_85_idx is not None:
                precision = coco_eval.eval['precision']
                val = np.mean(precision[iou_85_idx, :, :, 0, 2])
                eval_results[f'{metric}_{metric_item}'] = float(val)
        elif metric_item in coco_metric_names:
            # Standard metrics
            key = f'{metric}_{metric_item}'
            val = coco_eval.stats[coco_metric_names[metric_item]]
            eval_results[key] = float(f'{round(val, 3)}')
```

## Technical challenges and solutions

### Challenge 1: Floating Point Precision Issues

The standard NumPy linspace can create floating point precision issues where 0.85 might be stored as 0.8499999999999999.

**Solution:**
```python
# Round IoU thresholds to avoid precision issues
self.iou_thrs = np.round(self.iou_thrs, 2)
```

### Challenge 2: Index Alignment

When accessing the precision array, ensure correct index mapping for the 0.85 threshold.

**Solution:**
```python
def find_iou_index(iou_thrs, target_iou):
    """Safely find index of target IoU threshold."""
    for idx, iou in enumerate(iou_thrs):
        if abs(iou - target_iou) < 1e-5:
            return idx
    return None
```

### Challenge 3: Backward Compatibility

Ensure that adding mAP@85 doesn't break existing evaluation pipelines.

**Solution:**
- Use optional parameters with defaults
- Check if 'mAP_85' is requested before computing
- Maintain separate result keys for custom metrics

### Challenge 4: Performance Impact

Additional IoU thresholds increase evaluation time linearly.

**Solution:**
- Only compute mAP@85 when explicitly requested
- Consider caching IoU computations across thresholds
- Use vectorized operations where possible

## Testing and validation methodology

### Unit Test Example

```python
import unittest
import numpy as np
from your_module import CocoMetricWith85

class TestCocoMetric85(unittest.TestCase):
    
    def test_iou_thresholds_include_85(self):
        """Verify 0.85 is in the IoU thresholds."""
        metric = CocoMetricWith85()
        self.assertIn(0.85, metric.iou_thrs)
        
    def test_map_85_lower_than_map_75(self):
        """Verify mAP@85 < mAP@75 for same predictions."""
        # Run evaluation on test data
        results = metric.compute_metrics(test_predictions)
        self.assertLess(results['segm_mAP_85'], results['segm_mAP_75'])
        
    def test_perfect_predictions(self):
        """Test with perfect IoU=1.0 predictions."""
        # Create synthetic perfect predictions
        perfect_preds = create_perfect_predictions()
        results = metric.compute_metrics(perfect_preds)
        # mAP@85 should equal mAP@75 for perfect predictions
        self.assertAlmostEqual(results['segm_mAP_85'], results['segm_mAP_75'])
```

### Validation Protocol

1. **Baseline Comparison**: Verify that mAP@85 values are consistently lower than mAP@75
2. **Cross-validation**: Compare results with manual calculations on small datasets
3. **Edge Cases**: Test with empty predictions, single detections, and perfect matches
4. **Performance Benchmarking**: Measure evaluation time increase (expect ~10% overhead)

### Debugging Tips

```python
# Enable verbose COCO evaluation
import logging
logging.basicConfig(level=logging.DEBUG)

# Print intermediate values
print(f"IoU thresholds: {cocoEval.params.iouThrs}")
print(f"Precision shape: {cocoEval.eval['precision'].shape}")
print(f"AP@85 index: {iou_85_idx}")
```

## Complete configuration example

Here's a complete example for using mAP@85 in your MMDetection project:

```python
# config/custom_mask_rcnn_config.py
_base_ = ['./mask_rcnn_r50_fpn_1x_coco.py']

# Override the evaluator
val_evaluator = dict(
    type='CocoMetricWith85',  # Use custom metric class
    ann_file='data/coco/annotations/instances_val2017.json',
    metric='segm',
    iou_thrs=[0.5, 0.55, 0.6, 0.65, 0.7, 0.75, 0.8, 0.85, 0.9, 0.95],
    metric_items=['mAP', 'mAP_50', 'mAP_75', 'mAP_85'],
    classwise=True,
    prefix='segm'
)

test_evaluator = val_evaluator

# Optional: Add to training log
default_hooks = dict(
    logger=dict(
        type='LoggerHook',
        interval=50,
        log_metric_by_epoch=True,
        # Add mAP_85 to logged metrics
        log_metric_names=['segm_mAP', 'segm_mAP_50', 'segm_mAP_75', 'segm_mAP_85']
    )
)
```

This implementation provides a robust foundation for using stricter IoU thresholds in COCO evaluation, enabling more precise quality assessment for applications requiring high localization accuracy.

