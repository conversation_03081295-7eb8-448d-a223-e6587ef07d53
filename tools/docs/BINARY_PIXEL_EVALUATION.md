# 二分类像素级评价系统

## 概述

本系统实现了房间实例分割的二分类像素级评价，将所有房间类别视为前景，将门窗和背景区域视为背景，提供精确的像素级语义分割性能评价。

## 核心特性

### ✅ 二分类语义分割评价
- **房间类别 (前景)**: 包含所有房间类型 (living room, kitchen, bedroom, 等)
- **背景类别 (背景)**: 包含门窗、未定义区域和真正的背景

### ✅ 完整的像素级指标
- **房间类别指标**: Precision, Recall, F1-Score
- **背景类别指标**: Precision, Recall, F1-Score  
- **平均指标**: 两个类别的平均性能

### ✅ 多种评价模式
- `pure_pixel`: 纯像素级评价
- `instance_aware`: 实例感知像素级评价
- `weighted_pixel`: 加权像素级评价

## 最新评价结果

### 验证集性能 (11张图像)

```
================================================================================
Pure Pixel PIXEL-LEVEL EVALUATION RESULTS
================================================================================

[Overall Metrics - Pure Pixel (Room Class)]
  Precision: 0.9782
  Recall:    0.9599
  F1-Score:  0.9690

[Average Metrics - Both Classes]
  Average Precision: 0.9719
  Average Recall:    0.9706
  Average F1-Score:  0.9712

[Per-Class Metrics - Pure Pixel]
Class                Precision  Recall     F1-Score   Support     
------------------------------------------------------------------------
background           0.9655     0.9813     0.9733     384654      
room                 0.9782     0.9599     0.9690     336242      
================================================================================
```

### 关键指标解析

#### 房间类别指标
- **Precision: 97.82%** - 预测为房间的像素中，实际为房间的比例
- **Recall: 95.99%** - 实际房间像素中，被正确预测为房间的比例
- **F1-Score: 96.90%** - 房间类别的综合指标

#### 背景类别指标
- **Precision: 96.55%** - 预测为背景的像素中，实际为背景的比例
- **Recall: 98.13%** - 实际背景像素中，被正确预测为背景的比例
- **F1-Score: 97.33%** - 背景类别的综合指标

#### 平均指标
- **Average Precision: 97.19%** - 两个类别精确率的平均值
- **Average Recall: 97.06%** - 两个类别召回率的平均值
- **Average F1-Score: 97.12%** - 两个类别F1分数的平均值

## 使用方法

### 方法1: 使用便捷脚本

```bash
# 使用默认参数
./scripts/run_binary_pixel_evaluation.sh

# 自定义参数
./scripts/run_binary_pixel_evaluation.sh \
    --score-thr 0.5 \
    --pixel-mode instance_aware \
    --output output/my_evaluation
```

### 方法2: 直接使用Python脚本

```bash
python tools/inference_with_evaluators.py \
    --config configs/mask2former_config.py \
    --checkpoint checkpoints/hc_rs10_q2_wo_floor_2_0718_v2_best_coco_segm_mAP_85_epoch_539.pth \
    --input /home/<USER>/data/RS10_data/hc_rs10_q2_wo_floor_2_0718_v2/val \
    --output output/binary_evaluation \
    --ann-file /home/<USER>/data/RS10_data/hc_rs10_q2_wo_floor_2_0718_v2/annotations/val.json \
    --score-thr 0.3 \
    --device cuda:0 \
    --evaluators improved_pixel \
    --pixel-mode pure_pixel
```

## 输出文件

### 主要结果文件
- `improved_pixel_metrics/pure_pixel_metrics_summary.json`: 总体指标摘要
- `improved_pixel_metrics/pure_pixel_detailed_results.json`: 每张图像的详细结果
- 可视化结果图像

### JSON结果格式

```json
{
  "overall_metrics": {
    "pure_pixel_avg_precision": 0.9719,
    "pure_pixel_avg_recall": 0.9706,
    "pure_pixel_avg_f1": 0.9712,
    "pure_pixel_room_precision": 0.9782,
    "pure_pixel_room_recall": 0.9599,
    "pure_pixel_room_f1": 0.9690,
    "pure_pixel_background_precision": 0.9655,
    "pure_pixel_background_recall": 0.9813,
    "pure_pixel_background_f1": 0.9733
  },
  "class_metrics": {
    "room": {
      "precision": 0.9782,
      "recall": 0.9599,
      "f1": 0.9690,
      "support": 336242
    },
    "background": {
      "precision": 0.9655,
      "recall": 0.9813,
      "f1": 0.9733,
      "support": 384654
    }
  }
}
```

## 技术实现

### 类别映射
- **房间类别 (ID 0-14)**: living room, kitchen, bedroom, bathroom, balcony, corridor, dining room, study, studio, store room, garden, laundry room, office, basement, garage
- **背景类别 (ID 15-17 + 真正背景)**: undefined, door, window + 背景区域

### 语义图构建
1. **预测语义图**: 房间类别 → 1, 其他区域 → 0
2. **真值语义图**: 房间类别 → 1, 其他区域 → 0
3. **像素级比较**: 逐像素计算TP, FP, FN

### 指标计算
- **房间类别**: 基于 (预测=1, 真值=1) 的像素统计
- **背景类别**: 基于 (预测=0, 真值=0) 的像素统计
- **平均指标**: 两个类别指标的算术平均

## 配置文件

参考 `configs/binary_pixel_evaluation_config.py` 了解详细配置选项。

## 与COCO指标的对比

| 指标类型 | COCO mAP@85% | 像素级 F1-Score |
|----------|--------------|-----------------|
| 实例分割 | 87.64%       | -               |
| 像素级   | -            | 97.12%          |

两种指标互补，提供全面的模型性能评价。
