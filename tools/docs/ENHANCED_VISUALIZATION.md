# 增强可视化功能说明

## 概述

我们对 `inference_with_evaluators.py` 的可视化功能进行了重大改进，现在提供了更加鲜艳、易于区分的实例分割可视化效果。

## 主要改进

### 1. 鲜艳颜色方案
- **预定义鲜艳颜色**：使用20种预定义的高饱和度、高亮度颜色
- **智能颜色生成**：当实例数量超过预定义颜色时，自动生成高饱和度随机颜色
- **颜色格式**：使用BGR格式，完全兼容OpenCV

```python
# 预定义的鲜艳颜色包括：
bright_colors = [
    (0, 255, 255),    # 黄色
    (255, 0, 255),    # 品红色
    (255, 255, 0),    # 青色
    (0, 255, 0),      # 绿色
    (255, 0, 0),      # 蓝色
    (0, 0, 255),      # 红色
    (255, 165, 0),    # 橙色
    # ... 更多颜色
]
```

### 2. 多种可视化模式

#### Basic Mode (基础模式)
- 简单的颜色叠加
- 兼容原有的可视化方式
- 文件后缀：`_basic.png`

#### Enhanced Mode (增强模式) - **推荐**
- 使用鲜艳颜色方案
- 显示实例ID和置信度
- 更好的透明度混合 (alpha=0.6)
- 文件后缀：`_enhanced.png`

#### Detailed Mode (详细模式)
- 包含边界框和轮廓
- 显示详细的标签信息
- 适合调试和分析
- 文件后缀：`_detailed.png`

#### Both Mode (双模式)
- 同时生成增强和详细两种可视化
- 默认模式

### 3. 新增命令行参数

```bash
# 可视化模式选择
--visualization-mode {basic,enhanced,detailed,both}
    # basic: 基础可视化
    # enhanced: 增强可视化（推荐）
    # detailed: 详细可视化（含边界框）
    # both: 同时生成增强和详细版本（默认）

# 禁用可视化（节省时间和空间）
--disable-visualization
```

## 使用示例

### 1. 使用增强可视化模式（推荐）
```bash
python tools/inference_with_evaluators.py \
    --config configs/mask2former_config.py \
    --checkpoint path/to/checkpoint.pth \
    --input path/to/images \
    --output output/results \
    --ann-file path/to/annotations.json \
    --evaluators coco improved_pixel \
    --visualization-mode enhanced
```

### 2. 生成详细可视化（用于调试）
```bash
python tools/inference_with_evaluators.py \
    --config configs/mask2former_config.py \
    --checkpoint path/to/checkpoint.pth \
    --input path/to/images \
    --output output/results \
    --ann-file path/to/annotations.json \
    --evaluators coco \
    --visualization-mode detailed
```

### 3. 禁用可视化（快速评估）
```bash
python tools/inference_with_evaluators.py \
    --config configs/mask2former_config.py \
    --checkpoint path/to/checkpoint.pth \
    --input path/to/images \
    --output output/results \
    --ann-file path/to/annotations.json \
    --evaluators coco improved_pixel \
    --disable-visualization
```

## 输出文件说明

### 可视化文件命名规则
- `{image_name}_enhanced.png`: 增强可视化结果
- `{image_name}_detailed.png`: 详细可视化结果
- `{image_name}_basic.png`: 基础可视化结果
- `{image_name}_no_predictions.png`: 无预测结果时的原图

### 可视化特性对比

| 特性 | Basic | Enhanced | Detailed |
|------|-------|----------|----------|
| 鲜艳颜色 | ❌ | ✅ | ✅ |
| 实例ID显示 | ❌ | ✅ | ✅ |
| 置信度显示 | ❌ | ✅ | ✅ |
| 边界框 | ❌ | ❌ | ✅ |
| 轮廓线 | ❌ | ❌ | ✅ |
| 透明度优化 | ❌ | ✅ | ✅ |

## 技术细节

### 颜色生成算法
1. **预定义颜色池**：20种精心选择的鲜艳颜色
2. **动态生成**：当实例数超过预定义颜色时，使用HSV色彩空间生成高饱和度颜色
3. **颜色参数**：
   - 色调(H)：0-179（随机）
   - 饱和度(S)：200-255（高饱和度）
   - 亮度(V)：200-255（高亮度）

### 可视化渲染
- **透明度混合**：alpha=0.6（overlay），beta=0.4（原图）
- **文本渲染**：使用OpenCV的FONT_HERSHEY_SIMPLEX字体
- **边界框**：2像素宽度的彩色边框
- **轮廓**：2像素宽度的彩色轮廓线

## 性能考虑

- **内存使用**：增强可视化不会显著增加内存使用
- **处理时间**：详细模式会增加约10-20%的处理时间（由于轮廓计算）
- **存储空间**：每种模式生成独立的PNG文件

## 最佳实践

1. **日常使用**：推荐使用 `enhanced` 模式，提供最佳的视觉效果
2. **调试分析**：使用 `detailed` 模式查看边界框和轮廓
3. **批量处理**：使用 `--disable-visualization` 节省时间和存储空间
4. **演示展示**：使用 `both` 模式生成多种版本供选择

## 兼容性

- ✅ 完全向后兼容原有的评估功能
- ✅ 支持所有现有的评估器（COCO、Pixel等）
- ✅ 支持房屋类型分类评估
- ✅ 保持原有的命令行接口
