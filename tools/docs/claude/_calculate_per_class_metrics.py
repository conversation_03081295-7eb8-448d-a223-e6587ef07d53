def _calculate_per_class_metrics(self):
    """计算每个类别的精确率和召回率"""
    class_stats = {}
    
    # 收集每个类别的TP, FP, FN
    for result in self.per_image_results:
        if 'matched_pairs' not in result:
            continue
            
        # 统计每个类别的匹配情况
        for pair in result.get('matched_pairs', []):
            cat_id = pair['category_id']
            if cat_id not in class_stats:
                class_stats[cat_id] = {'tp': 0, 'fp': 0, 'fn': 0}
            class_stats[cat_id]['tp'] += 1
    
    # 从原始预测和GT中统计FP和FN
    for img_result in self.per_image_results:
        img_id = img_result.get('img_id')
        if not img_id:
            continue
            
        # 获取该图像的预测和GT
        img_predictions = [p for p in self.predictions if p['image_id'] == img_id]
        ann_ids = self.coco_gt.getAnnIds(imgIds=[img_id])
        gt_anns = self.coco_gt.loadAnns(ann_ids)
        
        # 统计每个类别的预测和GT数量
        pred_by_class = {}
        gt_by_class = {}
        
        for pred in img_predictions:
            cat_id = pred['category_id']
            pred_by_class[cat_id] = pred_by_class.get(cat_id, 0) + 1
        
        for gt in gt_anns:
            cat_id = gt['category_id']
            gt_by_class[cat_id] = gt_by_class.get(cat_id, 0) + 1
        
        # 计算每个类别的FP和FN
        all_cat_ids = set(pred_by_class.keys()) | set(gt_by_class.keys())
        for cat_id in all_cat_ids:
            if cat_id not in class_stats:
                class_stats[cat_id] = {'tp': 0, 'fp': 0, 'fn': 0}
            
            pred_count = pred_by_class.get(cat_id, 0)
            gt_count = gt_by_class.get(cat_id, 0)
            tp_count = len([p for p in img_result.get('matched_pairs', []) 
                          if p['category_id'] == cat_id])
            
            class_stats[cat_id]['fp'] += max(0, pred_count - tp_count)
            class_stats[cat_id]['fn'] += max(0, gt_count - tp_count)
    
    # 计算每个类别的指标
    class_metrics = {}
    for cat_id, stats in class_stats.items():
        tp = stats['tp']
        fp = stats['fp']
        fn = stats['fn']
        
        precision = tp / (tp + fp) if (tp + fp) > 0 else 0.0
        recall = tp / (tp + fn) if (tp + fn) > 0 else 0.0
        f1 = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0.0
        
        # 获取类别名称
        cat_info = self.coco_gt.loadCats([cat_id])[0] if cat_id in self.coco_gt.getCatIds() else {'name': f'class_{cat_id}'}
        
        class_metrics[cat_id] = {
            'name': cat_info['name'],
            'precision': precision,
            'recall': recall,
            'f1_score': f1,
            'tp': tp,
            'fp': fp,
            'fn': fn,
            'support': tp + fn  # GT数量
        }
    
    # 打印类别级结果
    log_print("\n📊 Per-Class Metrics @ IoU={}:".format(self.iou_threshold))
    log_print("  {:>20} {:>10} {:>10} {:>10} {:>8}".format(
        "Class", "Precision", "Recall", "F1-Score", "Support"))
    log_print("  " + "-" * 70)
    
    for cat_id in sorted(class_metrics.keys()):
        metrics = class_metrics[cat_id]
        log_print("  {:>20} {:>10.4f} {:>10.4f} {:>10.4f} {:>8d}".format(
            metrics['name'],
            metrics['precision'],
            metrics['recall'],
            metrics['f1_score'],
            metrics['support']
        ))
    
    return class_metrics