def calculate_per_image_metrics_improved(self, predictions, img_id, filename=None):
    """改进版：准确计算单张图像在IoU=85%时的精确率和召回率"""
    # 获取ground truth
    ann_ids = self.coco_gt.getAnnIds(imgIds=[img_id])
    gt_anns = self.coco_gt.loadAnns(ann_ids)
    
    filename_info = f" ({filename})" if filename else ""
    log_print(f"  📊 Image {img_id}{filename_info}: {len(predictions)} predictions, {len(gt_anns)} ground truth")
    
    # 特殊情况处理
    if len(predictions) == 0 and len(gt_anns) == 0:
        return {
            'precision': 1.0, 
            'recall': 1.0, 
            'tp': 0, 
            'fp': 0, 
            'fn': 0,
            'matched_pairs': []
        }
    elif len(predictions) == 0:
        return {
            'precision': 0.0, 
            'recall': 0.0, 
            'tp': 0, 
            'fp': 0, 
            'fn': len(gt_anns),
            'matched_pairs': []
        }
    elif len(gt_anns) == 0:
        return {
            'precision': 0.0, 
            'recall': 0.0, 
            'tp': 0, 
            'fp': len(predictions), 
            'fn': 0,
            'matched_pairs': []
        }
    
    # 准备masks
    pred_masks = []
    gt_masks = []
    
    # 提取预测masks
    for pred in predictions:
        if 'segmentation' in pred:
            # RLE格式
            if isinstance(pred['segmentation'], dict):
                mask = mask_util.decode(pred['segmentation'])
            else:
                # 多边形格式，需要转换
                h, w = self.coco_gt.imgs[img_id]['height'], self.coco_gt.imgs[img_id]['width']
                mask = self._poly_to_mask(pred['segmentation'], h, w)
            pred_masks.append({
                'mask': mask,
                'score': pred['score'],
                'category_id': pred['category_id']
            })
    
    # 提取GT masks
    for gt in gt_anns:
        if 'segmentation' in gt:
            if isinstance(gt['segmentation'], dict):
                mask = mask_util.decode(gt['segmentation'])
            else:
                h, w = self.coco_gt.imgs[img_id]['height'], self.coco_gt.imgs[img_id]['width']
                mask = self._poly_to_mask(gt['segmentation'], h, w)
            gt_masks.append({
                'mask': mask,
                'category_id': gt['category_id']
            })
    
    # 按分数排序预测
    pred_masks.sort(key=lambda x: x['score'], reverse=True)
    
    # 计算IoU矩阵
    iou_matrix = np.zeros((len(pred_masks), len(gt_masks)))
    for i, pred in enumerate(pred_masks):
        for j, gt in enumerate(gt_masks):
            # 只计算相同类别的IoU
            if pred['category_id'] == gt['category_id']:
                iou = self._compute_mask_iou(pred['mask'], gt['mask'])
                iou_matrix[i, j] = iou
    
    # 贪心匹配：按预测分数从高到低匹配
    tp = 0
    fp = 0
    matched_gt = set()
    matched_pairs = []
    
    for i, pred in enumerate(pred_masks):
        matched = False
        best_iou = 0
        best_j = -1
        
        # 找到IoU最高且超过阈值的未匹配GT
        for j in range(len(gt_masks)):
            if j not in matched_gt and iou_matrix[i, j] >= self.iou_threshold:
                if iou_matrix[i, j] > best_iou:
                    best_iou = iou_matrix[i, j]
                    best_j = j
        
        if best_j >= 0:
            # 找到匹配
            tp += 1
            matched_gt.add(best_j)
            matched = True
            matched_pairs.append({
                'pred_idx': i,
                'gt_idx': best_j,
                'iou': best_iou,
                'category_id': pred['category_id']
            })
        else:
            # 没有匹配，是false positive
            fp += 1
    
    # 未匹配的GT是false negatives
    fn = len(gt_masks) - len(matched_gt)
    
    # 计算精确率和召回率
    precision = tp / (tp + fp) if (tp + fp) > 0 else 0.0
    recall = tp / (tp + fn) if (tp + fn) > 0 else 0.0
    
    log_print(f"  📊 Image {img_id} metrics @ IoU={self.iou_threshold}: ")
    log_print(f"     TP={tp}, FP={fp}, FN={fn}")
    log_print(f"     Precision={precision:.4f}, Recall={recall:.4f}")
    
    return {
        'precision': precision,
        'recall': recall,
        'tp': tp,
        'fp': fp,
        'fn': fn,
        'matched_pairs': matched_pairs
    }

def _compute_mask_iou(self, mask1, mask2):
    """计算两个mask的IoU"""
    intersection = np.logical_and(mask1, mask2).sum()
    union = np.logical_or(mask1, mask2).sum()
    if union == 0:
        return 0.0
    return intersection / union

def _poly_to_mask(self, polygons, height, width):
    """将多边形转换为mask"""
    import pycocotools.mask as mask_util
    rles = mask_util.frPyObjects(polygons, height, width)
    rle = mask_util.merge(rles)
    return mask_util.decode(rle)

def evaluate_improved(self):
    """改进的评价方法，提供更准确的整体指标"""
    if not self.per_image_results:
        log_print("Warning: No per-image results to evaluate")
        return {}
    
    # 计算整体的TP, FP, FN
    total_tp = sum(result['tp'] for result in self.per_image_results)
    total_fp = sum(result['fp'] for result in self.per_image_results)
    total_fn = sum(result['fn'] for result in self.per_image_results)
    
    # 计算整体精确率和召回率（微平均）
    overall_precision = total_tp / (total_tp + total_fp) if (total_tp + total_fp) > 0 else 0.0
    overall_recall = total_tp / (total_tp + total_fn) if (total_tp + total_fn) > 0 else 0.0
    overall_f1 = 2 * overall_precision * overall_recall / (overall_precision + overall_recall) \
                 if (overall_precision + overall_recall) > 0 else 0.0
    
    # 计算每张图像的平均指标（宏平均）
    precisions = [result['precision'] for result in self.per_image_results]
    recalls = [result['recall'] for result in self.per_image_results]
    f1_scores = [2 * p * r / (p + r) if (p + r) > 0 else 0.0 
                 for p, r in zip(precisions, recalls)]
    
    macro_avg_precision = np.mean(precisions) if precisions else 0.0
    macro_avg_recall = np.mean(recalls) if recalls else 0.0
    macro_avg_f1 = np.mean(f1_scores) if f1_scores else 0.0
    
    # 计算每个类别的指标
    class_metrics = self._calculate_per_class_metrics()
    
    metrics = {
        # 微平均指标（基于总体TP/FP/FN）
        f'precision_micro@{int(self.iou_threshold*100)}': overall_precision,
        f'recall_micro@{int(self.iou_threshold*100)}': overall_recall,
        f'f1_micro@{int(self.iou_threshold*100)}': overall_f1,
        
        # 宏平均指标（每张图像的平均）
        f'precision_macro@{int(self.iou_threshold*100)}': macro_avg_precision,
        f'recall_macro@{int(self.iou_threshold*100)}': macro_avg_recall,
        f'f1_macro@{int(self.iou_threshold*100)}': macro_avg_f1,
        
        # 统计信息
        'total_tp': total_tp,
        'total_fp': total_fp,
        'total_fn': total_fn,
        'num_images': len(self.per_image_results),
        
        # 类别级指标
        'per_class_metrics': class_metrics
    }
    
    # 保存详细结果
    self._save_detailed_results(metrics)
    
    log_print(f"\n📊 Evaluation Results @ IoU={self.iou_threshold}:")
    log_print(f"  Micro-averaged (Overall):")
    log_print(f"    Precision: {overall_precision:.4f}")
    log_print(f"    Recall: {overall_recall:.4f}")
    log_print(f"    F1-Score: {overall_f1:.4f}")
    log_print(f"  Macro-averaged (Per-image):")
    log_print(f"    Precision: {macro_avg_precision:.4f}")
    log_print(f"    Recall: {macro_avg_recall:.4f}")
    log_print(f"    F1-Score: {macro_avg_f1:.4f}")
    
    return metrics