# 1. 批量处理优化
class BatchInferenceCocoEvaluator(InferenceCocoEvaluator):
    """支持批量处理的评价器，减少内存占用"""
    
    def __init__(self, *args, batch_size=100, **kwargs):
        super().__init__(*args, **kwargs)
        self.batch_size = batch_size
        self.current_batch_results = []
        self.batch_count = 0
        
    def add_prediction(self, img_id, result, filename=None):
        """添加预测到批次"""
        # 计算单张图像的结果
        per_image_metrics = self.calculate_per_image_metrics_improved(
            self._extract_predictions(img_id, result), img_id, filename
        )
        self.current_batch_results.append(per_image_metrics)
        
        # 当批次满时，处理并释放内存
        if len(self.current_batch_results) >= self.batch_size:
            self._process_batch()
            
    def _process_batch(self):
        """处理当前批次并更新统计"""
        batch_tp = sum(r['tp'] for r in self.current_batch_results)
        batch_fp = sum(r['fp'] for r in self.current_batch_results)
        batch_fn = sum(r['fn'] for r in self.current_batch_results)
        
        # 更新总体统计
        self.total_stats['tp'] += batch_tp
        self.total_stats['fp'] += batch_fp
        self.total_stats['fn'] += batch_fn
        
        # 保存批次摘要而不是详细结果
        self.batch_summaries.append({
            'batch_id': self.batch_count,
            'num_images': len(self.current_batch_results),
            'tp': batch_tp,
            'fp': batch_fp,
            'fn': batch_fn
        })
        
        # 清空当前批次
        self.current_batch_results = []
        self.batch_count += 1
        
        log_print(f"  ✓ Processed batch {self.batch_count}, Total images: {self.total_stats['num_images']}")

# 2. 稀疏mask处理优化
def optimize_mask_computation(mask1, mask2):
    """优化的mask IoU计算，特别适合稀疏mask"""
    # 获取非零区域的边界框
    y1, x1 = np.where(mask1)
    y2, x2 = np.where(mask2)
    
    if len(y1) == 0 or len(y2) == 0:
        return 0.0
    
    # 计算边界框的交集
    min_y = max(y1.min(), y2.min())
    max_y = min(y1.max(), y2.max())
    min_x = max(x1.min(), x2.min())
    max_x = min(x1.max(), x2.max())
    
    # 如果边界框不相交，IoU为0
    if min_y > max_y or min_x > max_x:
        return 0.0
    
    # 只在交集区域计算IoU
    roi_mask1 = mask1[min_y:max_y+1, min_x:max_x+1]
    roi_mask2 = mask2[min_y:max_y+1, min_x:max_x+1]
    
    intersection = np.logical_and(roi_mask1, roi_mask2).sum()
    union = mask1.sum() + mask2.sum() - intersection
    
    return intersection / union if union > 0 else 0.0

# 3. 并行处理优化
from concurrent.futures import ThreadPoolExecutor, as_completed
import multiprocessing as mp

def parallel_iou_computation(pred_masks, gt_masks, iou_threshold=0.85, n_workers=None):
    """并行计算IoU矩阵"""
    if n_workers is None:
        n_workers = min(mp.cpu_count(), 8)
    
    n_pred = len(pred_masks)
    n_gt = len(gt_masks)
    iou_matrix = np.zeros((n_pred, n_gt))
    
    def compute_iou_row(i, pred):
        row_ious = []
        for j, gt in enumerate(gt_masks):
            if pred['category_id'] == gt['category_id']:
                iou = optimize_mask_computation(pred['mask'], gt['mask'])
                if iou >= iou_threshold:  # 只存储超过阈值的IoU
                    row_ious.append((j, iou))
        return i, row_ious
    
    # 使用线程池并行计算
    with ThreadPoolExecutor(max_workers=n_workers) as executor:
        futures = {executor.submit(compute_iou_row, i, pred): i 
                  for i, pred in enumerate(pred_masks)}
        
        for future in as_completed(futures):
            i, row_ious = future.result()
            for j, iou in row_ious:
                iou_matrix[i, j] = iou
    
    return iou_matrix

# 4. 增量式评价
class IncrementalCocoEvaluator(InferenceCocoEvaluator):
    """支持增量式评价，避免存储所有预测结果"""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.running_stats = {
            'tp': 0, 'fp': 0, 'fn': 0,
            'precision_sum': 0.0, 'recall_sum': 0.0,
            'num_images': 0
        }
        # 不存储所有predictions
        self.predictions = None  
        
    def add_prediction(self, img_id, result, filename=None):
        """增量更新统计信息"""
        predictions = self._extract_predictions(img_id, result)
        metrics = self.calculate_per_image_metrics_improved(
            predictions, img_id, filename
        )
        
        # 更新运行统计
        self.running_stats['tp'] += metrics['tp']
        self.running_stats['fp'] += metrics['fp']
        self.running_stats['fn'] += metrics['fn']
        self.running_stats['precision_sum'] += metrics['precision']
        self.running_stats['recall_sum'] += metrics['recall']
        self.running_stats['num_images'] += 1
        
        # 可选：定期保存中间结果
        if self.running_stats['num_images'] % 100 == 0:
            self._save_intermediate_results()
    
    def evaluate(self):
        """基于运行统计计算最终结果"""
        n = self.running_stats['num_images']
        if n == 0:
            return {}
        
        # 微平均
        tp = self.running_stats['tp']
        fp = self.running_stats['fp']
        fn = self.running_stats['fn']
        
        micro_precision = tp / (tp + fp) if (tp + fp) > 0 else 0.0
        micro_recall = tp / (tp + fn) if (tp + fn) > 0 else 0.0
        micro_f1 = 2 * micro_precision * micro_recall / (micro_precision + micro_recall) \
                   if (micro_precision + micro_recall) > 0 else 0.0
        
        # 宏平均
        macro_precision = self.running_stats['precision_sum'] / n
        macro_recall = self.running_stats['recall_sum'] / n
        macro_f1 = 2 * macro_precision * macro_recall / (macro_precision + macro_recall) \
                   if (macro_precision + macro_recall) > 0 else 0.0
        
        return {
            f'precision_micro@{int(self.iou_threshold*100)}': micro_precision,
            f'recall_micro@{int(self.iou_threshold*100)}': micro_recall,
            f'f1_micro@{int(self.iou_threshold*100)}': micro_f1,
            f'precision_macro@{int(self.iou_threshold*100)}': macro_precision,
            f'recall_macro@{int(self.iou_threshold*100)}': macro_recall,
            f'f1_macro@{int(self.iou_threshold*100)}': macro_f1,
            'total_images': n
        }