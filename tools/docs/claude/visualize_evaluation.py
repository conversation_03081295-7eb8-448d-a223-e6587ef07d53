# 1. 评价器配置类
class EvaluatorConfig:
    """评价器配置管理"""
    def __init__(self):
        self.iou_thresholds = [0.5, 0.75, 0.85, 0.95]  # 多个IoU阈值
        self.score_threshold = 0.3
        self.max_dets = [1, 10, 100]
        self.area_ranges = {
            'all': [0, 1e5**2],
            'small': [0, 32**2],
            'medium': [32**2, 96**2],
            'large': [96**2, 1e5**2]
        }
        self.use_parallel = True
        self.n_workers = 4
        self.save_visualizations = True
        self.visualization_top_k = 10  # 可视化前K个最差的结果

# 2. 结果可视化
def visualize_evaluation_results(evaluator, output_dir, top_k_worst=10):
    """可视化评价结果，特别是错误案例"""
    import matplotlib.pyplot as plt
    import seaborn as sns
    
    # 创建可视化目录
    vis_dir = os.path.join(output_dir, 'visualizations')
    os.makedirs(vis_dir, exist_ok=True)
    
    # 1. 绘制混淆矩阵热图（如果有类别级结果）
    if hasattr(evaluator, 'class_confusion_matrix'):
        plt.figure(figsize=(12, 10))
        sns.heatmap(evaluator.class_confusion_matrix, 
                   annot=True, fmt='d', cmap='Blues',
                   xticklabels=evaluator.class_names,
                   yticklabels=evaluator.class_names)
        plt.title(f'Confusion Matrix @ IoU={evaluator.iou_threshold}')
        plt.xlabel('Predicted')
        plt.ylabel('Ground Truth')
        plt.tight_layout()
        plt.savefig(os.path.join(vis_dir, 'confusion_matrix.png'))
        plt.close()
    
    # 2. 绘制类别性能条形图
    if hasattr(evaluator, 'per_class_metrics'):
        class_metrics = evaluator.per_class_metrics
        
        # 准备数据
        classes = []
        precisions = []
        recalls = []
        f1_scores = []
        
        for cat_id, metrics in sorted(class_metrics.items()):
            classes.append(metrics['name'])
            precisions.append(metrics['precision'])
            recalls.append(metrics['recall'])
            f1_scores.append(metrics['f1_score'])
        
        # 创建子图
        fig, (ax1, ax2, ax3) = plt.subplots(3, 1, figsize=(12, 10))
        
        # Precision
        ax1.barh(classes, precisions, color='blue', alpha=0.7)
        ax1.set_xlabel('Precision')
        ax1.set_xlim(0, 1)
        ax1.set_title(f'Per-Class Precision @ IoU={evaluator.iou_threshold}')
        
        # Recall
        ax2.barh(classes, recalls, color='green', alpha=0.7)
        ax2.set_xlabel('Recall')
        ax2.set_xlim(0, 1)
        ax2.set_title(f'Per-Class Recall @ IoU={evaluator.iou_threshold}')
        
        # F1-Score
        ax3.barh(classes, f1_scores, color='red', alpha=0.7)
        ax3.set_xlabel('F1-Score')
        ax3.set_xlim(0, 1)
        ax3.set_title(f'Per-Class F1-Score @ IoU={evaluator.iou_threshold}')
        
        plt.tight_layout()
        plt.savefig(os.path.join(vis_dir, 'per_class_metrics.png'))
        plt.close()
    
    # 3. 找出最差的预测案例
    if hasattr(evaluator, 'per_image_results'):
        # 按F1分数排序，找出最差的
        image_f1_scores = []
        for result in evaluator.per_image_results:
            p = result['precision']
            r = result['recall']
            f1 = 2 * p * r / (p + r) if (p + r) > 0 else 0.0
            image_f1_scores.append({
                'img_id': result.get('img_id', -1),
                'filename': result.get('filename', 'unknown'),
                'f1_score': f1,
                'precision': p,
                'recall': r,
                'tp': result['tp'],
                'fp': result['fp'],
                'fn': result['fn']
            })
        
        # 排序并获取最差的案例
        image_f1_scores.sort(key=lambda x: x['f1_score'])
        worst_cases = image_f1_scores[:top_k_worst]
        
        # 保存最差案例的详细信息
        worst_cases_file = os.path.join(vis_dir, 'worst_cases.json')
        with open(worst_cases_file, 'w') as f:
            json.dump(worst_cases, f, indent=2)
        
        log_print(f"\n📊 Top {top_k_worst} Worst Performing Images:")
        log_print("  {:>10} {:>30} {:>10} {:>10} {:>10}".format(
            "Image ID", "Filename", "F1-Score", "Precision", "Recall"))
        log_print("  " + "-" * 80)
        for case in worst_cases:
            log_print("  {:>10} {:>30} {:>10.4f} {:>10.4f} {:>10.4f}".format(
                case['img_id'], 
                case['filename'][-30:],  # 截断长文件名
                case['f1_score'],
                case['precision'],
                case['recall']
            ))

# 3. 详细的错误分析
def analyze_errors(evaluator, output_dir):
    """分析预测错误的模式"""
    error_analysis = {
        'missed_detections': [],  # FN案例
        'false_alarms': [],       # FP案例
        'class_confusions': {},   # 类别混淆
        'size_errors': {          # 按大小分类的错误
            'small': {'fp': 0, 'fn': 0},
            'medium': {'fp': 0, 'fn': 0},
            'large': {'fp': 0, 'fn': 0}
        }
    }
    
    # 分析每个图像的错误
    for img_result in evaluator.per_image_results:
        if 'error_details' in img_result:
            # 分析漏检（FN）
            for fn_case in img_result['error_details']['false_negatives']:
                error_analysis['missed_detections'].append({
                    'img_id': img_result['img_id'],
                    'category': fn_case['category'],
                    'area': fn_case['area'],
                    'reason': 'missed_detection'
                })
            
            # 分析误检（FP）
            for fp_case in img_result['error_details']['false_positives']:
                error_analysis['false_alarms'].append({
                    'img_id': img_result['img_id'],
                    'predicted_category': fp_case['category'],
                    'confidence': fp_case['score'],
                    'reason': 'false_alarm'
                })
    
    # 保存错误分析报告
    report_file = os.path.join(output_dir, 'error_analysis_report.json')
    with open(report_file, 'w') as f:
        json.dump(error_analysis, f, indent=2)
    
    # 生成错误分析摘要
    log_print("\n📊 Error Analysis Summary:")
    log_print(f"  Total Missed Detections (FN): {len(error_analysis['missed_detections'])}")
    log_print(f"  Total False Alarms (FP): {len(error_analysis['false_alarms'])}")
    
    # 按类别统计错误
    fn_by_class = {}
    fp_by_class = {}
    
    for fn in error_analysis['missed_detections']:
        cat = fn['category']
        fn_by_class[cat] = fn_by_class.get(cat, 0) + 1
    
    for fp in error_analysis['false_alarms']:
        cat = fp['predicted_category']
        fp_by_class[cat] = fp_by_class.get(cat, 0) + 1
    
    log_print("\n  Most Frequently Missed Classes:")
    for cat, count in sorted(fn_by_class.items(), key=lambda x: x[1], reverse=True)[:5]:
        log_print(f"    {cat}: {count} times")
    
    log_print("\n  Most Frequent False Alarm Classes:")
    for cat, count in sorted(fp_by_class.items(), key=lambda x: x[1], reverse=True)[:5]:
        log_print(f"    {cat}: {count} times")