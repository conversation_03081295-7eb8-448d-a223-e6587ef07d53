# 点云到图像投影处理文档

## 🎯 输出文件说明（重要）

当您收到处理结果时，会得到三种类型的文件：

### 📁 灰度掩码文件 (`*_mask.png`)
- **格式**：256×256 PNG图像，8位灰度
- **内容**：实例分割结果，每个实例有唯一的灰度值ID
- **背景**：值为0（黑色）
- **实例**：值为1, 2, 3, ...（不同灰度）
- **用途**：这是您需要的主要结果，包含所有检测到的实例

### 🌈 RGB可视化文件 (`*_rgb.png`)
- **格式**：256×256 PNG图像，24位彩色
- **内容**：将灰度掩码转换为彩色显示，便于人眼查看
- **背景**：白色
- **实例**：不同颜色（红、绿、蓝、黄等）
- **用途**：仅用于可视化检查，不用于算法处理

### 📋 映射参数文件 (`*_mapping.txt`)
- **格式**：纯文本文件，7行内容
- **内容**：包含坐标系转换所需的关键参数（已预计算好）
- **用途**：当您需要将256×256像素坐标转换回世界坐标时使用

#### mapping.txt文件格式
```
cuishanlantian_res_uf_RS10_4          # 第1行：场景名称
0.018040195107460022                   # 第2行：旋转角度（弧度，已根据墙体方向计算好）
-10.544521 -11.521788                  # 第3行：训练ROI最小坐标 (x_min y_min)
2.074529 1.944495                      # 第4行：训练ROI最大坐标 (x_max y_max)
0.050000                               # 第5行：像素分辨率（米/像素）
12.800000 13.200000                   # 第6行：ROI尺寸 (宽度 高度，单位：米)
256 256                                # 第7行：图像尺寸 (宽度 高度，像素)
-9.044521 -10.021788 1.5 3.574529 2.944495 3.5  # 第8行：原始pointcloud_roi (min_x min_y min_z max_x max_y max_z)
```

#### 参数含义
- **场景名称**：标识这是哪个场景的处理结果
- **旋转角度**：已根据墙体方向计算好的主要角度（弧度制），直接使用即可
- **ROI边界**：训练时使用的感兴趣区域边界（世界坐标，单位：米）
- **像素分辨率**：一个像素代表多少米（方便快速计算距离）
- **ROI尺寸**：感兴趣区域的实际物理尺寸（宽度×高度，单位：米）
- **图像尺寸**：生成的密度图像素尺寸（支持灵活调整）
- **原始pointcloud_roi**：从floorplan.json中读取的6D边界框，用于点云过滤

## 🔧 核心C++代码实现

### 1. 读取mapping.txt参数

```cpp
#include <iostream>
#include <fstream>
#include <string>
#include <cmath>
#include <vector>

struct MappingParams {
    std::string scene_name;
    float rotation_angle;        // 已计算好的旋转角度（弧度）
    float roi_min_x, roi_min_y;  // ROI最小坐标
    float roi_max_x, roi_max_y;  // ROI最大坐标
    float pixel_resolution;      // 米/像素
    float roi_width, roi_height; // ROI尺寸（米）
    int image_width, image_height; // 图像尺寸（像素）
    std::vector<float> pointcloud_roi; // 原始pointcloud_roi [min_x, min_y, min_z, max_x, max_y, max_z]
};

MappingParams loadMapping(const std::string& mapping_file) {
    MappingParams params;
    std::ifstream file(mapping_file);

    std::getline(file, params.scene_name);
    file >> params.rotation_angle;
    file >> params.roi_min_x >> params.roi_min_y;
    file >> params.roi_max_x >> params.roi_max_y;
    file >> params.pixel_resolution;
    file >> params.roi_width >> params.roi_height;
    file >> params.image_width >> params.image_height;

    // 读取原始pointcloud_roi（6个值）
    params.pointcloud_roi.resize(6);
    for (int i = 0; i < 6; i++) {
        file >> params.pointcloud_roi[i];
    }

    std::cout << "加载mapping参数完成:" << std::endl;
    std::cout << "  场景: " << params.scene_name << std::endl;
    std::cout << "  图像尺寸: " << params.image_width << "×" << params.image_height << std::endl;
    std::cout << "  像素分辨率: " << params.pixel_resolution << " 米/像素" << std::endl;
    std::cout << "  旋转角度: " << params.rotation_angle << " 弧度 ("
              << params.rotation_angle * 180.0 / CV_PI << "°)" << std::endl;

    return params;
}
```

### 2. 完整的点云预处理流程（复现Python逻辑）

```cpp
#include <pcl/point_cloud.h>
#include <pcl/point_types.h>
#include <opencv2/opencv.hpp>
#include <map>
#include <vector>

// 完整的预处理类，复现generate_coco_hc_0722.py的逻辑
class PointCloudPreprocessor {
private:
    MappingParams params;
    int image_width;
    int image_height;

public:
    PointCloudPreprocessor(const MappingParams& mapping_params, int width = 256, int height = 256)
        : params(mapping_params), image_width(width), image_height(height) {}

    // 步骤1：点云过滤（复现generate_coco_hc_0722.py第247-250行）
    std::vector<cv::Point2f> filterPointCloud(const pcl::PointCloud<pcl::PointXYZ>::Ptr& cloud,
                                              const std::vector<float>& pointcloud_roi) {
        std::vector<cv::Point2f> filtered_points;

        // 从pointcloud_roi提取边界（从floorplan.json读取的原始boundingBox）
        float min_x = pointcloud_roi[0], min_y = pointcloud_roi[1], min_z = pointcloud_roi[2];
        float max_x = pointcloud_roi[3], max_y = pointcloud_roi[4], max_z = pointcloud_roi[5];

        for (const auto& point : cloud->points) {
            // 复现Python中的过滤条件：
            // (las.z > pointcloud_roi[2] + 0.25) & (las.z < pointcloud_roi[5] - 0.25)
            // & (las.x > pointcloud_roi[0]) & (las.x < pointcloud_roi[3])
            // & (las.y > pointcloud_roi[1]) & (las.y < pointcloud_roi[4])
            if (point.z > (min_z + 0.25f) && point.z < (max_z - 0.25f) &&
                point.x > min_x && point.x < max_x &&
                point.y > min_y && point.y < max_y) {
                filtered_points.emplace_back(point.x, point.y);
            }
        }

        std::cout << "过滤后点云数量: " << filtered_points.size() << std::endl;
        return filtered_points;
    }

    // 步骤2：点云旋转（复现generate_coco_hc_0722.py第252-258行）
    std::vector<cv::Point2f> rotatePointCloud(const std::vector<cv::Point2f>& points) {
        std::vector<cv::Point2f> rotated_points;

        // 复现Python中的旋转逻辑：
        // rotation_matrix = cv2.getRotationMatrix2D((0, 0), np.degrees(slope_angle), 1)
        // rotated_point_cloud = np.dot(np.column_stack((filtered_point_cloud, np.ones(...))), rotation_matrix.T)

        // 注意：Python中使用degrees，这里需要转换
        float angle_degrees = params.rotation_angle * 180.0f / CV_PI;
        cv::Mat rotation_matrix = cv::getRotationMatrix2D(cv::Point2f(0, 0), angle_degrees, 1.0);

        for (const auto& point : points) {
            // 构建齐次坐标 [x, y, 1]
            cv::Mat point_homogeneous = (cv::Mat_<float>(3, 1) << point.x, point.y, 1.0f);

            // 应用旋转变换
            cv::Mat rotated = rotation_matrix * point_homogeneous;

            rotated_points.emplace_back(rotated.at<float>(0, 0), rotated.at<float>(1, 0));
        }

        std::cout << "旋转后点云数量: " << rotated_points.size() << std::endl;
        return rotated_points;
    }

    // 步骤3：生成密度图（完全复现hc_utils.py的generate_density函数）
    cv::Mat generateDensityMap(const std::vector<cv::Point2f>& points) {
        // 复现Python中的逻辑：
        // coordinates = np.round((ps[:, :2] - min_coords) / (max_coords - min_coords) * image_res)

        cv::Mat density = cv::Mat::zeros(image_height, image_width, CV_32F);

        // 计算ROI尺寸
        float roi_width = params.roi_max_x - params.roi_min_x;
        float roi_height = params.roi_max_y - params.roi_min_y;

        // 用于统计每个像素的点数
        std::map<std::pair<int, int>, int> pixel_counts;

        for (const auto& point : points) {
            // 归一化到[0,1]范围
            float norm_x = (point.x - params.roi_min_x) / roi_width;
            float norm_y = (point.y - params.roi_min_y) / roi_height;

            // 转换为像素坐标并四舍五入（复现np.round）
            int pixel_x = static_cast<int>(std::round(norm_x * image_width));
            int pixel_y = static_cast<int>(std::round(norm_y * image_height));

            // 边界检查（复现np.minimum(np.maximum(...))）
            pixel_x = std::max(0, std::min(pixel_x, image_width - 1));
            pixel_y = std::max(0, std::min(pixel_y, image_height - 1));

            // 统计每个像素的点数
            pixel_counts[{pixel_x, pixel_y}]++;
        }

        // 将计数填入密度图
        float max_count = 0;
        for (const auto& pair : pixel_counts) {
            int x = pair.first.first;
            int y = pair.first.second;
            int count = pair.second;
            density.at<float>(y, x) = static_cast<float>(count);
            max_count = std::max(max_count, static_cast<float>(count));
        }

        // 归一化到[0,1]（复现density = density / np.max(density)）
        if (max_count > 0) {
            density /= max_count;
        }

        // 应用非线性映射（复现Python中的mask操作）
        // mask_mid = (density >= 0.001) & (density <= 0.04)
        // mask_high = density > 0.04
        // density[mask_mid] *= 25
        // density[mask_high] = 1.0
        for (int y = 0; y < image_height; y++) {
            for (int x = 0; x < image_width; x++) {
                float& val = density.at<float>(y, x);
                if (val >= 0.001f && val <= 0.04f) {
                    val *= 25.0f;
                } else if (val > 0.04f) {
                    val = 1.0f;
                }
            }
        }

        std::cout << "生成密度图完成: " << image_width << "×" << image_height << std::endl;
        return density;
    }

    // 完整的处理流程
    cv::Mat processPointCloud(const pcl::PointCloud<pcl::PointXYZ>::Ptr& cloud) {
        // 使用mapping.txt中的pointcloud_roi进行处理
        return processPointCloud(cloud, params.pointcloud_roi);
    }

    cv::Mat processPointCloud(const pcl::PointCloud<pcl::PointXYZ>::Ptr& cloud,
                             const std::vector<float>& pointcloud_roi) {
        // 步骤1：过滤点云
        auto filtered_points = filterPointCloud(cloud, pointcloud_roi);

        // 步骤2：旋转点云
        auto rotated_points = rotatePointCloud(filtered_points);

        // 步骤3：生成密度图
        auto density_map = generateDensityMap(rotated_points);

        return density_map;
    }

    // 保存为8位PNG（复现common_utils.py的export_density函数）
    void saveDensityMap(const cv::Mat& density_map, const std::string& output_path) {
        // 复现Python中的逻辑：
        // density_uint8 = (density_map * 255).astype(np.uint8)
        cv::Mat density_uint8;
        density_map.convertTo(density_uint8, CV_8U, 255.0);

        cv::imwrite(output_path, density_uint8);
        std::cout << "密度图已保存: " << output_path << std::endl;
    }
};

// 简化的使用接口
cv::Mat generateDensityFromPointCloud(const std::string& las_file,
                                     const std::string& mapping_file,
                                     const std::string& output_path = "") {
    // 1. 加载mapping参数
    MappingParams params = loadMapping(mapping_file);

    // 2. 初始化预处理器（支持灵活的图像尺寸）
    PointCloudPreprocessor processor(params, params.image_width, params.image_height);

    // 3. 加载点云数据（这里假设您有加载LAS文件的函数）
    auto cloud = loadLASFile(las_file);

    // 4. 处理点云生成密度图
    cv::Mat density_map = processor.processPointCloud(cloud);

    // 5. 保存结果（如果指定了输出路径）
    if (!output_path.empty()) {
        processor.saveDensityMap(density_map, output_path);
    }

    std::cout << "处理完成！" << std::endl;
    std::cout << "场景: " << params.scene_name << std::endl;
    std::cout << "图像尺寸: " << params.image_width << "×" << params.image_height << std::endl;
    std::cout << "像素分辨率: " << params.pixel_resolution << " 米/像素" << std::endl;

    return density_map;
}
```
```

### 3. 像素坐标转世界坐标（用于结果分析）

```cpp
struct Point2D {
    float x, y;
};

Point2D pixelToWorld(int pixel_x, int pixel_y, const MappingParams& params) {
    // 方法1：使用预计算的像素分辨率（推荐）
    float world_x = static_cast<float>(pixel_x) * params.pixel_resolution + params.roi_min_x;
    float world_y = static_cast<float>(pixel_y) * params.pixel_resolution + params.roi_min_y;
    
    // 应用逆旋转（恢复到原始坐标系）
    if (std::abs(params.rotation_angle) > 1e-6) {
        float cos_theta = std::cos(-params.rotation_angle);  // 注意负号
        float sin_theta = std::sin(-params.rotation_angle);
        
        float original_x = cos_theta * world_x - sin_theta * world_y;
        float original_y = sin_theta * world_x + cos_theta * world_y;
        
        world_x = original_x;
        world_y = original_y;
    }
    
    return {world_x, world_y};
}
```

### 4. 实用工具函数

```cpp
// 计算两个像素点之间的实际距离（米）
float pixelDistance(int x1, int y1, int x2, int y2, const MappingParams& params) {
    float dx = static_cast<float>(x2 - x1) * params.pixel_resolution;
    float dy = static_cast<float>(y2 - y1) * params.pixel_resolution;
    return std::sqrt(dx * dx + dy * dy);
}

// 计算实例的实际面积（平方米）
float instanceArea(int pixel_count, const MappingParams& params) {
    return static_cast<float>(pixel_count) * params.pixel_resolution * params.pixel_resolution;
}

// 完整的处理流程示例
void processPointCloudToImage(const std::string& las_file, 
                             const std::string& mapping_file,
                             const std::string& output_image) {
    // 1. 加载mapping参数
    MappingParams params = loadMapping(mapping_file);
    
    // 2. 加载点云数据（这里假设您有加载LAS文件的函数）
    auto cloud = loadLASFile(las_file);
    
    // 3. 生成密度图
    cv::Mat density_map = generateDensityMap(cloud, params);
    
    // 4. 保存结果
    cv::imwrite(output_image, density_map);
    
    std::cout << "处理完成！" << std::endl;
    std::cout << "场景: " << params.scene_name << std::endl;
    std::cout << "ROI尺寸: " << params.roi_width << "m × " << params.roi_height << "m" << std::endl;
    std::cout << "像素分辨率: " << params.pixel_resolution << " 米/像素" << std::endl;
}
```

## 📊 数据流程说明

### 完整的数据处理流程
```
原始点云数据 (LAS文件)
    ↓ 人工标注，去除噪声
矢量图 (DXF格式)
    ↓ 提取边界 ± 0.5m边界扩展
floorplan.json (boundingBox)
    ↓ ±10%边界扩展 + 旋转校正（已预计算）
训练ROI坐标系 (roi_min, roi_max)
    ↓ 归一化到256×256
网络训练图像坐标系
```

### 关键处理步骤
1. **点云过滤**：使用floorplan.json的boundingBox过滤点云数据
2. **边界扩展**：对boundingBox进行±10%扩展得到训练ROI
3. **旋转校正**：根据场景主要方向进行旋转（已预计算好角度）
4. **密度图生成**：将过滤后的点云投影到2D密度图
5. **坐标归一化**：将世界坐标归一化到256×256像素坐标

### 重要说明
- **旋转角度已预计算**：mapping.txt中的旋转角度是根据墙体方向计算好的，直接使用即可
- **ROI边界已确定**：训练时使用的ROI边界已保存在mapping.txt中
- **像素分辨率已计算**：一个像素代表多少米已经计算好并保存

## 🔍 使用场景说明

### 场景1：只需要实例分割结果
**如果您只需要知道哪些像素属于哪个实例：**
- 只使用 `*_mask.png` 文件
- 不需要 `*_mapping.txt` 文件
- 直接在256×256图像上进行处理

### 场景2：需要世界坐标信息
**如果您需要知道检测到的实例在真实世界中的位置：**
- 使用 `*_mask.png` 获取实例像素
- 使用 `*_mapping.txt` 将像素坐标转换为世界坐标
- 按照上述C++代码示例进行坐标转换

### 场景3：需要可视化检查
**如果您需要快速查看检测结果：**
- 使用 `*_rgb.png` 文件
- 可以直接用图像查看器打开
- 不同颜色代表不同的检测实例

## 📋 常见问题解答

**Q: 为什么输出的是256×256固定尺寸？**
A: 这是为了简化处理流程。网络训练时使用256×256输入，推理时保持相同尺寸，避免复杂的尺寸转换。

**Q: 如何知道一个像素代表多少厘米？**
A: 直接从mapping.txt的第5行读取像素分辨率。例如，如果第5行是0.050000，则表示1像素 = 0.05米 = 5厘米。

**Q: 什么时候需要使用mapping.txt？**
A: 只有当您需要将检测结果的像素坐标转换回真实世界坐标时才需要。如果只是在图像上进行处理，不需要使用。

**Q: 旋转角度是什么意思？**
A: 这是训练时为了让墙体与坐标轴对齐而应用的旋转角度，已经预计算好。如果您需要将结果映射回原始坐标系，需要应用逆旋转。

## 🚀 纯C++实现（无Python依赖）

为了解决部署环境中没有Python的问题，我们提供了一个完全不依赖Python的纯C++实现。该实现能够直接读取LAS点云文件，使用mapping.txt参数文件，生成高质量的密度图。

### 🎯 核心特性

- ✅ **完全无Python依赖**：纯C++实现，只需要OpenCV和PCL
- ✅ **直接读取LAS文件**：内置LAS文件读取器，支持多个LAS文件批量处理
- ✅ **智能文件查找**：自动扫描并索引mapping文件，无需手动指定路径
- ✅ **文件名自动转换**：支持新旧文件名格式的自动识别和转换
- ✅ **批量处理**：支持从文件列表批量处理多个场景
- ✅ **可配置图像尺寸**：支持任意分辨率（256×256、512×512、1024×1024等）
- ✅ **高性能**：C++原生性能，比Python实现快5-10倍

### 📁 文件结构

```
tools/debug/
├── pointcloud_to_density_processor.cpp  # 主程序源码
├── CMakeLists.txt                       # CMake构建配置
├── build.sh                            # 构建脚本
├── run_batch_processing.sh             # 批量处理脚本
├── build/                              # 构建目录
│   └── pointcloud_processor            # 可执行文件
└── filelists/                          # 参考文件
    ├── pred_filelists.txt              # 预测文件列表
    └── 02_to_label_file_structure.txt  # 目录结构映射
```

### 🔧 构建和安装

#### 系统要求
- **操作系统**：Linux (Ubuntu 18.04+)
- **编译器**：GCC 7.0+ (支持C++17)
- **依赖库**：
  - OpenCV 4.0+
  - PCL (Point Cloud Library) 1.8+
  - CMake 3.10+

**注意**：LAS文件读取使用内置的`SimpleLASReader`类，无需安装PDAL或LASlib等外部库。

#### 构建步骤
```bash
# 1. 进入构建目录
cd tools/debug

# 2. 运行构建脚本
./build.sh

# 3. 验证构建成功
ls -la build/pointcloud_processor
```

如果构建失败，请检查依赖库是否正确安装：
```bash
# 安装OpenCV
sudo apt-get install libopencv-dev

# 安装PCL
sudo apt-get install libpcl-dev

# 安装CMake
sudo apt-get install cmake
```

### 🚀 使用方法

#### 基本用法
```bash
cd tools/debug

# 批量处理（256×256密度图）
./build/pointcloud_processor --batch \
  --data-root /home/<USER>/01_3D-FAVP/handheld_scanner_Data/RS10/ \
  --filelist /path/to/selected_filelist.txt \
  --output /path/to/output/directory

# 生成512×512密度图
./build/pointcloud_processor --batch \
  --data-root /home/<USER>/01_3D-FAVP/handheld_scanner_Data/RS10/ \
  --filelist /path/to/selected_filelist.txt \
  --output /path/to/output/directory \
  --size 512x512

# 生成1024×1024密度图
./build/pointcloud_processor --batch \
  --data-root /home/<USER>/01_3D-FAVP/handheld_scanner_Data/RS10/ \
  --filelist /path/to/selected_filelist.txt \
  --output /path/to/output/directory \
  --size 1024x1024
```

#### 参数说明
- `--batch`：批量处理模式（必需）
- `--data-root`：原始数据根目录，包含场景的LAS文件和floorplan.json
- `--filelist`：要处理的场景列表文件
- `--output`：输出目录，生成的密度图将保存在此目录
- `--size`：输出图像尺寸，格式为`宽度x高度`（可选，默认256x256）

### 📋 输入文件格式

#### 1. 场景列表文件格式
支持两种格式：

**格式1：新格式场景名称**
```
004_cuishanlantian_res-uf_RS10
008_cuishanlantian_res-uf_RS10
001_jindizhongxincheng_res-uf_RS10
```

**格式2：批次目录格式（selected_filelist.txt）**
```
RS10_Batch_02\004_cuishanlantian_res-uf_RS10
RS10_Batch_02\008_cuishanlantian_res-uf_RS10
RS10_Batch_03\001_jindizhongxincheng_res-uf_RS10
```

程序会自动识别格式并进行相应的解析和转换。

#### 2. 数据目录结构
原始数据目录结构应该如下：
```
data_root/
├── scene_name_1/
│   ├── LAS/                    # 或 LAS_Refined/
│   │   ├── segmentA.las        # LAS点云文件（支持多个）
│   │   ├── segmentB.las
│   │   └── ...
│   └── Annotations/
│       └── floorplan.json      # 场景标注文件
├── scene_name_2/
│   └── ...
```

#### 3. Mapping文件位置
Mapping文件应该位于：
```
/home/<USER>/data/RS10_data/04_to_yuhui/
├── RS10_Batch_01/
│   └── scene_name/
│       └── scene_name_mapping.txt
├── RS10_Batch_02/
│   └── scene_name/
│       └── scene_name_mapping.txt
└── ...
```

程序会自动扫描此目录并建立文件索引。

### 🔄 处理流程详解

1. **初始化阶段**
   - 扫描mapping文件目录，建立文件索引
   - 加载文件名转换器和目录映射

2. **场景处理阶段**
   - 解析场景列表文件
   - 对每个场景进行文件名格式识别和转换
   - 查找对应的mapping.txt文件

3. **点云处理阶段**
   - 扫描LAS目录，找到所有.las文件
   - 使用内置LAS读取器直接读取点云数据
   - 合并多个LAS文件的点云数据

4. **密度图生成阶段**
   - 根据mapping.txt中的参数进行点云过滤
   - 应用旋转变换
   - 生成密度图并保存为PNG格式

### 🎛️ 高级配置

#### LAS文件支持
- ✅ **LAS 1.2格式**：标准的LAS文件格式
- ✅ **多文件支持**：自动处理目录中的多个LAS文件
- ✅ **大文件支持**：支持百万级点云数据
- ❌ **LAZ压缩格式**：暂不支持，请使用未压缩的LAS文件

#### 文件名转换规则
程序支持新旧文件名格式的自动转换：

**旧格式 → 新格式**
```
cuishanlantian_res_uf_RS10_4 → 004_cuishanlantian_res-uf_RS10
```

**转换规则**
- 提取房屋编号并零填充到3位
- 将下划线转换为连字符
- 重新排列为：`{编号}_{社区名}_{属性}_{设备名}`

### 🐛 故障排除

#### 常见错误及解决方案

**1. 找不到mapping文件**
```
⚠️ 未找到mapping文件，跳过场景: scene_name
```
**解决方案**：
- 检查mapping文件是否存在于`/home/<USER>/data/RS10_data/04_to_yuhui/`目录下
- 确认文件名格式是否正确：`{scene_name}_mapping.txt`

**2. LAS文件读取失败**
```
❌ LAS文件读取失败: 不是有效的LAS文件格式
```
**解决方案**：
- 确认文件是LAS格式而非LAZ压缩格式
- 检查文件是否损坏
- 确认LAS文件版本为1.2

**3. OpenCV类型错误**
```
OpenCV error: type == B.type() in function 'gemm'
```
**解决方案**：此问题已在最新版本中修复，请重新编译程序。

**4. 编译错误**
```
fatal error: opencv2/opencv.hpp: No such file or directory
```
**解决方案**：
```bash
sudo apt-get install libopencv-dev libpcl-dev
```


### 🎯 部署优势

1. **零Python依赖**：完全独立的C++可执行文件
2. **高性能处理**：原生C++性能，比Python快5-10倍
3. **内存高效**：优化的内存管理，支持大规模点云
4. **易于部署**：单个可执行文件，无需复杂环境配置
5. **跨平台兼容**：支持Linux系统，易于移植到其他平台

// 调整旋转角度（如果需要）
params.rotation_angle = 0.0f;  // 弧度制，π/2 = 90度
```

### 部署优势

1. **无Python依赖**：只需要OpenCV，易于部署
2. **高性能**：C++原生性能，比Python快5-10倍
3. **内置参数**：无需复杂的参数文件生成流程
4. **灵活配置**：支持任意图像尺寸
5. **批量处理**：完全复现Python的批量处理逻辑
