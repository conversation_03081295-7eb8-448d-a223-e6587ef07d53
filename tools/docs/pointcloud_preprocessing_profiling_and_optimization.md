# 点云预处理性能分析与加速指南

本指南详细介绍如何使用本仓库提供的“单场景点云预处理性能分析脚本”，以及已实现与可选的加速策略。旨在在不改变采样密度（downsampling rate）和数据质量的前提下，显著提升预处理速度。

适用对象：需要定位 `tools/dataset_converters/generate_coco_hc_0722.py` 运行瓶颈，并对点云加载、旋转与密度图生成步骤进行优化的开发者与研究人员。

---

## 1. 目录结构与关键脚本

- 预处理主脚本：
  - `tools/dataset_converters/generate_coco_hc_0722.py`
    - 遍历场景、读取 floorplan.json、计算旋转角、读取 LAS 点云、生成密度图、输出 COCO 标注等。
    - 新增参数：`--loader_backend`（详见第 5 节）。
    - 已优化：二维旋转改为直接 cos/sin 计算，避免齐次坐标矩阵乘（降内存与计算开销）。

- 性能分析脚本：
  - `tools/debug/profile_pointcloud_preprocess.py`
    - 针对单个场景复现与切分主流程，逐阶段计时，输出详尽 JSON 报告与“Top hotspots”。
    - 支持多线程/单线程对比，支持选择优化密度图路径。

- 密度图工具：
  - `tools/dataset_converters/hc_utils.py`
    - `generate_density()`：原始版本（基于 unique 统计）。
    - `generate_density_optimized()`：优化版本（基于 `numpy.histogram2d`）。

---

## 2. 快速开始

1) 列出切分中的可用场景（train/val/test）：

```bash
python tools/debug/profile_pointcloud_preprocess.py --list_scenes
```

2) 对首个 train 场景进行性能分析（使用优化密度图 + 多线程）：

```bash
python tools/debug/profile_pointcloud_preprocess.py --use_optimized_density --multithread --max_workers 16
```

3) 对指定场景进行单线程基线分析：

```bash
python tools/debug/profile_pointcloud_preprocess.py --scene RS10_XXXX --multithread false
```

运行结束后，脚本会在控制台打印“Top hotspots”，并将完整报告保存到：

- `tools/debug/profile_report.json`

---

## 3. 配置与默认值

性能分析脚本默认与主脚本保持一致（可在 CLI 覆盖）：

- 数据根目录：`--data_root /home/<USER>/01_3D-FAVP/handheld_scanner_Data/RS10/`
- 切分文件夹：`--filelists_dir /home/<USER>/data/RS10_data/00_dataset_spilt/filelists`
- 密度图尺寸：与配置中的 `img_size` 保持一致
- 下采样因子：`--downsample_factor 4`
- 并行工作线程：`--max_workers 16`
- 是否使用优化密度图：`--use_optimized_density`（默认关闭）
- 是否多线程（ThreadPool）：`--multithread`（默认开启）

说明：
- 主脚本 `generate_coco_hc_0722.py` 中还提供 `--loader_backend`（`thread`/`process`/`none`），用于指定 LAS 读取与处理的并行后端；性能分析脚本当前使用 `--multithread` 开关模拟线程/单线程对比。

---

## 4. 输出解读

运行完成后，控制台会输出“Top hotspots”，并生成 `profile_report.json`。关键字段说明：

- `scene`：分析的场景名
- `n_las_files`：该场景使用的 LAS 文件数量
- `n_points_before_downsample_sum`：所有 LAS 文件在 per-file 预采样前的点数和
- `n_points_after_processing`：所有处理后的点总数（用于密度图投影）
- `timings_sec`：各阶段累计耗时（秒），已按耗时降序排序，常见键有：
  - `per_file_las_read`：LAS 读取时间
  - `per_file_mask_build`：ROI 过滤掩码构建时间
  - `per_file_mask_apply`：掩码应用（索引切片）
  - `per_file_downsample`：per-file 下采样
  - `per_file_rotate`：二维旋转
  - `concat_points`：跨文件拼接
  - `downsample_stage2`：第二阶段全局下采样（若启用）
  - `generate_density` 或 `generate_density_optimized`：密度图生成
  - `rot_normalize_gt`：GT 旋转归一化
- `total_time_sec`：上述各阶段总和（粗略总时长）

建议：优先关注耗时前 3~5 项，即“Top hotspots”，并采用第 6 节的实践进行定向优化。

---

## 5. 并行后端选择（主脚本）

主脚本 `generate_coco_hc_0722.py` 新增参数 `--loader_backend`：

- `thread`（默认）：使用 `ThreadPoolExecutor` 并行处理各 LAS 文件。适合 IO 密集或 NumPy 能释放 GIL 的场景。
- `process`：使用 `ProcessPoolExecutor`。当耗时主要在 CPU 计算（如直方图、旋转、过滤）且线程受 GIL 限制时，进程并行通常更快。
- `none`：串行处理。便于基线对比或调试。

示例：

```bash
# 进程并行（建议在 CPU 计算占比高时尝试）
python tools/dataset_converters/generate_coco_hc_0722.py --loader_backend process --max_workers 16 --use_optimized_density
```

提示：当选择线程池并行时，可考虑限制内部 BLAS/OMP 线程数，避免过度并行（见第 6 节）。

---

## 6. 已实现优化与推荐实践

已实现：
1) 二维旋转优化（保持结果等价）
   - 将齐次坐标矩阵乘替换为直接 `cos/sin` 计算，减少内存与算力开销。
2) 并行后端开关
   - `--loader_backend thread|process|none`，可按数据与机器情况选择最优策略。
3) 优化密度图生成（可选）
   - `generate_density_optimized()` 使用 `numpy.histogram2d`，通常快于 unique-based 实现。

推荐实践：
- 若“密度图生成”或“旋转”占用多数时间：
  - 打开 `--use_optimized_density`；尝试 `--loader_backend process`。
- 若“LAS 读取”占多数且磁盘足够快：
  - 保持 `thread` 后端；增加 `--max_workers`（适度）。
- 控制 NumPy/MKL 线程：
  - 在使用线程池时，适当设置环境变量减少过度并行：
    - `OMP_NUM_THREADS=1 MKL_NUM_THREADS=1 NUMEXPR_NUM_THREADS=1`
  - 在使用进程池时，可给每个进程保留少量内部线程：
    - 例如 `OMP_NUM_THREADS=2`。

保持采样密度：
- 当前两阶段下采样策略：
  - per-file 初步下采样：`initial_downsample = max(1, downsample_factor//4)`
  - 全局二次下采样（如需要）：`remaining_factor = downsample_factor // initial_downsample`
- 任何优化方案均须维持总的 `1/downsample_factor` 采样比例。若改为“按文件直接总下采样”，请以新开关形式提供，默认保持现有全局行为。

---

## 7. 进一步可选加速（按需实现）

以下方案在不改变采样密度的前提下，通常能进一步提速，可在需求明确后逐步引入：

1) 流式累积密度（避免拼接巨数组）
   - 逐 LAS：读取 → 过滤 → 下采样 → 旋转 → `histogram2d` 得到局部密度 → 累加至全局密度。
   - 优点：避免构造 `all_points` 大数组与一次性大规模直方图；显著降低内存峰值与缓存抖动。
   - 兼容性：保持 per-file 初步下采样；若保留“全局二次下采样”，则流式仅用于初步阶段。

2) Numba/CuPy（需环境支持，建议可选开关）
   - Numba：将投影与计数写为简单核函数，配合 `np.add.at`/`bincount`，常优于通用 `histogram2d`。
   - CuPy：若 GPU 有空闲，可将大规模直方图加速到 GPU。

3) 更严格的 dtype 管理与复用
   - 全链路 `float32`，尽量避免中间转换与拷贝；复用临时数组。

---

## 8. 常见问题与排查

- 无法找到场景/切分：
  - 检查 `--filelists_dir` 下是否存在 `train.txt/val.txt/test.txt`，且场景目录位于 `--data_root`。
- 性能报告为空或异常：
  - 若某些场景 floorplan 异常（斜率、坐标全零），主脚本会跳过；分析脚本同样会记录耗时很小的加载步骤。
- 多线程下吞吐不升反降：
  - 检查是否为典型 CPU 计算瓶颈（尝试 `--loader_backend process`）；限制 BLAS/OMP 线程数避免过度并行。

---

## 9. 示例命令合集

- 多线程 + 优化密度（单场景分析）
```bash
python tools/debug/profile_pointcloud_preprocess.py --use_optimized_density --multithread --max_workers 16
```

- 单线程（基线对比）
```bash
python tools/debug/profile_pointcloud_preprocess.py --scene RS10_XXXX --multithread false
```

- 主脚本 + 进程并行 + 优化密度
```bash
python tools/dataset_converters/generate_coco_hc_0722.py --loader_backend process --max_workers 16 --use_optimized_density
```

- 主脚本 + 线程并行 + 控制内部线程（Linux bash 示例）
```bash
export OMP_NUM_THREADS=1 MKL_NUM_THREADS=1 NUMEXPR_NUM_THREADS=1
python tools/dataset_converters/generate_coco_hc_0722.py --loader_backend thread --max_workers 16 --use_optimized_density
```

---

## 10. 后续工作与建议

- 若实际热点显示“密度图生成”与“拼接”耗时过高，建议优先实现“流式累积密度”路径（新增开关，默认保持现有行为）。
- 将 `--loader_backend` 选择与内部线程策略纳入训练/预处理配置中，便于不同机器快速切换最佳组合。
- 为关键模块补充单元测试（建议放在 `tools/debug/`）以保障后续优化的正确性与可回归性。

如需我进一步落地“流式累积密度”实现或为分析脚本补充 `--loader_backend` 开关，请告知具体偏好与环境限制。
