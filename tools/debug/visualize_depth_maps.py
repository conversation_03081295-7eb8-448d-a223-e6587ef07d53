#!/usr/bin/env python3
"""
深度图可视化脚本

将Structured3D数据集中的深度图进行可视化处理，生成易于查看的图像
"""

import cv2
import numpy as np
import os
import sys
from pathlib import Path

# 设置matplotlib后端以避免显示问题
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端
import matplotlib.pyplot as plt
import matplotlib.cm as cm

def create_output_directory():
    """创建输出目录"""
    output_dir = "output/0_tmp_debug_files/0_depth_map_display_stru3d"
    os.makedirs(output_dir, exist_ok=True)
    return output_dir

def find_sample_depth_images(data_root, max_samples=3):
    """查找示例深度图像文件"""
    depth_files = []
    
    if not os.path.exists(data_root):
        print(f"❌ 数据根目录不存在: {data_root}")
        return depth_files
    
    print(f"🔍 在 {data_root} 中搜索深度图像...")
    
    # 遍历查找深度图像
    for root, dirs, files in os.walk(data_root):
        for file in files:
            if file == 'depth.png':
                full_path = os.path.join(root, file)
                depth_files.append(full_path)
                print(f"  找到: {full_path}")
                if len(depth_files) >= max_samples:
                    break
        if len(depth_files) >= max_samples:
            break
    
    print(f"✅ 总共找到 {len(depth_files)} 个深度图像文件")
    return depth_files

def analyze_depth_image(depth_path):
    """分析深度图像的基本信息"""
    print(f"\n=== 分析深度图像: {os.path.basename(os.path.dirname(depth_path))} ===")
    
    # 读取深度图像
    depth_img = cv2.imread(depth_path, cv2.IMREAD_ANYDEPTH | cv2.IMREAD_ANYCOLOR)
    
    if depth_img is None:
        print(f"❌ 无法读取深度图像: {depth_path}")
        return None
    
    print(f"图像尺寸: {depth_img.shape}")
    print(f"数据类型: {depth_img.dtype}")
    print(f"深度值范围: [{depth_img.min()}, {depth_img.max()}]")
    
    # 统计有效深度值
    valid_mask = depth_img > 500
    valid_depths = depth_img[valid_mask]
    
    print(f"有效深度值数量: {len(valid_depths)} / {depth_img.size} ({len(valid_depths)/depth_img.size*100:.1f}%)")
    if len(valid_depths) > 0:
        print(f"有效深度值范围: [{valid_depths.min()}, {valid_depths.max()}]")
        print(f"有效深度值均值: {valid_depths.mean():.1f}")
        print(f"有效深度值标准差: {valid_depths.std():.1f}")
    
    return depth_img

def create_depth_visualizations(depth_img, output_dir, scene_name):
    """创建多种深度图可视化"""
    
    # 1. 原始深度图（灰度）
    plt.figure(figsize=(15, 12))
    
    # 子图1: 原始深度图
    plt.subplot(2, 3, 1)
    plt.imshow(depth_img, cmap='gray')
    plt.title(f'原始深度图 (灰度)\n{scene_name}')
    plt.colorbar(label='深度值')
    
    # 子图2: 彩色深度图 (Jet colormap)
    plt.subplot(2, 3, 2)
    # 对深度值进行归一化以获得更好的可视化效果
    depth_normalized = depth_img.astype(np.float32)
    depth_normalized[depth_normalized <= 500] = np.nan  # 将无效值设为NaN
    
    im2 = plt.imshow(depth_normalized, cmap='jet')
    plt.title('彩色深度图 (Jet)')
    plt.colorbar(im2, label='深度值')
    
    # 子图3: 热力图 (Hot colormap)
    plt.subplot(2, 3, 3)
    im3 = plt.imshow(depth_normalized, cmap='hot')
    plt.title('热力图 (Hot)')
    plt.colorbar(im3, label='深度值')
    
    # 子图4: 深度直方图
    plt.subplot(2, 3, 4)
    valid_depths = depth_img[depth_img > 500]
    if len(valid_depths) > 0:
        plt.hist(valid_depths, bins=50, alpha=0.7, edgecolor='black')
        plt.xlabel('深度值')
        plt.ylabel('像素数量')
        plt.title('深度值分布直方图')
        plt.grid(True, alpha=0.3)
    
    # 子图5: 增强对比度的深度图
    plt.subplot(2, 3, 5)
    # 使用百分位数进行对比度增强
    if len(valid_depths) > 0:
        p2, p98 = np.percentile(valid_depths, (2, 98))
        depth_enhanced = np.clip(depth_img, p2, p98)
        depth_enhanced = (depth_enhanced - p2) / (p98 - p2) * 255
        depth_enhanced = depth_enhanced.astype(np.uint8)
        
        plt.imshow(depth_enhanced, cmap='viridis')
        plt.title(f'增强对比度\n(2%-98%百分位数)')
        plt.colorbar(label='归一化深度值')
    
    # 子图6: 深度梯度图
    plt.subplot(2, 3, 6)
    if len(valid_depths) > 0:
        # 计算深度梯度
        grad_x = cv2.Sobel(depth_img.astype(np.float32), cv2.CV_32F, 1, 0, ksize=3)
        grad_y = cv2.Sobel(depth_img.astype(np.float32), cv2.CV_32F, 0, 1, ksize=3)
        gradient_magnitude = np.sqrt(grad_x**2 + grad_y**2)
        
        plt.imshow(gradient_magnitude, cmap='plasma')
        plt.title('深度梯度图')
        plt.colorbar(label='梯度幅值')
    
    plt.tight_layout()
    
    # 保存综合可视化图
    output_path = os.path.join(output_dir, f'{scene_name}_depth_visualization.png')
    plt.savefig(output_path, dpi=150, bbox_inches='tight')
    print(f"✅ 保存综合可视化图: {output_path}")
    plt.close()
    
    # 单独保存高质量的彩色深度图
    plt.figure(figsize=(12, 8))
    im = plt.imshow(depth_normalized, cmap='jet', aspect='auto')
    plt.title(f'高质量彩色深度图 - {scene_name}', fontsize=16)
    cbar = plt.colorbar(im, shrink=0.8)
    cbar.set_label('深度值 (单位)', fontsize=12)
    plt.axis('off')  # 去除坐标轴
    
    hq_output_path = os.path.join(output_dir, f'{scene_name}_depth_colormap_hq.png')
    plt.savefig(hq_output_path, dpi=300, bbox_inches='tight', pad_inches=0.1)
    print(f"✅ 保存高质量彩色深度图: {hq_output_path}")
    plt.close()

def create_3d_depth_visualization(depth_img, output_dir, scene_name):
    """创建3D深度可视化"""
    try:
        from mpl_toolkits.mplot3d import Axes3D
        
        # 降采样以提高性能
        step = max(1, min(depth_img.shape) // 100)
        depth_sampled = depth_img[::step, ::step]
        
        # 创建坐标网格
        y, x = np.mgrid[0:depth_sampled.shape[0], 0:depth_sampled.shape[1]]
        
        # 只显示有效深度值
        valid_mask = depth_sampled > 500
        x_valid = x[valid_mask]
        y_valid = y[valid_mask]
        z_valid = depth_sampled[valid_mask]
        
        if len(z_valid) > 0:
            fig = plt.figure(figsize=(12, 8))
            ax = fig.add_subplot(111, projection='3d')
            
            # 创建3D散点图
            scatter = ax.scatter(x_valid, y_valid, z_valid, 
                               c=z_valid, cmap='jet', s=1, alpha=0.6)
            
            ax.set_xlabel('X (像素)')
            ax.set_ylabel('Y (像素)')
            ax.set_zlabel('深度值')
            ax.set_title(f'3D深度可视化 - {scene_name}')
            
            plt.colorbar(scatter, shrink=0.5, aspect=20, label='深度值')
            
            # 保存3D可视化
            output_3d_path = os.path.join(output_dir, f'{scene_name}_depth_3d.png')
            plt.savefig(output_3d_path, dpi=150, bbox_inches='tight')
            print(f"✅ 保存3D深度可视化: {output_3d_path}")
            plt.close()
    
    except ImportError:
        print("⚠️  无法创建3D可视化（缺少3D绘图支持）")
    except Exception as e:
        print(f"⚠️  创建3D可视化时出错: {e}")

def main():
    print("深度图可视化脚本")
    print("=" * 50)
    
    # 创建输出目录
    output_dir = create_output_directory()
    print(f"📁 输出目录: {output_dir}")
    
    # 默认数据路径
    default_data_root = "/home/<USER>/05_DL_dataset_wuhan/03_开源数据集/structure_3D/Panorama/"
    
    if len(sys.argv) > 1:
        data_root = sys.argv[1]
    else:
        data_root = default_data_root
        print(f"使用默认数据路径: {data_root}")
        print("您也可以通过命令行参数指定路径: python visualize_depth_maps.py <数据路径>")
    
    # 查找深度图像文件
    depth_files = find_sample_depth_images(data_root, max_samples=3)
    
    if not depth_files:
        print("❌ 未找到深度图像文件")
        print("请确保数据路径正确，并且包含 depth.png 文件")
        return
    
    # 处理每个深度图像
    for i, depth_file in enumerate(depth_files):
        print(f"\n{'='*60}")
        print(f"处理第 {i+1}/{len(depth_files)} 个深度图像")
        print(f"{'='*60}")
        
        # 分析深度图像
        depth_img = analyze_depth_image(depth_file)
        
        if depth_img is not None:
            # 生成场景名称
            scene_name = f"scene_{i+1:02d}_{os.path.basename(os.path.dirname(depth_file))}"
            
            # 创建可视化
            create_depth_visualizations(depth_img, output_dir, scene_name)
            create_3d_depth_visualization(depth_img, output_dir, scene_name)
    
    print(f"\n{'='*60}")
    print("🎉 深度图可视化完成！")
    print(f"📁 所有可视化结果已保存到: {output_dir}")
    print("📋 生成的文件类型:")
    print("  - *_depth_visualization.png: 综合可视化（6个子图）")
    print("  - *_depth_colormap_hq.png: 高质量彩色深度图")
    print("  - *_depth_3d.png: 3D深度可视化")
    print(f"{'='*60}")

if __name__ == "__main__":
    main()
