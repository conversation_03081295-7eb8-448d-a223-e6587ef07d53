#!/usr/bin/env python3
"""
诊断失败场景的具体问题

检查：
1. 文件是否存在
2. 文件大小是否正常
3. 图像是否可以正常读取
4. 目录结构是否完整
"""

import os
import sys
import cv2
import numpy as np

def diagnose_single_scene(data_root, part, scene):
    """诊断单个失败场景"""
    print(f"\n🔍 诊断场景: {part}/{scene}")
    
    scene_path = os.path.join(data_root, part, 'Structured3D', scene)
    print(f"   场景路径: {scene_path}")
    
    if not os.path.exists(scene_path):
        print(f"   ❌ 场景目录不存在")
        return False
    
    # 检查2D_rendering目录
    rendering_dir = os.path.join(scene_path, "2D_rendering")
    if not os.path.exists(rendering_dir):
        print(f"   ❌ 2D_rendering目录不存在")
        return False
    
    # 获取房间目录
    try:
        rooms = [d for d in os.listdir(rendering_dir) 
                if os.path.isdir(os.path.join(rendering_dir, d)) and d.isdigit()]
        rooms = sorted(rooms)
        print(f"   📂 找到 {len(rooms)} 个房间: {rooms}")
    except Exception as e:
        print(f"   ❌ 无法读取房间目录: {e}")
        return False
    
    if not rooms:
        print(f"   ❌ 没有找到房间目录")
        return False
    
    # 检查每个房间的文件
    all_rooms_ok = True
    for room in rooms:
        print(f"   🏠 检查房间 {room}:")
        
        panorama_dir = os.path.join(rendering_dir, room, "panorama")
        if not os.path.exists(panorama_dir):
            print(f"      ❌ panorama目录不存在")
            all_rooms_ok = False
            continue
        
        full_dir = os.path.join(panorama_dir, "full")
        if not os.path.exists(full_dir):
            print(f"      ❌ full目录不存在")
            all_rooms_ok = False
            continue
        
        # 检查关键文件
        files_to_check = {
            'depth.png': os.path.join(full_dir, 'depth.png'),
            'rgb_coldlight.png': os.path.join(full_dir, 'rgb_coldlight.png'),
            'camera_xyz.txt': os.path.join(panorama_dir, 'camera_xyz.txt')
        }
        
        room_ok = True
        for file_name, file_path in files_to_check.items():
            if os.path.exists(file_path):
                file_size = os.path.getsize(file_path)
                print(f"      ✅ {file_name}: {file_size} bytes")
                
                # 尝试读取图像文件
                if file_name.endswith('.png'):
                    try:
                        if file_name == 'depth.png':
                            img = cv2.imread(file_path, cv2.IMREAD_ANYDEPTH | cv2.IMREAD_ANYCOLOR)
                        else:
                            img = cv2.imread(file_path)
                        
                        if img is None:
                            print(f"         ❌ 图像无法读取（返回None）")
                            room_ok = False
                        else:
                            print(f"         ✅ 图像读取成功: {img.shape}")
                            
                            # 对于RGB图像，测试颜色空间转换
                            if file_name == 'rgb_coldlight.png':
                                try:
                                    rgb_img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
                                    print(f"         ✅ 颜色转换成功")
                                except Exception as e:
                                    print(f"         ❌ 颜色转换失败: {e}")
                                    room_ok = False
                    except Exception as e:
                        print(f"         ❌ 读取图像时出错: {e}")
                        room_ok = False
                
                elif file_name == 'camera_xyz.txt':
                    try:
                        with open(file_path, 'r') as f:
                            content = f.read().strip()
                        if content:
                            coords = list(map(float, content.split()))
                            print(f"         ✅ 相机坐标: {coords}")
                        else:
                            print(f"         ❌ 文件为空")
                            room_ok = False
                    except Exception as e:
                        print(f"         ❌ 读取相机文件时出错: {e}")
                        room_ok = False
            else:
                print(f"      ❌ {file_name}: 文件不存在")
                room_ok = False
        
        if not room_ok:
            all_rooms_ok = False
    
    return all_rooms_ok

def main():
    print("失败场景诊断脚本")
    print("=" * 60)
    
    # 失败场景列表
    failed_scenes = [
        ('Structured3D_panorama_01', 'scene_00212'),
        ('Structured3D_panorama_01', 'scene_00213'),
        ('Structured3D_panorama_03', 'scene_00613'),
        ('Structured3D_panorama_04', 'scene_00809'),
        ('Structured3D_panorama_04', 'scene_00810'),
        ('Structured3D_panorama_05', 'scene_01010'),
        ('Structured3D_panorama_06', 'scene_01209'),
        ('Structured3D_panorama_06', 'scene_01210'),
        ('Structured3D_panorama_07', 'scene_01410'),
        ('Structured3D_panorama_07', 'scene_01412'),
        ('Structured3D_panorama_09', 'scene_01815'),
        ('Structured3D_panorama_10', 'scene_02011'),
        ('Structured3D_panorama_11', 'scene_02212'),
        ('Structured3D_panorama_11', 'scene_02213'),
        ('Structured3D_panorama_12', 'scene_02411'),
        ('Structured3D_panorama_13', 'scene_02608')
    ]
    
    data_root = "/home/<USER>/05_DL_dataset_wuhan/03_开源数据集/structure_3D/Panorama/"
    
    print(f"📁 数据根目录: {data_root}")
    print(f"🎯 诊断 {len(failed_scenes)} 个失败场景")
    
    # 诊断每个失败场景
    recoverable_scenes = []
    corrupted_scenes = []
    
    for part, scene in failed_scenes:
        is_ok = diagnose_single_scene(data_root, part, scene)
        
        if is_ok:
            recoverable_scenes.append((part, scene))
            print(f"   ✅ 场景可能可以恢复")
        else:
            corrupted_scenes.append((part, scene))
            print(f"   ❌ 场景数据确实有问题")
    
    # 总结
    print(f"\n" + "=" * 60)
    print(f"📊 诊断总结")
    print(f"=" * 60)
    print(f"总失败场景: {len(failed_scenes)}")
    print(f"可能可恢复: {len(recoverable_scenes)}")
    print(f"确实损坏: {len(corrupted_scenes)}")
    
    if recoverable_scenes:
        print(f"\n✅ 可能可恢复的场景:")
        for part, scene in recoverable_scenes:
            print(f"   - {part}/{scene}")
        print(f"\n💡 建议: 这些场景可能只是临时问题，可以尝试:")
        print(f"   1. 重新运行重试命令")
        print(f"   2. 检查磁盘空间和权限")
        print(f"   3. 单独处理这些场景")
    
    if corrupted_scenes:
        print(f"\n❌ 确实损坏的场景:")
        for part, scene in corrupted_scenes:
            print(f"   - {part}/{scene}")
        print(f"\n💡 建议: 这些场景的数据文件确实有问题:")
        print(f"   1. 检查原始数据是否完整")
        print(f"   2. 重新下载这些数据部分")
        print(f"   3. 或者接受这些场景无法处理的现实")
    
    # 计算最终成功率
    if len(failed_scenes) > 0:
        final_success_rate = (3484 + len(recoverable_scenes)) / 3500 * 100
        print(f"\n📈 如果可恢复场景成功处理:")
        print(f"   最终成功率: {final_success_rate:.1f}% ({3484 + len(recoverable_scenes)}/3500)")
    
    print(f"\n🎯 结论:")
    if len(corrupted_scenes) == len(failed_scenes):
        print(f"   所有失败场景的数据文件都确实存在问题")
        print(f"   重试无法解决这些问题，需要修复数据源")
    elif len(recoverable_scenes) > 0:
        print(f"   部分场景可能可以通过重试解决")
        print(f"   建议先尝试修复环境问题再重试")
    else:
        print(f"   需要进一步分析具体问题")

if __name__ == "__main__":
    main()
