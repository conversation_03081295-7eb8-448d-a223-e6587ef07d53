#!/usr/bin/env python3
"""
测试深度图路径和基本功能
"""

import os
import sys

def test_paths():
    """测试可能的数据路径"""
    print("测试深度图数据路径...")
    
    possible_paths = [
        "/home/<USER>/05_DL_dataset_wuhan/03_开源数据集/structure_3D/Panorama/",
        "/home/<USER>/05_DL_dataset_wuhan/03_开源数据集/structure_3D/",
        "/home/<USER>/05_DL_dataset_wuhan/03_开源数据集/",
        "/home/<USER>/05_DL_dataset_wuhan/",
        "/home/<USER>/",
        "/data/structure_3D/",
        "/data/",
        "/home/<USER>/data/",
    ]
    
    found_paths = []
    
    for path in possible_paths:
        print(f"检查路径: {path}")
        if os.path.exists(path):
            print(f"  ✅ 路径存在")
            try:
                items = os.listdir(path)
                print(f"  📁 包含 {len(items)} 个项目")
                if len(items) > 0:
                    print(f"  📋 前几个项目: {items[:3]}")
                found_paths.append(path)
            except PermissionError:
                print(f"  ❌ 权限不足")
        else:
            print(f"  ❌ 路径不存在")
    
    return found_paths

def find_depth_files(base_path):
    """在给定路径下查找深度文件"""
    print(f"\n在 {base_path} 中搜索深度文件...")
    
    depth_files = []
    count = 0
    max_search = 100  # 限制搜索数量
    
    try:
        for root, dirs, files in os.walk(base_path):
            count += 1
            if count > max_search:
                print(f"  搜索限制达到 {max_search} 个目录")
                break
                
            if 'depth.png' in files:
                depth_file = os.path.join(root, 'depth.png')
                depth_files.append(depth_file)
                print(f"  找到深度文件: {depth_file}")
                
                if len(depth_files) >= 3:  # 找到3个就够了
                    break
    
    except Exception as e:
        print(f"  搜索时出错: {e}")
    
    return depth_files

def test_opencv():
    """测试OpenCV功能"""
    print("\n测试OpenCV...")
    try:
        import cv2
        print(f"  ✅ OpenCV版本: {cv2.__version__}")
        return True
    except ImportError as e:
        print(f"  ❌ OpenCV导入失败: {e}")
        return False

def test_matplotlib():
    """测试matplotlib功能"""
    print("\n测试matplotlib...")
    try:
        import matplotlib
        matplotlib.use('Agg')  # 非交互式后端
        import matplotlib.pyplot as plt
        print(f"  ✅ matplotlib版本: {matplotlib.__version__}")
        print(f"  ✅ 后端: {matplotlib.get_backend()}")
        return True
    except ImportError as e:
        print(f"  ❌ matplotlib导入失败: {e}")
        return False

def create_test_output():
    """创建测试输出目录"""
    print("\n创建输出目录...")
    output_dir = "output/0_tmp_debug_files/0_depth_map_display_stru3d"
    
    try:
        os.makedirs(output_dir, exist_ok=True)
        print(f"  ✅ 输出目录创建成功: {output_dir}")
        
        # 测试写入权限
        test_file = os.path.join(output_dir, "test.txt")
        with open(test_file, 'w') as f:
            f.write("测试文件")
        os.remove(test_file)
        print(f"  ✅ 写入权限正常")
        
        return output_dir
    except Exception as e:
        print(f"  ❌ 创建输出目录失败: {e}")
        return None

def main():
    print("深度图路径和功能测试")
    print("=" * 40)
    
    # 测试路径
    found_paths = test_paths()
    
    if not found_paths:
        print("\n❌ 未找到任何有效路径")
        return
    
    # 测试依赖库
    opencv_ok = test_opencv()
    matplotlib_ok = test_matplotlib()
    
    if not (opencv_ok and matplotlib_ok):
        print("\n❌ 依赖库测试失败")
        return
    
    # 创建输出目录
    output_dir = create_test_output()
    if not output_dir:
        print("\n❌ 输出目录创建失败")
        return
    
    # 搜索深度文件
    for path in found_paths[:2]:  # 只测试前2个路径
        depth_files = find_depth_files(path)
        if depth_files:
            print(f"\n✅ 在 {path} 中找到 {len(depth_files)} 个深度文件")
            
            # 测试读取第一个深度文件
            if depth_files:
                test_file = depth_files[0]
                print(f"\n测试读取深度文件: {test_file}")
                
                try:
                    import cv2
                    depth_img = cv2.imread(test_file, cv2.IMREAD_ANYDEPTH | cv2.IMREAD_ANYCOLOR)
                    
                    if depth_img is not None:
                        print(f"  ✅ 成功读取深度图像")
                        print(f"  📏 尺寸: {depth_img.shape}")
                        print(f"  🔢 数据类型: {depth_img.dtype}")
                        print(f"  📊 值范围: [{depth_img.min()}, {depth_img.max()}]")
                        
                        print(f"\n🎉 所有测试通过！可以运行深度图可视化脚本")
                        print(f"推荐使用路径: {path}")
                        return
                    else:
                        print(f"  ❌ 无法读取深度图像")
                
                except Exception as e:
                    print(f"  ❌ 读取深度图像时出错: {e}")
    
    print(f"\n⚠️  未找到可用的深度文件")

if __name__ == "__main__":
    main()
