#!/usr/bin/env python3
"""
测试简洁的可视化功能
"""

import os
import sys
import numpy as np
import cv2
from unittest.mock import Mock

# Add project root to path
ROOT_DIR = os.path.abspath(os.path.join(os.path.dirname(__file__), os.pardir, os.pardir))
if ROOT_DIR not in sys.path:
    sys.path.insert(0, ROOT_DIR)

def create_test_image():
    """创建一个测试图像"""
    img = np.ones((300, 300, 3), dtype=np.uint8) * 128  # 灰色背景
    cv2.rectangle(img, (50, 50), (150, 150), (200, 200, 200), -1)  # 浅灰色矩形
    cv2.circle(img, (200, 200), 50, (180, 180, 180), -1)  # 浅灰色圆形
    return img

def create_test_masks():
    """创建测试用的masks"""
    masks = []
    
    # Mask 1: 矩形区域
    mask1 = np.zeros((300, 300), dtype=np.uint8)
    mask1[60:140, 60:140] = 1
    masks.append(mask1)
    
    # Mask 2: 圆形区域
    mask2 = np.zeros((300, 300), dtype=np.uint8)
    cv2.circle(mask2, (200, 200), 40, 1, -1)
    masks.append(mask2)
    
    # Mask 3: 不规则区域
    mask3 = np.zeros((300, 300), dtype=np.uint8)
    points = np.array([[100, 250], [150, 220], [200, 250], [180, 280], [120, 280]], np.int32)
    cv2.fillPoly(mask3, [points], 1)
    masks.append(mask3)
    
    return np.array(masks)

def create_mock_pred_instances():
    """创建模拟的预测实例"""
    masks = create_test_masks()
    
    # 创建mock对象
    pred_instances = Mock()
    pred_instances.masks = Mock()
    pred_instances.masks.cpu.return_value.numpy.return_value = masks
    
    return pred_instances

def test_clean_visualization():
    """测试简洁的可视化功能"""
    print("Testing clean visualization function...")
    
    # 导入我们的函数
    sys.path.insert(0, os.path.join(ROOT_DIR, 'tools'))
    from inference_with_evaluators import generate_bright_colors, save_clean_visualization
    
    # 创建测试数据
    test_img = create_test_image()
    
    # 创建mock result对象
    result = Mock()
    result.pred_instances = create_mock_pred_instances()
    
    # 创建输出目录
    output_dir = os.path.join(ROOT_DIR, 'tools', 'debug', 'clean_visualization_test')
    os.makedirs(output_dir, exist_ok=True)
    
    # 创建临时图像文件
    test_img_path = os.path.join(output_dir, 'test_input.png')
    cv2.imwrite(test_img_path, test_img)
    
    # 测试可视化函数
    save_clean_visualization(test_img_path, result, output_dir)
    
    # 检查输出文件是否存在
    output_path = os.path.join(output_dir, 'test_input_result.png')
    if os.path.exists(output_path):
        print(f"✓ Clean visualization saved successfully: {output_path}")
        
        # 验证输出图像
        result_img = cv2.imread(output_path)
        if result_img is not None:
            print(f"✓ Output image dimensions: {result_img.shape}")
            print("✓ Clean visualization test passed!")
            return True
        else:
            print("❌ Failed to read output image")
            return False
    else:
        print("❌ Output file not created")
        return False

def test_color_generation():
    """测试颜色生成功能"""
    print("\nTesting bright color generation...")
    
    from inference_with_evaluators import generate_bright_colors
    
    # 测试生成5种颜色
    colors = generate_bright_colors(5)
    print(f"Generated {len(colors)} colors:")
    
    for i, color in enumerate(colors):
        print(f"  Color {i}: BGR{color}")
        # 验证颜色格式
        assert len(color) == 3, f"Color {i} should have 3 components"
        assert all(0 <= c <= 255 for c in color), f"Color {i} values should be in [0, 255]"
    
    print("✓ Color generation test passed!")
    return True

def create_demo_comparison():
    """创建演示对比图"""
    print("\nCreating demonstration comparison...")
    
    from inference_with_evaluators import generate_bright_colors
    
    # 创建测试图像和masks
    test_img = create_test_image()
    masks = create_test_masks()
    
    # 创建旧版本效果（随机暗色）
    old_img = test_img.copy()
    np.random.seed(42)  # 固定种子
    for mask in masks:
        old_color = np.random.randint(0, 255, 3).tolist()  # 可能很暗
        old_img[mask > 0] = old_color
    old_result = cv2.addWeighted(test_img, 0.7, old_img, 0.3, 0)
    
    # 创建新版本效果（鲜艳色）
    new_img = test_img.copy()
    bright_colors = generate_bright_colors(len(masks))
    overlay = test_img.copy()
    for i, mask in enumerate(masks):
        color = bright_colors[i]
        overlay[mask > 0] = color
    new_result = cv2.addWeighted(test_img, 0.4, overlay, 0.6, 0)
    
    # 创建对比图
    comparison = np.hstack([old_result, new_result])
    
    # 添加标题
    cv2.putText(comparison, "Old: Random Colors", 
               (20, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
    cv2.putText(comparison, "New: Bright Colors", 
               (320, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
    
    # 保存对比图
    output_dir = os.path.join(ROOT_DIR, 'tools', 'debug', 'clean_visualization_test')
    os.makedirs(output_dir, exist_ok=True)
    comparison_path = os.path.join(output_dir, 'comparison.png')
    cv2.imwrite(comparison_path, comparison)
    
    print(f"✓ Comparison image saved: {comparison_path}")
    return True

def main():
    """运行所有测试"""
    print("=" * 60)
    print("TESTING CLEAN VISUALIZATION")
    print("=" * 60)
    
    all_passed = True
    
    try:
        if not test_color_generation():
            all_passed = False
        
        if not test_clean_visualization():
            all_passed = False
            
        if not create_demo_comparison():
            all_passed = False
        
        if all_passed:
            print("\n" + "=" * 60)
            print("ALL TESTS PASSED! ✓")
            print("=" * 60)
            print("简洁可视化功能正常工作:")
            print("✅ 只保存一个图片文件: {image_name}_result.png")
            print("✅ 使用鲜艳随机颜色")
            print("✅ 无文字标签")
            print("✅ 无边界框")
            print("✅ 清晰简洁的效果")
            print("\n使用方法:")
            print("python tools/inference_with_evaluators.py \\")
            print("    --config configs/mask2former_config.py \\")
            print("    --checkpoint path/to/checkpoint.pth \\")
            print("    --input path/to/images \\")
            print("    --output output/results \\")
            print("    --ann-file path/to/annotations.json \\")
            print("    --evaluators coco")
            
        else:
            print("\n❌ SOME TESTS FAILED!")
            return 1
            
    except Exception as e:
        print(f"\n❌ Test failed with exception: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0

if __name__ == '__main__':
    exit(main())
