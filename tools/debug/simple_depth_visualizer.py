#!/usr/bin/env python3
"""
简单的深度图可视化脚本
"""

import cv2
import numpy as np
import os
import sys

# 设置matplotlib后端
import matplotlib
matplotlib.use('Agg')
import matplotlib.pyplot as plt

def create_output_directory():
    """创建输出目录"""
    output_dir = "output/0_tmp_debug_files/0_depth_map_display_stru3d"
    os.makedirs(output_dir, exist_ok=True)
    print(f"📁 创建输出目录: {output_dir}")
    return output_dir

def find_depth_images(data_root, max_samples=2):
    """查找深度图像文件"""
    depth_files = []
    
    print(f"🔍 搜索深度图像文件...")
    print(f"数据根目录: {data_root}")
    
    if not os.path.exists(data_root):
        print(f"❌ 数据根目录不存在: {data_root}")
        return depth_files
    
    # 遍历查找深度图像
    count = 0
    for root, dirs, files in os.walk(data_root):
        if count >= max_samples:
            break
        for file in files:
            if file == 'depth.png':
                full_path = os.path.join(root, file)
                depth_files.append(full_path)
                print(f"  找到: {full_path}")
                count += 1
                if count >= max_samples:
                    break
    
    print(f"✅ 找到 {len(depth_files)} 个深度图像文件")
    return depth_files

def visualize_single_depth(depth_path, output_dir, index):
    """可视化单个深度图像"""
    print(f"\n--- 处理深度图像 {index} ---")
    print(f"文件路径: {depth_path}")
    
    # 读取深度图像
    depth_img = cv2.imread(depth_path, cv2.IMREAD_ANYDEPTH | cv2.IMREAD_ANYCOLOR)
    
    if depth_img is None:
        print(f"❌ 无法读取深度图像: {depth_path}")
        return False
    
    print(f"图像尺寸: {depth_img.shape}")
    print(f"数据类型: {depth_img.dtype}")
    print(f"深度值范围: [{depth_img.min()}, {depth_img.max()}]")
    
    # 统计有效深度值
    valid_mask = depth_img > 500
    valid_count = np.sum(valid_mask)
    total_count = depth_img.size
    
    print(f"有效深度值: {valid_count}/{total_count} ({valid_count/total_count*100:.1f}%)")
    
    # 创建可视化
    fig, axes = plt.subplots(2, 2, figsize=(12, 10))
    fig.suptitle(f'深度图可视化 - 图像 {index}', fontsize=16)
    
    # 1. 原始灰度图
    axes[0, 0].imshow(depth_img, cmap='gray')
    axes[0, 0].set_title('原始深度图 (灰度)')
    axes[0, 0].axis('off')
    
    # 2. 彩色深度图
    depth_colored = depth_img.copy().astype(np.float32)
    depth_colored[depth_colored <= 500] = np.nan  # 无效值设为NaN
    
    im2 = axes[0, 1].imshow(depth_colored, cmap='jet')
    axes[0, 1].set_title('彩色深度图 (Jet)')
    axes[0, 1].axis('off')
    plt.colorbar(im2, ax=axes[0, 1], fraction=0.046, pad=0.04)
    
    # 3. 增强对比度
    if valid_count > 0:
        valid_depths = depth_img[valid_mask]
        p2, p98 = np.percentile(valid_depths, (2, 98))
        depth_enhanced = np.clip(depth_img.astype(np.float32), p2, p98)
        depth_enhanced = (depth_enhanced - p2) / (p98 - p2)
        
        axes[1, 0].imshow(depth_enhanced, cmap='viridis')
        axes[1, 0].set_title('增强对比度')
        axes[1, 0].axis('off')
    
    # 4. 深度直方图
    if valid_count > 0:
        valid_depths = depth_img[valid_mask]
        axes[1, 1].hist(valid_depths, bins=50, alpha=0.7, edgecolor='black')
        axes[1, 1].set_xlabel('深度值')
        axes[1, 1].set_ylabel('像素数量')
        axes[1, 1].set_title('深度值分布')
        axes[1, 1].grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    # 保存图像
    scene_name = os.path.basename(os.path.dirname(depth_path))
    output_path = os.path.join(output_dir, f'depth_visualization_{index:02d}_{scene_name}.png')
    
    try:
        plt.savefig(output_path, dpi=150, bbox_inches='tight')
        print(f"✅ 保存可视化图像: {output_path}")
        plt.close()
        return True
    except Exception as e:
        print(f"❌ 保存图像时出错: {e}")
        plt.close()
        return False

def create_simple_colormap(depth_img, output_dir, index):
    """创建简单的彩色深度图"""
    try:
        # 使用OpenCV创建彩色深度图
        depth_normalized = depth_img.copy().astype(np.float32)
        
        # 归一化到0-255范围
        valid_mask = depth_normalized > 500
        if np.sum(valid_mask) > 0:
            valid_depths = depth_normalized[valid_mask]
            min_depth = valid_depths.min()
            max_depth = valid_depths.max()
            
            # 归一化
            depth_normalized[valid_mask] = (valid_depths - min_depth) / (max_depth - min_depth) * 255
            depth_normalized[~valid_mask] = 0
            
            # 转换为8位
            depth_8bit = depth_normalized.astype(np.uint8)
            
            # 应用彩色映射
            depth_colored = cv2.applyColorMap(depth_8bit, cv2.COLORMAP_JET)
            
            # 保存彩色深度图
            scene_name = os.path.basename(os.path.dirname(depth_path))
            output_path = os.path.join(output_dir, f'depth_colormap_{index:02d}_{scene_name}.png')
            
            cv2.imwrite(output_path, depth_colored)
            print(f"✅ 保存彩色深度图: {output_path}")
            return True
    
    except Exception as e:
        print(f"❌ 创建彩色深度图时出错: {e}")
        return False

def main():
    print("简单深度图可视化脚本")
    print("=" * 40)
    
    # 创建输出目录
    output_dir = create_output_directory()
    
    # 数据路径
    default_data_root = "/home/<USER>/05_DL_dataset_wuhan/03_开源数据集/structure_3D/Panorama/"
    
    if len(sys.argv) > 1:
        data_root = sys.argv[1]
    else:
        data_root = default_data_root
    
    print(f"数据路径: {data_root}")
    
    # 查找深度图像
    depth_files = find_depth_images(data_root, max_samples=2)
    
    if not depth_files:
        print("❌ 未找到深度图像文件")
        return
    
    # 处理每个深度图像
    success_count = 0
    for i, depth_path in enumerate(depth_files, 1):
        if visualize_single_depth(depth_path, output_dir, i):
            create_simple_colormap(depth_img, output_dir, i)
            success_count += 1
    
    print(f"\n{'='*40}")
    print(f"🎉 处理完成！")
    print(f"成功处理: {success_count}/{len(depth_files)} 个深度图像")
    print(f"输出目录: {output_dir}")
    print(f"{'='*40}")

if __name__ == "__main__":
    main()
