#!/usr/bin/env python3
"""
测试改进的评价器的准确性
验证TP/FP/FN计算逻辑是否正确
"""

import os
import sys
import numpy as np
import json
import tempfile
from unittest.mock import Mock

# Add project root to path
ROOT_DIR = os.path.abspath(os.path.join(os.path.dirname(__file__), os.pardir, os.pardir))
if ROOT_DIR not in sys.path:
    sys.path.insert(0, ROOT_DIR)

# Mock the dependencies that might not be available
sys.modules['cv2'] = Mock()
sys.modules['torch'] = Mock()
sys.modules['mmdet'] = Mock()
sys.modules['mmdet.apis'] = Mock()
sys.modules['mmengine'] = Mock()
sys.modules['mmengine.config'] = Mock()

# Mock pycocotools
from unittest.mock import MagicMock
pycocotools_mock = MagicMock()
pycocotools_mock.mask.encode = lambda x: {'counts': b'test', 'size': [100, 100]}
pycocotools_mock.mask.decode = lambda x: np.ones((100, 100), dtype=np.uint8)
pycocotools_mock.mask.area = lambda x: 1000
pycocotools_mock.mask.frPyObjects = lambda p, h, w: [{'counts': b'test', 'size': [h, w]}]
pycocotools_mock.mask.merge = lambda x: {'counts': b'test', 'size': [100, 100]}
sys.modules['pycocotools'] = pycocotools_mock
sys.modules['pycocotools.coco'] = pycocotools_mock
sys.modules['pycocotools.cocoeval'] = pycocotools_mock
sys.modules['pycocotools.mask'] = pycocotools_mock.mask

def create_test_mask(shape, region):
    """创建测试用的mask"""
    mask = np.zeros(shape, dtype=np.uint8)
    x1, y1, x2, y2 = region
    mask[y1:y2, x1:x2] = 1
    return mask

def test_iou_calculation():
    """测试IoU计算的准确性"""
    print("Testing IoU calculation...")
    
    # 创建两个重叠的mask
    mask1 = create_test_mask((100, 100), (10, 10, 50, 50))  # 40x40 = 1600 pixels
    mask2 = create_test_mask((100, 100), (30, 30, 70, 70))  # 40x40 = 1600 pixels
    
    # 计算预期的IoU
    # 交集: (30,30) to (50,50) = 20x20 = 400 pixels
    # 并集: 1600 + 1600 - 400 = 2800 pixels
    # IoU = 400 / 2800 = 0.1429
    expected_iou = 400 / 2800
    
    # 导入我们的评价器类
    from tools.inference_with_evaluators import InferenceCocoEvaluator
    
    # 创建一个简单的评价器实例来测试IoU计算
    evaluator = InferenceCocoEvaluator.__new__(InferenceCocoEvaluator)
    calculated_iou = evaluator._compute_mask_iou(mask1, mask2)
    
    print(f"Expected IoU: {expected_iou:.4f}")
    print(f"Calculated IoU: {calculated_iou:.4f}")
    print(f"Difference: {abs(expected_iou - calculated_iou):.6f}")
    
    assert abs(expected_iou - calculated_iou) < 1e-6, f"IoU calculation error: expected {expected_iou}, got {calculated_iou}"
    print("✓ IoU calculation test passed!")

def test_tp_fp_fn_logic():
    """测试TP/FP/FN计算逻辑"""
    print("\nTesting TP/FP/FN calculation logic...")
    
    # 创建测试场景
    # 3个预测，2个GT，1个匹配
    pred_masks = [
        {'mask': create_test_mask((100, 100), (10, 10, 50, 50)), 'score': 0.9, 'category_id': 1},  # 应该匹配GT1
        {'mask': create_test_mask((100, 100), (60, 60, 90, 90)), 'score': 0.8, 'category_id': 1},  # 应该是FP
        {'mask': create_test_mask((100, 100), (20, 20, 40, 40)), 'score': 0.7, 'category_id': 1},  # 应该匹配GT1但IoU较低
    ]
    
    gt_masks = [
        {'mask': create_test_mask((100, 100), (15, 15, 45, 45)), 'category_id': 1},  # 应该匹配pred1
        {'mask': create_test_mask((100, 100), (70, 70, 90, 90)), 'category_id': 1},  # 应该是FN
    ]
    
    # 模拟计算过程
    iou_threshold = 0.5
    iou_matrix = np.zeros((len(pred_masks), len(gt_masks)))
    
    # 手动计算IoU矩阵
    from tools.inference_with_evaluators import InferenceCocoEvaluator
    evaluator = InferenceCocoEvaluator.__new__(InferenceCocoEvaluator)
    
    for i, pred in enumerate(pred_masks):
        for j, gt in enumerate(gt_masks):
            if pred['category_id'] == gt['category_id']:
                iou = evaluator._compute_mask_iou(pred['mask'], gt['mask'])
                iou_matrix[i, j] = iou
                print(f"IoU between pred{i} and gt{j}: {iou:.4f}")
    
    # 执行贪心匹配
    tp = 0
    fp = 0
    matched_gt = set()
    
    # 按分数排序（已经排序）
    for i, pred in enumerate(pred_masks):
        best_iou = 0
        best_j = -1
        
        for j in range(len(gt_masks)):
            if j not in matched_gt and iou_matrix[i, j] >= iou_threshold:
                if iou_matrix[i, j] > best_iou:
                    best_iou = iou_matrix[i, j]
                    best_j = j
        
        if best_j >= 0:
            tp += 1
            matched_gt.add(best_j)
            print(f"Pred{i} matched with GT{best_j} (IoU: {best_iou:.4f}) -> TP")
        else:
            fp += 1
            print(f"Pred{i} no match -> FP")
    
    fn = len(gt_masks) - len(matched_gt)
    print(f"Unmatched GTs: {fn} -> FN")
    
    print(f"\nFinal counts: TP={tp}, FP={fp}, FN={fn}")
    
    # 验证结果
    expected_tp = 1  # 只有pred0能匹配gt0
    expected_fp = 2  # pred1和pred2都是FP
    expected_fn = 1  # gt1没有匹配
    
    assert tp == expected_tp, f"TP count error: expected {expected_tp}, got {tp}"
    assert fp == expected_fp, f"FP count error: expected {expected_fp}, got {fp}"
    assert fn == expected_fn, f"FN count error: expected {expected_fn}, got {fn}"
    
    print("✓ TP/FP/FN calculation test passed!")

def test_precision_recall_calculation():
    """测试精确率和召回率计算"""
    print("\nTesting precision and recall calculation...")
    
    # 使用上面的测试结果
    tp, fp, fn = 1, 2, 1
    
    expected_precision = tp / (tp + fp)  # 1 / (1 + 2) = 0.3333
    expected_recall = tp / (tp + fn)     # 1 / (1 + 1) = 0.5
    
    precision = tp / (tp + fp) if (tp + fp) > 0 else 0.0
    recall = tp / (tp + fn) if (tp + fn) > 0 else 0.0
    
    print(f"Expected precision: {expected_precision:.4f}")
    print(f"Calculated precision: {precision:.4f}")
    print(f"Expected recall: {expected_recall:.4f}")
    print(f"Calculated recall: {recall:.4f}")
    
    assert abs(precision - expected_precision) < 1e-6, f"Precision error: expected {expected_precision}, got {precision}"
    assert abs(recall - expected_recall) < 1e-6, f"Recall error: expected {expected_recall}, got {recall}"
    
    print("✓ Precision and recall calculation test passed!")

def main():
    """运行所有测试"""
    print("=" * 60)
    print("TESTING IMPROVED EVALUATOR ACCURACY")
    print("=" * 60)
    
    try:
        test_iou_calculation()
        test_tp_fp_fn_logic()
        test_precision_recall_calculation()
        
        print("\n" + "=" * 60)
        print("ALL TESTS PASSED! ✓")
        print("The improved evaluator logic is working correctly.")
        print("=" * 60)
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0

if __name__ == '__main__':
    exit(main())
