#!/usr/bin/env python3
"""
测试缺失文件修复效果的脚本

验证即使缺少camera_xyz.txt文件也能正常生成点云
"""

import os
import sys
import tempfile
import shutil

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'dataset_converters', 'stru3d'))

def test_missing_camera_file():
    """测试缺少camera_xyz.txt文件的处理"""
    print("🧪 测试缺少camera_xyz.txt文件的处理...")
    
    try:
        from PointCloudReaderPanorama import PointCloudReaderPanorama
        
        # 创建临时测试目录结构
        with tempfile.TemporaryDirectory() as temp_dir:
            scene_path = os.path.join(temp_dir, "test_scene")
            rendering_path = os.path.join(scene_path, "2D_rendering")
            room_path = os.path.join(rendering_path, "1083")
            panorama_path = os.path.join(room_path, "panorama")
            
            # 创建目录结构
            os.makedirs(panorama_path, exist_ok=True)
            
            # 创建必要的图像文件（空文件用于测试）
            import numpy as np
            import cv2
            
            # 创建模拟的深度图像
            depth_img = np.random.randint(500, 3000, size=(100, 200)).astype(np.uint16)
            cv2.imwrite(os.path.join(panorama_path, "depth.png"), depth_img)
            
            # 创建模拟的RGB图像
            rgb_img = np.random.randint(0, 255, size=(100, 200, 3)).astype(np.uint8)
            cv2.imwrite(os.path.join(panorama_path, "rgb_coldlight.png"), rgb_img)
            
            # 创建模拟的法线图像
            normal_img = np.random.randint(0, 255, size=(100, 200, 3)).astype(np.uint8)
            cv2.imwrite(os.path.join(panorama_path, "normal.png"), normal_img)
            
            # 故意不创建camera_xyz.txt文件
            camera_file = os.path.join(panorama_path, "camera_xyz.txt")
            print(f"   故意不创建: {camera_file}")
            
            print(f"   测试场景路径: {scene_path}")
            print(f"   2D_rendering存在: {os.path.exists(rendering_path)}")
            print(f"   camera_xyz.txt存在: {os.path.exists(camera_file)}")
            
            # 尝试创建PointCloudReaderPanorama实例
            try:
                reader = PointCloudReaderPanorama(
                    scene_path,
                    random_level=0.1,
                    generate_color=True,
                    generate_normal=False,
                    remove_duplicates=True,
                    downsample_ratio=1.0,
                    use_vectorized=True
                )
                
                print("   ✅ 成功创建PointCloudReaderPanorama实例")
                print(f"   相机中心数量: {len(reader.camera_centers)}")
                print(f"   相机中心值: {reader.camera_centers}")
                
                # 尝试生成点云
                points = reader.point_cloud
                if points and 'coords' in points:
                    print(f"   ✅ 成功生成点云，点数: {len(points['coords'])}")
                    return True
                else:
                    print("   ❌ 点云生成失败")
                    return False
                    
            except Exception as e:
                print(f"   ❌ 创建实例失败: {e}")
                return False
                
    except ImportError as e:
        print(f"   ❌ 导入失败: {e}")
        return False
    except Exception as e:
        print(f"   ❌ 测试失败: {e}")
        return False

def test_with_camera_file():
    """测试有camera_xyz.txt文件的正常情况"""
    print("\n🧪 测试有camera_xyz.txt文件的正常情况...")
    
    try:
        from PointCloudReaderPanorama import PointCloudReaderPanorama
        
        # 创建临时测试目录结构
        with tempfile.TemporaryDirectory() as temp_dir:
            scene_path = os.path.join(temp_dir, "test_scene")
            rendering_path = os.path.join(scene_path, "2D_rendering")
            room_path = os.path.join(rendering_path, "1083")
            panorama_path = os.path.join(room_path, "panorama")
            
            # 创建目录结构
            os.makedirs(panorama_path, exist_ok=True)
            
            # 创建必要的图像文件
            import numpy as np
            import cv2
            
            # 创建模拟的深度图像
            depth_img = np.random.randint(500, 3000, size=(100, 200)).astype(np.uint16)
            cv2.imwrite(os.path.join(panorama_path, "depth.png"), depth_img)
            
            # 创建模拟的RGB图像
            rgb_img = np.random.randint(0, 255, size=(100, 200, 3)).astype(np.uint8)
            cv2.imwrite(os.path.join(panorama_path, "rgb_coldlight.png"), rgb_img)
            
            # 创建模拟的法线图像
            normal_img = np.random.randint(0, 255, size=(100, 200, 3)).astype(np.uint8)
            cv2.imwrite(os.path.join(panorama_path, "normal.png"), normal_img)
            
            # 创建camera_xyz.txt文件
            camera_file = os.path.join(panorama_path, "camera_xyz.txt")
            with open(camera_file, 'w') as f:
                f.write("1.5 2.0 1.8\n")  # 示例相机中心坐标
            
            print(f"   创建camera_xyz.txt: {camera_file}")
            print(f"   内容: 1.5 2.0 1.8")
            
            # 尝试创建PointCloudReaderPanorama实例
            try:
                reader = PointCloudReaderPanorama(
                    scene_path,
                    random_level=0.1,
                    generate_color=True,
                    generate_normal=False,
                    remove_duplicates=True,
                    downsample_ratio=1.0,
                    use_vectorized=True
                )
                
                print("   ✅ 成功创建PointCloudReaderPanorama实例")
                print(f"   相机中心数量: {len(reader.camera_centers)}")
                print(f"   相机中心值: {reader.camera_centers}")
                
                # 尝试生成点云
                points = reader.point_cloud
                if points and 'coords' in points:
                    print(f"   ✅ 成功生成点云，点数: {len(points['coords'])}")
                    return True
                else:
                    print("   ❌ 点云生成失败")
                    return False
                    
            except Exception as e:
                print(f"   ❌ 创建实例失败: {e}")
                return False
                
    except Exception as e:
        print(f"   ❌ 测试失败: {e}")
        return False

def main():
    print("缺失文件修复效果测试")
    print("=" * 50)
    
    # 测试两种情况
    test1_result = test_missing_camera_file()
    test2_result = test_with_camera_file()
    
    print("\n" + "=" * 50)
    print("🎯 测试结果总结")
    print("=" * 50)
    print(f"缺少camera_xyz.txt文件: {'✅ 通过' if test1_result else '❌ 失败'}")
    print(f"有camera_xyz.txt文件: {'✅ 通过' if test2_result else '❌ 失败'}")
    
    if test1_result and test2_result:
        print("\n🎉 所有测试通过！")
        print("现在可以处理缺少camera_xyz.txt文件的场景了")
        print("\n建议运行命令:")
        print("python generate_point_cloud_stru3d.py --num_workers 16 --use_vectorized True --max_scenes 4")
    else:
        print("\n❌ 部分测试失败，需要进一步调试")

if __name__ == "__main__":
    main()
