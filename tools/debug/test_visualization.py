#!/usr/bin/env python3
"""
测试可视化功能的脚本
"""

import os
import sys
import numpy as np
import cv2
from unittest.mock import Mock

# Add project root to path
ROOT_DIR = os.path.abspath(os.path.join(os.path.dirname(__file__), os.pardir, os.pardir))
if ROOT_DIR not in sys.path:
    sys.path.insert(0, ROOT_DIR)

def create_test_image():
    """创建一个测试图像"""
    # 创建一个简单的测试图像 (300x300, 3通道)
    img = np.ones((300, 300, 3), dtype=np.uint8) * 128  # 灰色背景
    
    # 添加一些简单的图案
    cv2.rectangle(img, (50, 50), (150, 150), (200, 200, 200), -1)  # 浅灰色矩形
    cv2.circle(img, (200, 200), 50, (180, 180, 180), -1)  # 浅灰色圆形
    
    return img

def create_test_masks():
    """创建测试用的masks"""
    masks = []
    
    # Mask 1: 矩形区域
    mask1 = np.zeros((300, 300), dtype=np.uint8)
    mask1[60:140, 60:140] = 1
    masks.append(mask1)
    
    # Mask 2: 圆形区域
    mask2 = np.zeros((300, 300), dtype=np.uint8)
    cv2.circle(mask2, (200, 200), 40, 1, -1)
    masks.append(mask2)
    
    # Mask 3: 不规则区域
    mask3 = np.zeros((300, 300), dtype=np.uint8)
    points = np.array([[100, 250], [150, 220], [200, 250], [180, 280], [120, 280]], np.int32)
    cv2.fillPoly(mask3, [points], 1)
    masks.append(mask3)
    
    return np.array(masks)

def create_mock_pred_instances():
    """创建模拟的预测实例"""
    masks = create_test_masks()
    scores = np.array([0.95, 0.87, 0.76])
    labels = np.array([1, 2, 1])
    
    # 创建mock对象
    pred_instances = Mock()
    pred_instances.masks = Mock()
    pred_instances.masks.cpu.return_value.numpy.return_value = masks
    pred_instances.scores = Mock()
    pred_instances.scores.cpu.return_value.numpy.return_value = scores
    pred_instances.labels = Mock()
    pred_instances.labels.cpu.return_value.numpy.return_value = labels
    
    return pred_instances

def test_color_generation():
    """测试颜色生成功能"""
    print("Testing color generation...")
    
    # 导入颜色生成函数
    sys.path.insert(0, os.path.join(ROOT_DIR, 'tools'))
    from inference_with_evaluators import generate_bright_colors
    
    # 测试生成不同数量的颜色
    for num_colors in [5, 10, 25]:
        colors = generate_bright_colors(num_colors)
        print(f"Generated {len(colors)} colors for {num_colors} requested")
        
        # 验证颜色数量
        assert len(colors) == num_colors, f"Expected {num_colors} colors, got {len(colors)}"
        
        # 验证颜色格式 (BGR)
        for i, color in enumerate(colors[:5]):  # 只显示前5个
            assert len(color) == 3, f"Color {i} should have 3 components"
            assert all(0 <= c <= 255 for c in color), f"Color {i} values should be in [0, 255]"
            print(f"  Color {i}: BGR{color}")
    
    print("✓ Color generation test passed!")

def test_visualization_functions():
    """测试可视化函数"""
    print("\nTesting visualization functions...")
    
    # 导入可视化函数
    from inference_with_evaluators import create_enhanced_visualization, create_detailed_visualization
    
    # 创建测试数据
    test_img = create_test_image()
    pred_instances = create_mock_pred_instances()
    
    # 测试增强可视化
    enhanced_img = create_enhanced_visualization(test_img, pred_instances)
    assert enhanced_img.shape == test_img.shape, "Enhanced image should have same shape as input"
    print("✓ Enhanced visualization created successfully")
    
    # 测试详细可视化
    detailed_img = create_detailed_visualization(test_img, pred_instances)
    assert detailed_img.shape == test_img.shape, "Detailed image should have same shape as input"
    print("✓ Detailed visualization created successfully")
    
    # 保存测试结果
    output_dir = os.path.join(ROOT_DIR, 'tools', 'debug', 'visualization_test_output')
    os.makedirs(output_dir, exist_ok=True)
    
    cv2.imwrite(os.path.join(output_dir, 'test_original.png'), test_img)
    cv2.imwrite(os.path.join(output_dir, 'test_enhanced.png'), enhanced_img)
    cv2.imwrite(os.path.join(output_dir, 'test_detailed.png'), detailed_img)
    
    print(f"✓ Test images saved to: {output_dir}")
    print("  - test_original.png: Original test image")
    print("  - test_enhanced.png: Enhanced visualization with bright colors")
    print("  - test_detailed.png: Detailed visualization with boxes and contours")

def test_edge_cases():
    """测试边界情况"""
    print("\nTesting edge cases...")
    
    from inference_with_evaluators import create_enhanced_visualization
    
    test_img = create_test_image()
    
    # 测试空的预测实例
    empty_pred = Mock()
    empty_pred.masks = Mock()
    empty_pred.masks.cpu.return_value.numpy.return_value = np.array([])
    empty_pred.scores = Mock()
    empty_pred.scores.cpu.return_value.numpy.return_value = np.array([])
    empty_pred.labels = Mock()
    empty_pred.labels.cpu.return_value.numpy.return_value = np.array([])
    
    result_img = create_enhanced_visualization(test_img, empty_pred)
    assert np.array_equal(result_img, test_img), "Empty predictions should return original image"
    print("✓ Empty predictions handled correctly")
    
    # 测试单个mask
    single_mask = np.zeros((300, 300), dtype=np.uint8)
    single_mask[100:200, 100:200] = 1
    
    single_pred = Mock()
    single_pred.masks = Mock()
    single_pred.masks.cpu.return_value.numpy.return_value = np.array([single_mask])
    single_pred.scores = Mock()
    single_pred.scores.cpu.return_value.numpy.return_value = np.array([0.9])
    single_pred.labels = Mock()
    single_pred.labels.cpu.return_value.numpy.return_value = np.array([1])
    
    result_img = create_enhanced_visualization(test_img, single_pred)
    assert result_img.shape == test_img.shape, "Single mask should work correctly"
    print("✓ Single mask handled correctly")

def main():
    """运行所有测试"""
    print("=" * 60)
    print("TESTING VISUALIZATION IMPROVEMENTS")
    print("=" * 60)
    
    try:
        test_color_generation()
        test_visualization_functions()
        test_edge_cases()
        
        print("\n" + "=" * 60)
        print("ALL VISUALIZATION TESTS PASSED! ✓")
        print("The enhanced visualization system is working correctly.")
        print("=" * 60)
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0

if __name__ == '__main__':
    exit(main())
