#!/usr/bin/env python3
"""
测试点云连续性增强效果的脚本

比较不同增强方法的效果
"""

import numpy as np
import sys
import os

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'dataset_converters', 'stru3d'))

def simulate_discontinuous_pointcloud():
    """模拟不连续的点云数据（类似原始问题）"""
    np.random.seed(42)
    
    # 创建模拟的深度图像数据（整数类型，模拟真实情况）
    depth_img = np.random.randint(1000, 3000, size=(50, 100)).astype(np.uint16)
    
    coords = []
    colors = []
    
    # 模拟点云生成过程
    camera_center = np.array([0, 0, 0])
    x_tick = 180.0 / depth_img.shape[0]
    y_tick = 360.0 / depth_img.shape[1]
    
    for x in range(0, depth_img.shape[0], 2):  # 降采样
        for y in range(0, depth_img.shape[1], 2):
            alpha = 90 - (x * x_tick)
            beta = y * y_tick - 180
            
            # 使用整数深度值（模拟原始问题）
            depth = float(depth_img[x, y])
            
            if depth > 500:
                z_offset = depth * np.sin(np.deg2rad(alpha))
                xy_offset = depth * np.cos(np.deg2rad(alpha))
                x_offset = xy_offset * np.sin(np.deg2rad(beta))
                y_offset = xy_offset * np.cos(np.deg2rad(beta))
                
                point = np.array([x_offset, y_offset, z_offset])
                coords.append(point + camera_center)
                colors.append([np.random.random(), np.random.random(), np.random.random()])
    
    return np.array(coords), np.array(colors)

def analyze_continuity(coords, method_name):
    """分析点云连续性"""
    if len(coords) == 0:
        return None
    
    z_coords = coords[:, 2]
    unique_z = np.unique(z_coords)
    
    # 计算连续性指标
    continuity_ratio = len(unique_z) / len(coords)
    
    if len(unique_z) > 1:
        z_diffs = np.diff(np.sort(unique_z))
        min_interval = z_diffs.min()
        mean_interval = z_diffs.mean()
        max_interval = z_diffs.max()
    else:
        min_interval = mean_interval = max_interval = 0
    
    result = {
        'method': method_name,
        'total_points': len(coords),
        'unique_z_count': len(unique_z),
        'continuity_ratio': continuity_ratio,
        'min_interval': min_interval,
        'mean_interval': mean_interval,
        'max_interval': max_interval
    }
    
    return result

def test_gaussian_enhancement(coords, colors):
    """测试高斯噪声增强"""
    enhanced_coords = []
    enhanced_colors = []
    
    noise_samples = 3
    noise_std = 0.5
    
    for coord, color in zip(coords, colors):
        # 保留原始点
        enhanced_coords.append(coord)
        enhanced_colors.append(color)
        
        # 添加噪声副本
        for _ in range(noise_samples):
            noise = np.random.normal(0, [noise_std, noise_std, noise_std * 0.3], 3)
            noisy_coord = coord + noise
            enhanced_coords.append(noisy_coord)
            enhanced_colors.append(color)
    
    return np.array(enhanced_coords), np.array(enhanced_colors)

def test_linear_interpolation(coords, colors):
    """测试线性插值增强"""
    if len(coords) < 2:
        return coords, colors
    
    enhanced_coords = list(coords)
    enhanced_colors = list(colors)
    
    # 按z坐标排序
    z_indices = np.argsort(coords[:, 2])
    sorted_coords = coords[z_indices]
    sorted_colors = colors[z_indices]
    
    # 在相邻点之间插值
    for i in range(len(sorted_coords) - 1):
        coord1, coord2 = sorted_coords[i], sorted_coords[i + 1]
        color1, color2 = sorted_colors[i], sorted_colors[i + 1]
        
        distance = np.linalg.norm(coord2 - coord1)
        
        if distance > 2.0:
            num_interpolations = min(int(distance / 1.0), 5)
            
            for j in range(1, num_interpolations + 1):
                t = j / (num_interpolations + 1)
                interp_coord = coord1 + t * (coord2 - coord1)
                interp_color = color1 + t * (color2 - color1)
                
                enhanced_coords.append(interp_coord)
                enhanced_colors.append(interp_color)
    
    return np.array(enhanced_coords), np.array(enhanced_colors)

def test_combined_enhancement(coords, colors):
    """测试组合增强方法"""
    # 先进行线性插值
    coords_interp, colors_interp = test_linear_interpolation(coords, colors)
    
    # 再进行高斯噪声增强（参数减小以避免过度增强）
    enhanced_coords = []
    enhanced_colors = []
    
    noise_samples = 2  # 减少噪声样本
    noise_std = 0.3    # 减小噪声强度
    
    for coord, color in zip(coords_interp, colors_interp):
        enhanced_coords.append(coord)
        enhanced_colors.append(color)
        
        for _ in range(noise_samples):
            noise = np.random.normal(0, [noise_std, noise_std, noise_std * 0.2], 3)
            noisy_coord = coord + noise
            enhanced_coords.append(noisy_coord)
            enhanced_colors.append(color)
    
    return np.array(enhanced_coords), np.array(enhanced_colors)

def main():
    print("点云连续性增强效果测试")
    print("=" * 50)
    
    # 生成模拟的不连续点云
    print("🔧 生成模拟的不连续点云...")
    original_coords, original_colors = simulate_discontinuous_pointcloud()
    
    # 测试不同的增强方法
    methods = {
        "原始点云": (original_coords, original_colors),
        "高斯噪声增强": test_gaussian_enhancement(original_coords, original_colors),
        "线性插值增强": test_linear_interpolation(original_coords, original_colors),
        "组合增强": test_combined_enhancement(original_coords, original_colors)
    }
    
    # 分析每种方法的效果
    results = []
    print(f"\n📊 连续性分析结果:")
    print("-" * 80)
    print(f"{'方法':<15} {'总点数':<8} {'唯一Z值':<8} {'连续性':<8} {'最小间隔':<12} {'平均间隔':<12}")
    print("-" * 80)
    
    for method_name, (coords, colors) in methods.items():
        result = analyze_continuity(coords, method_name)
        if result:
            results.append(result)
            print(f"{result['method']:<15} {result['total_points']:<8} {result['unique_z_count']:<8} "
                  f"{result['continuity_ratio']:<8.3f} {result['min_interval']:<12.6f} {result['mean_interval']:<12.6f}")
    
    # 找出最佳方法
    if results:
        best_method = max(results, key=lambda x: x['continuity_ratio'])
        print(f"\n🏆 最佳方法: {best_method['method']}")
        print(f"   连续性提升: {best_method['continuity_ratio']:.3f}")
        print(f"   点云数量: {best_method['total_points']}")
        print(f"   最小间隔: {best_method['min_interval']:.6f}")
    
    # 计算改善效果
    if len(results) >= 2:
        original_result = results[0]  # 原始点云
        print(f"\n📈 改善效果对比:")
        
        for result in results[1:]:
            continuity_improvement = result['continuity_ratio'] - original_result['continuity_ratio']
            point_increase = result['total_points'] - original_result['total_points']
            
            print(f"   {result['method']}:")
            print(f"     连续性改善: +{continuity_improvement:.3f}")
            print(f"     点云增加: +{point_increase}")
            
            if result['min_interval'] > 0 and original_result['min_interval'] > 0:
                interval_improvement = (original_result['min_interval'] - result['min_interval']) / original_result['min_interval'] * 100
                print(f"     间隔改善: {interval_improvement:+.1f}%")
    
    print(f"\n🎯 推荐配置:")
    print(f"   方法: 高斯噪声增强 (最稳定)")
    print(f"   参数: random_level=0.5, noise_samples=3, noise_std=0.5")
    print(f"   或者: 组合增强 (效果最佳但计算量大)")
    
    print(f"\n✅ 测试完成！现在可以运行修改后的 generate_point_cloud_stru3d.py")

if __name__ == "__main__":
    main()
