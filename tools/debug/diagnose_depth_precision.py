#!/usr/bin/env python3
"""
诊断深度图像精度问题的脚本

检查深度图像的数据类型、精度和分布，找出z轴不连续的根本原因
"""

import cv2
import numpy as np
import matplotlib.pyplot as plt
import os
import sys

def analyze_depth_image(depth_path):
    """分析单个深度图像"""
    print(f"\n=== 分析深度图像: {os.path.basename(depth_path)} ===")
    
    if not os.path.exists(depth_path):
        print(f"❌ 文件不存在: {depth_path}")
        return None
    
    # 读取深度图像
    depth_img = cv2.imread(depth_path, cv2.IMREAD_ANYDEPTH | cv2.IMREAD_ANYCOLOR)
    
    if depth_img is None:
        print(f"❌ 无法读取深度图像: {depth_path}")
        return None
    
    print(f"图像尺寸: {depth_img.shape}")
    print(f"数据类型: {depth_img.dtype}")
    print(f"深度值范围: [{depth_img.min()}, {depth_img.max()}]")
    
    # 检查有效深度值（>500的值）
    valid_mask = depth_img > 500
    valid_depths = depth_img[valid_mask]
    
    if len(valid_depths) == 0:
        print("⚠️  没有找到有效的深度值（>500）")
        return None
    
    print(f"有效深度值数量: {len(valid_depths)} / {depth_img.size} ({len(valid_depths)/depth_img.size*100:.1f}%)")
    print(f"有效深度值范围: [{valid_depths.min()}, {valid_depths.max()}]")
    
    # 检查深度值的精度和分布
    unique_depths = np.unique(valid_depths)
    print(f"唯一深度值数量: {len(unique_depths)}")
    
    if len(unique_depths) > 1:
        depth_diffs = np.diff(np.sort(unique_depths))
        min_diff = depth_diffs.min()
        max_diff = depth_diffs.max()
        mean_diff = depth_diffs.mean()
        
        print(f"深度值间隔统计:")
        print(f"  最小间隔: {min_diff}")
        print(f"  最大间隔: {max_diff}")
        print(f"  平均间隔: {mean_diff:.6f}")
        
        # 检查是否存在量化模式
        if depth_img.dtype == np.uint8:
            print("⚠️  深度图像是8位整数，精度有限")
        elif depth_img.dtype == np.uint16:
            print("✅ 深度图像是16位整数，精度较好")
        elif depth_img.dtype == np.float32:
            print("✅ 深度图像是32位浮点数，精度很好")
        else:
            print(f"ℹ️  深度图像数据类型: {depth_img.dtype}")
        
        # 检查是否存在整数量化
        if np.all(valid_depths == valid_depths.astype(int)):
            print("⚠️  所有深度值都是整数，可能存在精度截断")
        else:
            print("✅ 深度值包含小数部分，精度正常")
    
    return {
        'depth_img': depth_img,
        'valid_depths': valid_depths,
        'unique_depths': unique_depths,
        'dtype': depth_img.dtype
    }

def simulate_point_generation(depth_img, camera_center=None):
    """模拟点云生成过程，检查z坐标分布"""
    print(f"\n=== 模拟点云生成过程 ===")
    
    if camera_center is None:
        camera_center = np.array([0, 0, 0])
    
    x_tick = 180.0 / depth_img.shape[0]
    y_tick = 360.0 / depth_img.shape[1]
    
    z_coords = []
    
    # 只处理一小部分像素来快速检查
    step = max(1, depth_img.shape[0] // 20)
    
    for x in range(0, depth_img.shape[0], step):
        for y in range(0, depth_img.shape[1], step):
            alpha = 90 - (x * x_tick)
            beta = y * y_tick - 180
            
            depth = depth_img[x, y]
            
            if depth > 500:
                z_offset = depth * np.sin(np.deg2rad(alpha))
                final_z = z_offset + camera_center[2]
                z_coords.append(final_z)
    
    if len(z_coords) == 0:
        print("❌ 没有生成任何有效的z坐标")
        return None
    
    z_coords = np.array(z_coords)
    print(f"生成的z坐标数量: {len(z_coords)}")
    print(f"z坐标范围: [{z_coords.min():.6f}, {z_coords.max():.6f}]")
    
    # 检查z坐标的分布
    unique_z = np.unique(z_coords)
    print(f"唯一z坐标数量: {len(unique_z)}")
    
    if len(unique_z) > 1:
        z_diffs = np.diff(np.sort(unique_z))
        min_diff = z_diffs.min()
        max_diff = z_diffs.max()
        mean_diff = z_diffs.mean()
        
        print(f"z坐标间隔统计:")
        print(f"  最小间隔: {min_diff:.6f}")
        print(f"  最大间隔: {max_diff:.6f}")
        print(f"  平均间隔: {mean_diff:.6f}")
        
        # 检查是否存在分层问题
        if min_diff > 10:
            print("⚠️  检测到z坐标间隔过大，可能存在分层问题")
        else:
            print("✅ z坐标间隔正常")
    
    return z_coords

def find_sample_depth_images(data_root):
    """查找示例深度图像文件"""
    depth_files = []
    
    if not os.path.exists(data_root):
        print(f"❌ 数据根目录不存在: {data_root}")
        return depth_files
    
    # 遍历查找深度图像
    for root, dirs, files in os.walk(data_root):
        for file in files:
            if file == 'depth.png':
                depth_files.append(os.path.join(root, file))
                if len(depth_files) >= 3:  # 只取前3个作为示例
                    break
        if len(depth_files) >= 3:
            break
    
    return depth_files

def test_unique_effect():
    """测试np.unique操作对点云连续性的影响"""
    print("\n=== 测试np.unique操作的影响 ===")

    # 创建测试数据：连续的z坐标
    np.random.seed(42)
    n_points = 1000
    coords = np.random.randn(n_points, 3) * 10
    coords[:, 2] += 1000  # z轴偏移

    print(f"原始点云数量: {len(coords)}")
    print(f"原始z坐标范围: [{coords[:, 2].min():.6f}, {coords[:, 2].max():.6f}]")

    unique_z_original = np.unique(coords[:, 2])
    print(f"原始唯一z坐标数量: {len(unique_z_original)}")

    # 应用np.unique操作
    unique_coords, unique_ind = np.unique(coords, return_index=True, axis=0)
    coords_after_unique = coords[unique_ind]

    print(f"np.unique后点云数量: {len(coords_after_unique)}")
    print(f"np.unique后z坐标范围: [{coords_after_unique[:, 2].min():.6f}, {coords_after_unique[:, 2].max():.6f}]")

    unique_z_after = np.unique(coords_after_unique[:, 2])
    print(f"np.unique后唯一z坐标数量: {len(unique_z_after)}")

    reduction_ratio = len(coords_after_unique) / len(coords)
    print(f"点云数量减少比例: {reduction_ratio:.3f} ({(1-reduction_ratio)*100:.1f}% 被移除)")

    if reduction_ratio < 0.9:
        print("⚠️  np.unique操作显著减少了点云数量，可能影响连续性")
    else:
        print("✅ np.unique操作对点云数量影响较小")

def main():
    print("深度图像精度诊断脚本")
    print("=" * 50)

    # 首先测试np.unique的影响
    test_unique_effect()

    # 默认数据路径（您可以修改这个路径）
    default_data_root = "/home/<USER>/05_DL_dataset_wuhan/03_开源数据集/structure_3D/Panorama/"

    if len(sys.argv) > 1:
        data_root = sys.argv[1]
    else:
        data_root = default_data_root
        print(f"\n使用默认数据路径: {data_root}")
        print("您也可以通过命令行参数指定路径: python diagnose_depth_precision.py <数据路径>")

    # 查找示例深度图像
    depth_files = find_sample_depth_images(data_root)

    if not depth_files:
        print("❌ 未找到深度图像文件")
        print("请确保数据路径正确，并且包含 depth.png 文件")
        print("\n但是我们已经完成了np.unique效果的测试")
        return

    print(f"找到 {len(depth_files)} 个深度图像文件")

    # 分析每个深度图像
    for depth_file in depth_files:
        result = analyze_depth_image(depth_file)
        if result:
            # 模拟点云生成
            z_coords = simulate_point_generation(result['depth_img'])

    print("\n" + "=" * 50)
    print("诊断完成！")
    print("可能的问题原因：")
    print("1. 深度图像的数据类型和精度限制")
    print("2. 深度值被量化为整数")
    print("3. np.unique操作移除了重复坐标")
    print("4. 点云生成过程中的数学计算精度")
    print("\n建议的解决方案：")
    print("1. 检查深度图像是否为浮点类型")
    print("2. 考虑移除或修改np.unique操作")
    print("3. 增加随机噪声来避免完全重复的坐标")

if __name__ == "__main__":
    main()
