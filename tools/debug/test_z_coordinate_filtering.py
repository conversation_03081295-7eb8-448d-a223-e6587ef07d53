#!/usr/bin/env python3
"""
测试Z坐标过滤功能

验证：
1. Z坐标过滤逻辑的正确性
2. 过滤前后点云数量的变化
3. 过滤参数的合理性
"""

import os
import sys
import numpy as np
import tempfile

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'dataset_converters'))

def create_test_point_cloud():
    """创建测试点云数据"""
    # 创建一个包含不同Z层的点云
    np.random.seed(42)
    
    # 地面层点 (Z: 0-0.1)
    ground_points = np.random.rand(1000, 3) * [10, 10, 0.1]
    
    # 主要房间点 (Z: 0.5-2.5)
    room_points = np.random.rand(5000, 3) * [10, 10, 2.0] + [0, 0, 0.5]
    
    # 天花板层点 (Z: 2.8-3.0)
    ceiling_points = np.random.rand(800, 3) * [10, 10, 0.2] + [0, 0, 2.8]
    
    # 噪声点 (Z: 很高或很低)
    noise_high = np.random.rand(200, 3) * [10, 10, 1.0] + [0, 0, 5.0]
    noise_low = np.random.rand(200, 3) * [10, 10, 0.1] + [0, 0, -1.0]
    
    # 合并所有点
    all_points = np.vstack([ground_points, room_points, ceiling_points, noise_high, noise_low])
    
    return all_points

def test_z_filtering_logic():
    """测试Z坐标过滤逻辑"""
    print("🧪 测试Z坐标过滤逻辑...")
    
    # 创建测试点云
    xyz = create_test_point_cloud()
    original_count = len(xyz)
    
    print(f"   原始点云: {original_count} 个点")
    print(f"   Z坐标范围: {np.min(xyz[:, 2]):.2f} ~ {np.max(xyz[:, 2]):.2f}")
    
    # 应用过滤逻辑（复制自增强的脚本）
    z_min, z_max = np.min(xyz[:, 2]), np.max(xyz[:, 2])
    z_range = z_max - z_min

    # 只有当Z范围足够大时才进行过滤
    if z_range > 0.05:  # 如果Z范围大于5cm，才进行过滤
        # 过滤掉过高和过低的点
        z_filter_margin = min(0.25, z_range * 0.1)
        z_filter = (xyz[:, 2] > z_min + z_filter_margin) & (xyz[:, 2] < z_max - z_filter_margin)
        filtered_xyz = xyz[z_filter]
    else:
        # Z范围太小，跳过过滤
        filtered_xyz = xyz
        z_filter_margin = 0

    filtered_count = len(filtered_xyz)
    
    print(f"   过滤边距: {z_filter_margin:.2f}")
    print(f"   过滤后点云: {filtered_count} 个点")
    print(f"   过滤比例: {(original_count - filtered_count) / original_count * 100:.1f}%")
    print(f"   过滤后Z范围: {np.min(filtered_xyz[:, 2]):.2f} ~ {np.max(filtered_xyz[:, 2]):.2f}")
    
    # 验证过滤效果
    # 1. 应该移除了极端的Z值
    extreme_high_removed = np.sum(xyz[:, 2] > 4.0) > np.sum(filtered_xyz[:, 2] > 4.0)
    extreme_low_removed = np.sum(xyz[:, 2] < 0) > np.sum(filtered_xyz[:, 2] < 0)
    
    # 2. 应该保留了主要的房间点
    main_points_preserved = np.sum((filtered_xyz[:, 2] > 0.5) & (filtered_xyz[:, 2] < 2.5)) > 0
    
    print(f"   ✅ 移除极高点: {'是' if extreme_high_removed else '否'}")
    print(f"   ✅ 移除极低点: {'是' if extreme_low_removed else '否'}")
    print(f"   ✅ 保留主要点: {'是' if main_points_preserved else '否'}")
    
    return extreme_high_removed and extreme_low_removed and main_points_preserved

def test_edge_cases():
    """测试边界情况"""
    print("\n🧪 测试边界情况...")
    
    test_cases = [
        # 扁平点云（Z范围很小）
        {
            'name': '扁平点云',
            'points': np.random.rand(1000, 3) * [10, 10, 0.1] + [0, 0, 1.0]
        },
        # 高耸点云（Z范围很大）
        {
            'name': '高耸点云',
            'points': np.random.rand(1000, 3) * [10, 10, 10.0]
        },
        # 单层点云（所有点Z坐标相同）
        {
            'name': '单层点云',
            'points': np.column_stack([
                np.random.rand(1000, 2) * 10,
                np.ones(1000) * 1.5
            ])
        }
    ]
    
    all_passed = True
    
    for case in test_cases:
        xyz = case['points']
        original_count = len(xyz)
        
        # 应用过滤逻辑
        z_min, z_max = np.min(xyz[:, 2]), np.max(xyz[:, 2])
        z_range = z_max - z_min

        if z_range > 0.05:  # 如果Z范围大于5cm，才进行过滤
            z_filter_margin = min(0.25, z_range * 0.1)
            z_filter = (xyz[:, 2] > z_min + z_filter_margin) & (xyz[:, 2] < z_max - z_filter_margin)
            filtered_xyz = xyz[z_filter]
        else:
            # Z范围太小，跳过过滤
            filtered_xyz = xyz
            z_filter_margin = 0

        filtered_count = len(filtered_xyz)
        
        print(f"   {case['name']}:")
        print(f"     原始: {original_count} 点, Z范围: {z_range:.3f}")
        print(f"     过滤: {filtered_count} 点, 边距: {z_filter_margin:.3f}")
        
        # 验证：对于单层点云，应该保留大部分点
        if z_range < 0.01:  # 单层点云
            retention_rate = filtered_count / original_count
            case_passed = retention_rate > 0.8  # 应该保留80%以上的点
            print(f"     单层保留率: {retention_rate:.1%} {'✅' if case_passed else '❌'}")
        else:
            case_passed = filtered_count > 0  # 至少应该有一些点被保留
            print(f"     有效过滤: {'✅' if case_passed else '❌'}")
        
        if not case_passed:
            all_passed = False
    
    return all_passed

def test_filtering_parameters():
    """测试过滤参数的合理性"""
    print("\n🧪 测试过滤参数合理性...")
    
    # 测试不同Z范围下的过滤边距
    test_ranges = [0.1, 0.5, 1.0, 2.0, 5.0, 10.0]
    
    print("   Z范围 -> 过滤边距:")
    for z_range in test_ranges:
        z_filter_margin = min(0.25, z_range * 0.1)
        print(f"     {z_range:.1f}m -> {z_filter_margin:.3f}m")
    
    # 验证边距计算的合理性
    # 1. 小范围时应该使用比例边距
    small_range_margin = min(0.25, 0.5 * 0.1)
    small_range_correct = small_range_margin == 0.05
    
    # 2. 大范围时应该限制在0.25m
    large_range_margin = min(0.25, 10.0 * 0.1)
    large_range_correct = large_range_margin == 0.25
    
    print(f"   小范围边距计算: {'✅' if small_range_correct else '❌'}")
    print(f"   大范围边距限制: {'✅' if large_range_correct else '❌'}")
    
    return small_range_correct and large_range_correct

def main():
    print("Z坐标过滤功能测试")
    print("=" * 40)
    
    # 运行所有测试
    test_results = []
    
    test_results.append(test_z_filtering_logic())
    test_results.append(test_edge_cases())
    test_results.append(test_filtering_parameters())
    
    # 总结
    print("\n" + "=" * 40)
    print("🎯 测试结果总结")
    print("=" * 40)
    
    test_names = [
        "Z坐标过滤逻辑",
        "边界情况处理",
        "过滤参数合理性"
    ]
    
    for i, (name, result) in enumerate(zip(test_names, test_results)):
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{i+1}. {name}: {status}")
    
    all_passed = all(test_results)
    
    if all_passed:
        print(f"\n🎉 所有测试通过！Z坐标过滤功能正常")
        print(f"\n📋 过滤功能特点:")
        print(f"✅ 自动计算点云Z坐标范围")
        print(f"✅ 动态调整过滤边距（最大0.25m）")
        print(f"✅ 移除极端高低点（天花板/地板噪声）")
        print(f"✅ 保留主要房间结构点")
        print(f"✅ 处理各种边界情况（扁平/高耸/单层点云）")
        
        print(f"\n🔧 过滤逻辑:")
        print(f"   边距 = min(0.25, Z范围 × 0.1)")
        print(f"   保留范围 = [Z_min + 边距, Z_max - 边距]")
        print(f"   参考HC数据集的±0.25m过滤策略")
    else:
        print(f"\n⚠️  部分测试失败，需要检查实现")

if __name__ == "__main__":
    main()
