import os
import sys
import json
import time
import argparse
from collections import defaultdict
from typing import Dict, List, Tuple

import numpy as np

# Make dataset_converters importable
CUR_DIR = os.path.dirname(__file__)
REPO_ROOT = os.path.abspath(os.path.join(CUR_DIR, '..', '..'))
CONVERTERS_DIR = os.path.join(REPO_ROOT, 'tools', 'dataset_converters')
if CONVERTERS_DIR not in sys.path:
    sys.path.append(CONVERTERS_DIR)

# Import pipeline pieces
import generate_coco_hc_0722 as gen
from hc_utils import (
    img_size,
    generate_density,
    generate_density_optimized,
    parse_floor_plan_polys,
    generate_coco_dict,
    calculate_slopes,
    rot_normalize_vectorized_gt,
)

from common_utils import export_density  # noqa: F401 (not used; reserved)


def default_args():
    # Mirror defaults from generate_coco_hc_0722.config()
    return {
        'data_root': '/home/<USER>/01_3D-FAVP/handheld_scanner_Data/RS10/',
        'filelists_dir': '/home/<USER>/data/RS10_data/00_dataset_spilt/filelists',
        'density_height': img_size,
        'density_width': img_size,
        'downsample_factor': 4,
        'max_workers': 16,
        'use_optimized_density': False,
    }


def read_split_scenes(filelists_dir: str) -> Tuple[List[str], List[str], List[str]]:
    def _read_list(fname):
        if not os.path.exists(fname):
            return []
        with open(fname, 'r') as f:
            return [l.strip() for l in f if l.strip()]

    train_scenes = _read_list(os.path.join(filelists_dir, 'train.txt'))
    val_scenes = _read_list(os.path.join(filelists_dir, 'val.txt'))
    test_scenes = _read_list(os.path.join(filelists_dir, 'test.txt'))
    return train_scenes, val_scenes, test_scenes


def build_argparser():
    d = default_args()
    ap = argparse.ArgumentParser(description='Profile point cloud preprocessing for a single scene')
    ap.add_argument('--scene', type=str, default=None, help='Scene/part name to profile (must be present in filelists). If omitted, uses the first train scene.')
    ap.add_argument('--data_root', type=str, default=d['data_root'])
    ap.add_argument('--filelists_dir', type=str, default=d['filelists_dir'])
    ap.add_argument('--density_height', type=int, default=d['density_height'])
    ap.add_argument('--density_width', type=int, default=d['density_width'])
    ap.add_argument('--downsample_factor', type=int, default=d['downsample_factor'])
    ap.add_argument('--max_workers', type=int, default=d['max_workers'])
    ap.add_argument('--use_optimized_density', action='store_true', default=d['use_optimized_density'])
    ap.add_argument('--multithread', action='store_true', default=True, help='Whether to use multi-threaded LAS processing (ThreadPoolExecutor)')
    ap.add_argument('--list_scenes', action='store_true', help='List available scenes from filelists and exit')
    ap.add_argument('--profile_path', type=str, default=os.path.join(CUR_DIR, 'profile_report.json'), help='Path to save JSON timing report')
    ap.add_argument('--verbose', action='store_true')
    return ap


def timed_process_single_las_file(scene_path: str, pointcloud_roi: list, slope_angle_rad: float, downsample_factor: int = 4) -> Tuple[np.ndarray, Dict[str, float], int]:
    """
    A timed version of generate_coco_hc_0722.process_single_las_file but with explicit
    timing for each sub-step and using a fast NumPy rotation (no cv2 dependency here).

    Returns: (rotated_points Nx2 float32, timing_dict, n_points_before_downsample)
    """
    import laspy

    times = {}
    t0 = time.perf_counter()
    las = laspy.read(scene_path)
    times['las_read'] = time.perf_counter() - t0

    t1 = time.perf_counter()
    z_filter = (las.z > pointcloud_roi[2] + 0.25) & (las.z < pointcloud_roi[5] - 0.25)
    x_filter = (las.x > pointcloud_roi[0]) & (las.x < pointcloud_roi[3])
    y_filter = (las.y > pointcloud_roi[1]) & (las.y < pointcloud_roi[4])
    combined_filter = z_filter & x_filter & y_filter
    times['mask_build'] = time.perf_counter() - t1

    if not np.any(combined_filter):
        return np.empty((0, 2), dtype=np.float32), times, 0

    t2 = time.perf_counter()
    filtered_x = las.x[combined_filter]
    filtered_y = las.y[combined_filter]
    times['mask_apply'] = time.perf_counter() - t2

    n_points_raw = len(filtered_x)

    # Downsample
    t3 = time.perf_counter()
    if downsample_factor > 1 and n_points_raw > 0:
        n_samples = max(1, n_points_raw // downsample_factor)
        if n_samples < n_points_raw:
            idx = np.random.choice(n_points_raw, n_samples, replace=False)
            filtered_x = filtered_x[idx]
            filtered_y = filtered_y[idx]
    times['downsample'] = time.perf_counter() - t3

    # Rotation using pure NumPy
    t4 = time.perf_counter()
    c = np.cos(slope_angle_rad)
    s = np.sin(slope_angle_rad)
    x = filtered_x.astype(np.float64, copy=False)
    y = filtered_y.astype(np.float64, copy=False)
    xr = x * c + y * s
    yr = -x * s + y * c
    rotated_points = np.column_stack((xr, yr)).astype(np.float32)
    times['rotate'] = time.perf_counter() - t4

    return rotated_points, times, n_points_raw


def main():
    args = build_argparser().parse_args()

    train_scenes, val_scenes, test_scenes = read_split_scenes(args.filelists_dir)

    if args.list_scenes:
        print(f"train: {len(train_scenes)}, val: {len(val_scenes)}, test: {len(test_scenes)}")
        if train_scenes:
            print("First 10 train scenes:", train_scenes[:10])
        return 0

    # Pick scene
    scene = args.scene
    if scene is None:
        if len(train_scenes) == 0:
            print('No scenes found in train.txt; please provide --scene explicitly')
            return 1
        scene = train_scenes[0]
    scene_path = os.path.join(args.data_root, scene)
    if not os.path.isdir(scene_path):
        print(f'Scene directory not found: {scene_path}')
        return 1

    # Load floorplan.json and compute slope/roi
    timings = defaultdict(float)
    counts = defaultdict(int)

    t = time.perf_counter()
    gt_json_path = os.path.join(scene_path, 'Annotations/floorplan.json')
    with open(gt_json_path, 'r') as f:
        vectorized_gt = json.load(f)
    timings['load_floorplan_json'] += time.perf_counter() - t

    t = time.perf_counter()
    slope_angle, max_coords, min_coords = calculate_slopes(vectorized_gt, type='mean')
    timings['calculate_slopes'] += time.perf_counter() - t

    pointcloud_roi = vectorized_gt['frames'][0]['boundingBox']

    # Determine LAS directory and files
    t = time.perf_counter()
    las_dir = 'LAS_Refined' if os.path.exists(os.path.join(scene_path, 'LAS_Refined')) else 'LAS'
    las_folder = os.path.join(scene_path, las_dir)
    las_files = [f for f in os.listdir(las_folder) if f.endswith('.las')]
    timings['list_las_files'] += time.perf_counter() - t

    if args.verbose:
        print(f'Profiling scene={scene} using {len(las_files)} LAS files, roi={pointcloud_roi}, slope(rad)={slope_angle:.6f}')

    # Process LAS files
    all_points_list: List[np.ndarray] = []

    if args.multithread:
        from concurrent.futures import ThreadPoolExecutor, as_completed
        with ThreadPoolExecutor(max_workers=args.max_workers) as ex:
            futs = {
                ex.submit(timed_process_single_las_file, os.path.join(las_folder, lf), pointcloud_roi, slope_angle, max(1, args.downsample_factor // 4))
                : lf for lf in las_files
            }
            for fut in as_completed(futs):
                pts, tdict, nraw = fut.result()
                for k, v in tdict.items():
                    timings[f'per_file_{k}'] += v
                counts['n_points_before_downsample'] += nraw
                if pts.shape[0] > 0:
                    all_points_list.append(pts)
    else:
        for lf in las_files:
            pts, tdict, nraw = timed_process_single_las_file(os.path.join(las_folder, lf), pointcloud_roi, slope_angle, max(1, args.downsample_factor // 4))
            for k, v in tdict.items():
                timings[f'per_file_{k}'] += v
            counts['n_points_before_downsample'] += nraw
            if pts.shape[0] > 0:
                all_points_list.append(pts)

    t = time.perf_counter()
    all_points = np.concatenate(all_points_list, axis=0) if all_points_list else np.empty((0, 2), dtype=np.float32)
    timings['concat_points'] += time.perf_counter() - t

    # Optional second-stage downsample to match generate script behavior
    if args.downsample_factor > 1:
        initial_down = max(1, args.downsample_factor // 4)
        if args.downsample_factor > initial_down and all_points.shape[0] > 0:
            remaining_factor = args.downsample_factor // initial_down
            t = time.perf_counter()
            n_points = all_points.shape[0]
            n_samples = max(1, n_points // remaining_factor)
            idx = np.random.choice(n_points, n_samples, replace=False)
            all_points = all_points[idx]
            timings['downsample_stage2'] += time.perf_counter() - t

    # Density generation
    if all_points.shape[0] > 0:
        if args.use_optimized_density:
            t = time.perf_counter()
            density, _ = generate_density_optimized(all_points, max_coords, min_coords, width=args.density_width, height=args.density_height)
            timings['generate_density_optimized'] += time.perf_counter() - t
        else:
            t = time.perf_counter()
            density, _ = generate_density(all_points, max_coords, min_coords, width=args.density_width, height=args.density_height)
            timings['generate_density'] += time.perf_counter() - t
    else:
        density = np.zeros((args.density_height, args.density_width), dtype=np.float32)

    # Rotate + normalize GT (to emulate full pipeline cost)
    t = time.perf_counter()
    _vec_gt, rot_gt_img = rot_normalize_vectorized_gt(vectorized_gt, slope_angle, max_coords, min_coords, np.array((args.density_height, args.density_width)))
    timings['rot_normalize_gt'] += time.perf_counter() - t

    # Basic report
    total_time = sum(timings.values())
    report = {
        'scene': scene,
        'data_root': args.data_root,
        'multithread': args.multithread,
        'max_workers': args.max_workers,
        'downsample_factor': args.downsample_factor,
        'use_optimized_density': args.use_optimized_density,
        'n_las_files': len(las_files),
        'n_points_after_processing': int(all_points.shape[0]),
        'n_points_before_downsample_sum': int(counts['n_points_before_downsample']),
        'timings_sec': dict(sorted(timings.items(), key=lambda kv: kv[1], reverse=True)),
        'total_time_sec': total_time,
    }

    # Pretty print
    print("\n=== Point Cloud Preprocessing Profiling Report ===")
    print(json.dumps(report, indent=2))

    # Save JSON
    try:
        with open(args.profile_path, 'w') as f:
            json.dump(report, f, indent=2)
        print(f"Saved profiling report to {args.profile_path}")
    except Exception as e:
        print(f"Failed to save profiling report: {e}")

    # Additionally, show top hotspots
    print("\nTop hotspots:")
    for k, v in list(sorted(timings.items(), key=lambda kv: kv[1], reverse=True))[:8]:
        pct = (v / total_time * 100.0) if total_time > 0 else 0.0
        print(f"  {k:>24}: {v:8.3f}s  ({pct:5.1f}%)")

    return 0


if __name__ == '__main__':
    sys.exit(main())

