#!/usr/bin/env python3
"""
测试点云精度修复效果的脚本

用于验证移除坐标量化操作后，点云在z轴方向是否恢复连续性
"""

import numpy as np
import matplotlib.pyplot as plt
import sys
import os

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'dataset_converters', 'stru3d'))

def analyze_point_cloud_distribution(coords):
    """分析点云坐标分布"""
    print("=== 点云坐标分布分析 ===")
    print(f"总点数: {len(coords)}")
    print(f"X坐标范围: [{coords[:, 0].min():.2f}, {coords[:, 0].max():.2f}]")
    print(f"Y坐标范围: [{coords[:, 1].min():.2f}, {coords[:, 1].max():.2f}]")
    print(f"Z坐标范围: [{coords[:, 2].min():.2f}, {coords[:, 2].max():.2f}]")
    
    # 检查Z轴分布
    z_coords = coords[:, 2]
    unique_z = np.unique(z_coords)
    print(f"\nZ轴唯一值数量: {len(unique_z)}")
    print(f"Z轴前10个值: {unique_z[:10]}")
    
    # 检查是否存在量化模式
    z_mod_100 = z_coords % 100
    z_mod_10 = z_coords % 10
    z_mod_5 = z_coords % 5
    
    print(f"\nZ坐标量化检查:")
    print(f"Z % 100 == 0 的点数: {np.sum(z_mod_100 == 0)} ({np.sum(z_mod_100 == 0)/len(z_coords)*100:.1f}%)")
    print(f"Z % 10 == 0 的点数: {np.sum(z_mod_10 == 0)} ({np.sum(z_mod_10 == 0)/len(z_coords)*100:.1f}%)")
    print(f"Z % 5 == 0 的点数: {np.sum(z_mod_5 == 0)} ({np.sum(z_mod_5 == 0)/len(z_coords)*100:.1f}%)")
    
    # 检查Z轴间隔
    z_diffs = np.diff(np.sort(unique_z))
    min_diff = z_diffs.min()
    max_diff = z_diffs.max()
    mean_diff = z_diffs.mean()
    
    print(f"\nZ轴相邻值间隔:")
    print(f"最小间隔: {min_diff:.6f}")
    print(f"最大间隔: {max_diff:.6f}")
    print(f"平均间隔: {mean_diff:.6f}")
    
    # 判断是否存在量化问题
    if np.sum(z_mod_100 == 0) > len(z_coords) * 0.8:
        print("⚠️  检测到严重的Z轴量化问题（100单位量化）")
        return False
    elif np.sum(z_mod_10 == 0) > len(z_coords) * 0.8:
        print("⚠️  检测到Z轴量化问题（10单位量化）")
        return False
    elif min_diff > 50:
        print("⚠️  检测到Z轴精度截断问题（最小间隔过大）")
        return False
    else:
        print("✅ Z轴精度正常，未检测到明显的量化问题")
        return True

def create_test_visualization(coords, output_path=None):
    """创建点云分布可视化"""
    fig, axes = plt.subplots(2, 2, figsize=(12, 10))
    
    # Z轴分布直方图
    axes[0, 0].hist(coords[:, 2], bins=50, alpha=0.7, edgecolor='black')
    axes[0, 0].set_title('Z坐标分布直方图')
    axes[0, 0].set_xlabel('Z坐标')
    axes[0, 0].set_ylabel('点数')
    
    # XY平面投影
    axes[0, 1].scatter(coords[:, 0], coords[:, 1], s=0.1, alpha=0.5)
    axes[0, 1].set_title('XY平面投影')
    axes[0, 1].set_xlabel('X坐标')
    axes[0, 1].set_ylabel('Y坐标')
    axes[0, 1].set_aspect('equal')
    
    # XZ平面投影
    axes[1, 0].scatter(coords[:, 0], coords[:, 2], s=0.1, alpha=0.5)
    axes[1, 0].set_title('XZ平面投影')
    axes[1, 0].set_xlabel('X坐标')
    axes[1, 0].set_ylabel('Z坐标')
    
    # YZ平面投影
    axes[1, 1].scatter(coords[:, 1], coords[:, 2], s=0.1, alpha=0.5)
    axes[1, 1].set_title('YZ平面投影')
    axes[1, 1].set_xlabel('Y坐标')
    axes[1, 1].set_ylabel('Z坐标')
    
    plt.tight_layout()
    
    if output_path:
        plt.savefig(output_path, dpi=150, bbox_inches='tight')
        print(f"可视化图表已保存到: {output_path}")
    else:
        plt.show()
    
    plt.close()

def test_with_sample_data():
    """使用示例数据测试"""
    print("=== 使用示例数据测试 ===")
    
    # 创建测试数据
    np.random.seed(42)
    n_points = 10000
    
    # 原始连续数据
    coords_original = np.random.randn(n_points, 3) * 100
    coords_original[:, 2] += 1000  # Z轴偏移
    
    print("原始连续数据:")
    is_good = analyze_point_cloud_distribution(coords_original)
    
    # 模拟量化后的数据
    coords_quantized = coords_original.copy()
    coords_quantized[:, :2] = np.round(coords_quantized[:, :2] / 10) * 10
    coords_quantized[:, 2] = np.round(coords_quantized[:, 2] / 100) * 100
    
    print("\n" + "="*50)
    print("量化后的数据:")
    is_bad = analyze_point_cloud_distribution(coords_quantized)
    
    return is_good and not is_bad

if __name__ == "__main__":
    print("点云精度测试脚本")
    print("="*50)
    
    # 首先用示例数据测试
    test_result = test_with_sample_data()
    
    if test_result:
        print("\n✅ 测试脚本工作正常")
    else:
        print("\n❌ 测试脚本检测逻辑有问题")
    
    print("\n" + "="*50)
    print("使用说明:")
    print("1. 运行 python generate_point_cloud_stru3d.py 生成点云")
    print("2. 使用此脚本分析生成的点云文件")
    print("3. 检查输出中是否还有量化问题的警告")
