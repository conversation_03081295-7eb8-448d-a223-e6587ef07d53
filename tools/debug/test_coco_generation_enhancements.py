#!/usr/bin/env python3
"""
测试COCO生成脚本增强功能

验证：
1. 数据验证和错误处理功能
2. --max_scenes参数功能
3. 日志记录功能
4. 缺失点云文件的处理
"""

import os
import sys
import tempfile
import json
import subprocess

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'dataset_converters', 'stru3d'))

def test_data_validation():
    """测试数据验证功能"""
    print("🧪 测试数据验证功能...")
    
    try:
        from generate_coco_stru3d import validate_scene_data
        import logging
        
        # 创建临时日志记录器
        logger = logging.getLogger('test')
        logger.setLevel(logging.WARNING)
        
        # 创建临时测试目录结构
        with tempfile.TemporaryDirectory() as temp_dir:
            scene_path = os.path.join(temp_dir, "test_scene")
            os.makedirs(scene_path, exist_ok=True)
            
            # 测试1: 缺少点云文件
            is_valid, result = validate_scene_data(scene_path, "test_001", logger)
            print(f"   缺少点云文件: {'✅' if not is_valid and result == 'missing_point_cloud' else '❌'}")
            
            # 测试2: 创建空的点云文件
            ply_path = os.path.join(scene_path, "point_cloud.ply")
            with open(ply_path, 'w') as f:
                f.write("")  # 空文件
            
            is_valid, result = validate_scene_data(scene_path, "test_002", logger)
            print(f"   空点云文件: {'✅' if not is_valid and result == 'small_point_cloud' else '❌'}")
            
            # 测试3: 创建正常大小的点云文件但缺少注释文件
            with open(ply_path, 'w') as f:
                f.write("ply\nformat ascii 1.0\n" + "vertex 1.0 2.0 3.0\n" * 100)
            
            is_valid, result = validate_scene_data(scene_path, "test_003", logger)
            print(f"   缺少注释文件: {'✅' if not is_valid and result == 'missing_annotation' else '❌'}")
            
            # 测试4: 创建完整的文件结构
            annotation_path = os.path.join(scene_path, "annotation_3d.json")
            with open(annotation_path, 'w') as f:
                json.dump({"test": "data"}, f)
            
            is_valid, result = validate_scene_data(scene_path, "test_004", logger)
            print(f"   完整数据: {'✅' if is_valid and result == 'valid' else '❌'}")
            
        return True
        
    except Exception as e:
        print(f"   ❌ 测试失败: {e}")
        return False

def test_max_scenes_parameter():
    """测试--max_scenes参数"""
    print("\n🧪 测试--max_scenes参数...")
    
    try:
        from generate_coco_stru3d import config
        
        # 备份原始sys.argv
        original_argv = sys.argv.copy()
        
        test_cases = [
            # 无限制
            {
                'args': ['--data_root', '/test/path', '--output', '/test/output'],
                'expected_max_scenes': None
            },
            # 限制10个场景
            {
                'args': ['--data_root', '/test/path', '--output', '/test/output', '--max_scenes', '10'],
                'expected_max_scenes': 10
            },
            # 限制100个场景
            {
                'args': ['--data_root', '/test/path', '--output', '/test/output', '--max_scenes', '100'],
                'expected_max_scenes': 100
            }
        ]
        
        all_passed = True
        
        for i, test_case in enumerate(test_cases, 1):
            try:
                # 设置测试参数
                sys.argv = ['generate_coco_stru3d.py'] + test_case['args']
                
                # 解析参数
                args = config()
                
                # 验证结果
                if args.max_scenes == test_case['expected_max_scenes']:
                    print(f"   测试用例 {i}: ✅ max_scenes = {args.max_scenes}")
                else:
                    print(f"   测试用例 {i}: ❌ 期望 {test_case['expected_max_scenes']}, 实际 {args.max_scenes}")
                    all_passed = False
                    
            except Exception as e:
                print(f"   测试用例 {i}: ❌ 解析失败 - {e}")
                all_passed = False
            finally:
                # 恢复原始sys.argv
                sys.argv = original_argv.copy()
        
        return all_passed
        
    except Exception as e:
        print(f"   ❌ 测试失败: {e}")
        return False

def test_logging_setup():
    """测试日志设置功能"""
    print("\n🧪 测试日志设置功能...")
    
    try:
        from generate_coco_stru3d import setup_logging
        
        # 测试日志设置
        logger = setup_logging()
        
        # 检查日志记录器
        if logger and hasattr(logger, 'info'):
            print("   ✅ 日志记录器创建成功")
            
            # 测试日志输出
            logger.info("测试日志消息")
            print("   ✅ 日志消息输出成功")
            
            return True
        else:
            print("   ❌ 日志记录器创建失败")
            return False
            
    except Exception as e:
        print(f"   ❌ 测试失败: {e}")
        return False

def test_command_line_interface():
    """测试命令行接口"""
    print("\n🧪 测试命令行接口...")
    
    try:
        # 测试帮助信息
        script_path = os.path.join(os.path.dirname(__file__), '..', 'dataset_converters', 'stru3d', 'generate_coco_stru3d.py')
        
        result = subprocess.run([
            sys.executable, script_path, '--help'
        ], capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            help_output = result.stdout
            
            # 检查是否包含新参数
            if '--max_scenes' in help_output:
                print("   ✅ --max_scenes参数在帮助信息中")
            else:
                print("   ❌ --max_scenes参数不在帮助信息中")
                return False
            
            # 检查参数描述
            if 'Maximum number of scenes' in help_output:
                print("   ✅ 参数描述正确")
            else:
                print("   ❌ 参数描述缺失")
                return False
            
            return True
        else:
            print(f"   ❌ 命令执行失败: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print("   ❌ 命令执行超时")
        return False
    except Exception as e:
        print(f"   ❌ 测试失败: {e}")
        return False

def main():
    print("COCO生成脚本增强功能测试")
    print("=" * 50)
    
    # 运行所有测试
    test_results = []
    
    test_results.append(test_data_validation())
    test_results.append(test_max_scenes_parameter())
    test_results.append(test_logging_setup())
    test_results.append(test_command_line_interface())
    
    # 总结
    print("\n" + "=" * 50)
    print("🎯 测试结果总结")
    print("=" * 50)
    
    test_names = [
        "数据验证功能",
        "--max_scenes参数",
        "日志设置功能",
        "命令行接口"
    ]
    
    for i, (name, result) in enumerate(zip(test_names, test_results)):
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{i+1}. {name}: {status}")
    
    all_passed = all(test_results)
    
    if all_passed:
        print(f"\n🎉 所有测试通过！COCO生成脚本增强功能已就绪")
        print(f"\n📋 使用方法:")
        print(f"# 处理所有场景")
        print(f"python generate_coco_stru3d.py --data_root /path/to/data --output /path/to/output")
        print(f"")
        print(f"# 限制处理场景数量（用于测试）")
        print(f"python generate_coco_stru3d.py --data_root /path/to/data --output /path/to/output --max_scenes 10")
        print(f"")
        print(f"# 脚本会自动跳过缺少点云文件的场景并记录详细日志")
    else:
        print(f"\n⚠️  部分测试失败，需要检查实现")

if __name__ == "__main__":
    main()
