#!/usr/bin/env python3
"""
诊断房间完整性问题的专用脚本

检查：
1. 每个场景有多少个房间
2. 每个房间的文件是否完整
3. 点云生成过程中是否有房间被跳过
"""

import os
import sys
import cv2
import numpy as np

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'dataset_converters', 'stru3d'))

def analyze_scene_structure(scene_path):
    """分析单个场景的结构"""
    scene_name = os.path.basename(scene_path)
    print(f"\n🔍 分析场景: {scene_name}")
    print(f"   路径: {scene_path}")
    
    if not os.path.exists(scene_path):
        print(f"   ❌ 场景路径不存在")
        return None
    
    rendering_dir = os.path.join(scene_path, "2D_rendering")
    if not os.path.exists(rendering_dir):
        print(f"   ❌ 2D_rendering目录不存在")
        return None
    
    # 获取所有房间目录
    try:
        all_items = os.listdir(rendering_dir)
        room_dirs = [item for item in all_items 
                    if os.path.isdir(os.path.join(rendering_dir, item)) and item.isdigit()]
        room_dirs = sorted(room_dirs)
        
        print(f"   📂 找到 {len(room_dirs)} 个房间目录: {room_dirs}")
        
        room_analysis = []
        
        for room_id in room_dirs:
            room_path = os.path.join(rendering_dir, room_id)
            panorama_path = os.path.join(room_path, "panorama")
            
            room_info = {
                'room_id': room_id,
                'room_path': room_path,
                'panorama_path': panorama_path,
                'files': {},
                'issues': []
            }
            
            # 检查panorama目录
            if not os.path.exists(panorama_path):
                room_info['issues'].append("缺少panorama目录")
                room_analysis.append(room_info)
                continue
            
            # 检查关键文件（使用正确的路径结构）
            key_files = {
                'depth.png': os.path.join(panorama_path, 'full', 'depth.png'),
                'rgb_coldlight.png': os.path.join(panorama_path, 'full', 'rgb_coldlight.png'),
                'normal.png': os.path.join(panorama_path, 'full', 'normal.png'),
                'camera_xyz.txt': os.path.join(panorama_path, 'camera_xyz.txt')
            }
            
            for file_name, file_path in key_files.items():
                if os.path.exists(file_path):
                    try:
                        if file_name.endswith('.png'):
                            # 检查图像文件
                            img = cv2.imread(file_path, cv2.IMREAD_ANYDEPTH | cv2.IMREAD_ANYCOLOR)
                            if img is not None:
                                room_info['files'][file_name] = {
                                    'exists': True,
                                    'size': os.path.getsize(file_path),
                                    'shape': img.shape,
                                    'valid': True
                                }
                            else:
                                room_info['files'][file_name] = {
                                    'exists': True,
                                    'size': os.path.getsize(file_path),
                                    'valid': False
                                }
                                room_info['issues'].append(f"{file_name}无法读取")
                        else:
                            # 检查文本文件
                            with open(file_path, 'r') as f:
                                content = f.read().strip()
                            room_info['files'][file_name] = {
                                'exists': True,
                                'size': os.path.getsize(file_path),
                                'content': content,
                                'valid': len(content) > 0
                            }
                            if not room_info['files'][file_name]['valid']:
                                room_info['issues'].append(f"{file_name}为空")
                    except Exception as e:
                        room_info['files'][file_name] = {
                            'exists': True,
                            'valid': False,
                            'error': str(e)
                        }
                        room_info['issues'].append(f"{file_name}读取错误: {e}")
                else:
                    room_info['files'][file_name] = {'exists': False, 'valid': False}
                    room_info['issues'].append(f"缺少{file_name}")
            
            room_analysis.append(room_info)
        
        # 输出详细分析
        valid_rooms = 0
        for room_info in room_analysis:
            room_id = room_info['room_id']
            issues = room_info['issues']
            
            if not issues:
                print(f"     ✅ 房间{room_id}: 完整")
                valid_rooms += 1
            else:
                print(f"     ❌ 房间{room_id}: {len(issues)}个问题")
                for issue in issues:
                    print(f"        - {issue}")
        
        print(f"   📊 有效房间: {valid_rooms}/{len(room_dirs)}")
        
        return {
            'scene_name': scene_name,
            'scene_path': scene_path,
            'total_rooms': len(room_dirs),
            'valid_rooms': valid_rooms,
            'room_analysis': room_analysis
        }
        
    except Exception as e:
        print(f"   ❌ 分析场景时出错: {e}")
        return None

def test_point_cloud_generation(scene_path):
    """测试点云生成过程"""
    print(f"\n🧪 测试点云生成: {os.path.basename(scene_path)}")
    
    try:
        from PointCloudReaderPanorama import PointCloudReaderPanorama
        
        # 创建点云读取器
        reader = PointCloudReaderPanorama(
            scene_path,
            random_level=0.5,
            generate_color=True,
            generate_normal=False,
            remove_duplicates=True,
            downsample_ratio=1.0,
            use_vectorized=True
        )
        
        # 检查生成的点云
        points = reader.point_cloud
        
        if points and 'coords' in points:
            coord_count = len(points['coords'])
            color_count = len(points.get('colors', []))
            
            print(f"   ✅ 成功生成点云")
            print(f"   📊 坐标点数: {coord_count}")
            print(f"   🎨 颜色点数: {color_count}")
            
            if coord_count > 0:
                coords = points['coords']
                print(f"   📏 坐标范围:")
                print(f"      X: [{coords[:, 0].min():.2f}, {coords[:, 0].max():.2f}]")
                print(f"      Y: [{coords[:, 1].min():.2f}, {coords[:, 1].max():.2f}]")
                print(f"      Z: [{coords[:, 2].min():.2f}, {coords[:, 2].max():.2f}]")
            
            return True
        else:
            print(f"   ❌ 点云生成失败或为空")
            return False
            
    except Exception as e:
        print(f"   ❌ 点云生成出错: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    print("房间完整性诊断脚本")
    print("=" * 50)
    
    # 默认数据路径
    data_root = "/home/<USER>/05_DL_dataset_wuhan/03_开源数据集/structure_3D/Panorama/"
    
    if len(sys.argv) > 1:
        data_root = sys.argv[1]
    
    # 查找几个测试场景
    test_scenes = []
    
    try:
        data_parts = sorted([d for d in os.listdir(data_root) 
                           if os.path.isdir(os.path.join(data_root, d)) 
                           and d.startswith('Structured3D_panorama')])
        
        print(f"📦 找到 {len(data_parts)} 个数据部分")
        
        # 从每个部分选择一个场景进行测试
        for part in data_parts[:2]:  # 只测试前2个部分
            part_path = os.path.join(data_root, part, 'Structured3D')
            if os.path.exists(part_path):
                scenes = sorted([s for s in os.listdir(part_path) 
                               if os.path.isdir(os.path.join(part_path, s))])[:2]  # 每部分取2个场景
                
                for scene in scenes:
                    scene_path = os.path.join(part_path, scene)
                    test_scenes.append(scene_path)
        
        print(f"🎯 选择 {len(test_scenes)} 个场景进行详细测试")
        
        # 分析每个测试场景
        total_scenes = 0
        successful_scenes = 0
        
        for scene_path in test_scenes:
            total_scenes += 1
            
            # 分析场景结构
            scene_analysis = analyze_scene_structure(scene_path)
            
            if scene_analysis and scene_analysis['valid_rooms'] > 0:
                # 测试点云生成
                if test_point_cloud_generation(scene_path):
                    successful_scenes += 1
        
        # 总结
        print(f"\n" + "=" * 50)
        print(f"🎯 诊断总结")
        print(f"=" * 50)
        print(f"测试场景数: {total_scenes}")
        print(f"成功场景数: {successful_scenes}")
        print(f"成功率: {successful_scenes/total_scenes*100:.1f}%")
        
        if successful_scenes < total_scenes:
            print(f"\n⚠️  发现问题，建议:")
            print(f"1. 检查数据文件完整性")
            print(f"2. 验证路径构建逻辑")
            print(f"3. 检查图像读取过程")
        else:
            print(f"\n✅ 所有测试场景都正常")
            
    except Exception as e:
        print(f"❌ 诊断过程出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
