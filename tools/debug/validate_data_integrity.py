#!/usr/bin/env python3
"""
验证Structured3D数据完整性的脚本

检查每个场景是否包含必要的文件和目录结构
"""

import os
import sys
import json
from pathlib import Path

def check_scene_integrity(scene_path):
    """检查单个场景的数据完整性"""
    scene_name = os.path.basename(scene_path)
    issues = []
    
    # 检查基本目录结构
    required_dirs = [
        "2D_rendering",
        "3D_rendering"
    ]
    
    required_files = [
        "annotation_3d.json"
    ]
    
    # 检查目录
    for dir_name in required_dirs:
        dir_path = os.path.join(scene_path, dir_name)
        if not os.path.exists(dir_path):
            issues.append(f"缺少目录: {dir_name}")
        elif not os.path.isdir(dir_path):
            issues.append(f"不是目录: {dir_name}")
    
    # 检查文件
    for file_name in required_files:
        file_path = os.path.join(scene_path, file_name)
        if not os.path.exists(file_path):
            issues.append(f"缺少文件: {file_name}")
        elif not os.path.isfile(file_path):
            issues.append(f"不是文件: {file_name}")
    
    # 检查2D_rendering子目录
    rendering_2d_path = os.path.join(scene_path, "2D_rendering")
    if os.path.exists(rendering_2d_path):
        # 查找房间目录
        try:
            room_dirs = [d for d in os.listdir(rendering_2d_path) 
                        if os.path.isdir(os.path.join(rendering_2d_path, d)) and d.isdigit()]
            
            if not room_dirs:
                issues.append("2D_rendering中没有找到房间目录")
            else:
                # 检查第一个房间的panorama目录
                first_room = room_dirs[0]
                panorama_path = os.path.join(rendering_2d_path, first_room, "panorama")
                
                if not os.path.exists(panorama_path):
                    issues.append(f"缺少panorama目录: {first_room}/panorama")
                else:
                    # 检查关键文件
                    key_files = ["camera_xyz.txt", "depth.png", "rgb.png"]
                    for key_file in key_files:
                        key_file_path = os.path.join(panorama_path, key_file)
                        if not os.path.exists(key_file_path):
                            issues.append(f"缺少关键文件: {first_room}/panorama/{key_file}")
        
        except Exception as e:
            issues.append(f"检查2D_rendering时出错: {str(e)}")
    
    return issues

def validate_data_root(data_root, max_scenes=None):
    """验证整个数据根目录"""
    print(f"🔍 验证数据根目录: {data_root}")
    
    if not os.path.exists(data_root):
        print(f"❌ 数据根目录不存在: {data_root}")
        return
    
    # 查找所有数据部分
    data_parts = sorted([d for d in os.listdir(data_root) 
                        if os.path.isdir(os.path.join(data_root, d)) 
                        and d.startswith('Structured3D_panorama')])
    
    print(f"📦 找到 {len(data_parts)} 个数据部分")
    
    total_scenes = 0
    valid_scenes = 0
    invalid_scenes = []
    
    for part in data_parts:
        part_path = os.path.join(data_root, part, 'Structured3D')
        
        if not os.path.exists(part_path):
            print(f"⚠️  跳过 {part}: Structured3D目录不存在")
            continue
        
        print(f"\n📂 检查数据部分: {part}")
        
        try:
            scenes = sorted([s for s in os.listdir(part_path) 
                           if os.path.isdir(os.path.join(part_path, s))])
            
            print(f"   找到 {len(scenes)} 个场景")
            
            for scene in scenes:
                if max_scenes and total_scenes >= max_scenes:
                    break
                
                total_scenes += 1
                scene_path = os.path.join(part_path, scene)
                
                # 检查场景完整性
                issues = check_scene_integrity(scene_path)
                
                if issues:
                    print(f"   ❌ {scene}: {len(issues)} 个问题")
                    for issue in issues:
                        print(f"      - {issue}")
                    invalid_scenes.append({
                        'part': part,
                        'scene': scene,
                        'issues': issues
                    })
                else:
                    print(f"   ✅ {scene}: 完整")
                    valid_scenes += 1
            
            if max_scenes and total_scenes >= max_scenes:
                break
                
        except Exception as e:
            print(f"   ❌ 处理 {part} 时出错: {e}")
    
    # 输出总结
    print(f"\n" + "=" * 60)
    print(f"📊 数据完整性验证结果")
    print(f"=" * 60)
    print(f"总场景数: {total_scenes}")
    print(f"有效场景: {valid_scenes}")
    print(f"无效场景: {len(invalid_scenes)}")
    print(f"完整性: {valid_scenes/total_scenes*100:.1f}%")
    
    if invalid_scenes:
        print(f"\n❌ 无效场景详情:")
        for invalid in invalid_scenes:
            print(f"   {invalid['part']}/{invalid['scene']}: {len(invalid['issues'])} 个问题")
    
    # 生成跳过列表
    if invalid_scenes:
        skip_list_path = "output/0_depth_map_display_stru3d/invalid_scenes.txt"
        os.makedirs(os.path.dirname(skip_list_path), exist_ok=True)
        
        with open(skip_list_path, 'w') as f:
            f.write("# 无效场景列表 - 建议跳过处理\n")
            f.write(f"# 生成时间: {os.popen('date').read().strip()}\n")
            f.write(f"# 总无效场景: {len(invalid_scenes)}\n\n")
            
            for invalid in invalid_scenes:
                f.write(f"{invalid['part']}/{invalid['scene']}\n")
                for issue in invalid['issues']:
                    f.write(f"  # {issue}\n")
                f.write("\n")
        
        print(f"\n📝 无效场景列表已保存到: {skip_list_path}")

def main():
    print("Structured3D数据完整性验证")
    print("=" * 50)
    
    # 默认数据路径
    data_root = "/home/<USER>/05_DL_dataset_wuhan/03_开源数据集/structure_3D/Panorama/"
    
    if len(sys.argv) > 1:
        data_root = sys.argv[1]
    
    max_scenes = None
    if len(sys.argv) > 2:
        max_scenes = int(sys.argv[2])
        print(f"🔢 限制检查场景数量: {max_scenes}")
    
    validate_data_root(data_root, max_scenes)
    
    print(f"\n🎯 建议:")
    print(f"1. 跳过无效场景继续处理其他场景")
    print(f"2. 或者修复数据完整性问题")
    print(f"3. 使用 --skip-invalid 参数自动跳过无效场景")

if __name__ == "__main__":
    main()
