#!/usr/bin/env python3
"""
演示新的鲜艳颜色方案
"""

import numpy as np
import cv2
import os

def generate_bright_colors(num_colors):
    """生成鲜艳且易于区分的颜色列表"""
    # 预定义的鲜艳颜色 (BGR格式，适用于OpenCV)
    bright_colors = [
        (0, 255, 255),    # 黄色
        (255, 0, 255),    # 品红色
        (255, 255, 0),    # 青色
        (0, 255, 0),      # 绿色
        (255, 0, 0),      # 蓝色
        (0, 0, 255),      # 红色
        (255, 165, 0),    # 橙色
        (128, 0, 128),    # 紫色
        (255, 192, 203),  # 粉色
        (0, 255, 127),    # 春绿色
        (255, 20, 147),   # 深粉色
        (0, 191, 255),    # 深天蓝色
        (154, 205, 50),   # 黄绿色
        (255, 69, 0),     # 红橙色
        (138, 43, 226),   # 蓝紫色
        (255, 215, 0),    # 金色
        (50, 205, 50),    # 酸橙绿
        (255, 105, 180),  # 热粉色
        (0, 206, 209),    # 暗青色
        (255, 140, 0),    # 暗橙色
    ]
    
    # 如果需要更多颜色，生成随机鲜艳颜色
    colors = bright_colors.copy()
    while len(colors) < num_colors:
        # 生成高饱和度、高亮度的随机颜色
        hue = np.random.randint(0, 180)  # HSV色调范围
        saturation = np.random.randint(200, 255)  # 高饱和度
        value = np.random.randint(200, 255)  # 高亮度
        
        # 转换HSV到BGR
        hsv_color = np.uint8([[[hue, saturation, value]]])
        bgr_color = cv2.cvtColor(hsv_color, cv2.COLOR_HSV2BGR)[0][0]
        colors.append(tuple(map(int, bgr_color)))
    
    return colors[:num_colors]

def create_color_palette_demo():
    """创建颜色调色板演示"""
    colors = generate_bright_colors(20)
    
    # 创建调色板图像
    palette_height = 100
    color_width = 60
    palette_width = color_width * len(colors)
    
    palette_img = np.zeros((palette_height, palette_width, 3), dtype=np.uint8)
    
    for i, color in enumerate(colors):
        x_start = i * color_width
        x_end = (i + 1) * color_width
        palette_img[:, x_start:x_end] = color
        
        # 添加颜色编号
        cv2.putText(palette_img, str(i), 
                   (x_start + 20, palette_height // 2 + 5),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
    
    return palette_img

def create_mask_demo():
    """创建mask可视化演示"""
    # 创建背景图像
    img_size = 400
    background = np.ones((img_size, img_size, 3), dtype=np.uint8) * 50  # 深灰色背景
    
    # 创建一些示例masks
    masks = []
    
    # Mask 1: 圆形
    mask1 = np.zeros((img_size, img_size), dtype=np.uint8)
    cv2.circle(mask1, (100, 100), 50, 1, -1)
    masks.append(mask1)
    
    # Mask 2: 矩形
    mask2 = np.zeros((img_size, img_size), dtype=np.uint8)
    mask2[150:250, 50:150] = 1
    masks.append(mask2)
    
    # Mask 3: 椭圆
    mask3 = np.zeros((img_size, img_size), dtype=np.uint8)
    cv2.ellipse(mask3, (300, 100), (60, 40), 0, 0, 360, 1, -1)
    masks.append(mask3)
    
    # Mask 4: 多边形
    mask4 = np.zeros((img_size, img_size), dtype=np.uint8)
    points = np.array([[250, 200], [350, 180], [380, 250], [320, 300], [220, 280]], np.int32)
    cv2.fillPoly(mask4, [points], 1)
    masks.append(mask4)
    
    # Mask 5: 重叠区域
    mask5 = np.zeros((img_size, img_size), dtype=np.uint8)
    cv2.circle(mask5, (150, 300), 40, 1, -1)
    masks.append(mask5)
    
    # 生成颜色
    colors = generate_bright_colors(len(masks))
    
    # 创建可视化
    result_img = background.copy()
    overlay = background.copy()
    
    for i, (mask, color) in enumerate(zip(masks, colors)):
        # 在overlay上绘制彩色mask
        overlay[mask > 0] = color
        
        # 计算mask中心点
        y_coords, x_coords = np.where(mask > 0)
        if len(y_coords) > 0:
            center_y = int(np.mean(y_coords))
            center_x = int(np.mean(x_coords))
            
            # 添加标签
            label_text = f"Instance {i+1}"
            cv2.putText(overlay, label_text,
                       (center_x - 30, center_y),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
    
    # 混合图像
    alpha = 0.6
    result_img = cv2.addWeighted(result_img, 1-alpha, overlay, alpha, 0)
    
    return result_img

def main():
    """创建演示图像"""
    print("Creating color demonstration images...")
    
    # 创建输出目录
    output_dir = "tools/debug/color_demo_output"
    os.makedirs(output_dir, exist_ok=True)
    
    # 创建调色板演示
    palette_img = create_color_palette_demo()
    palette_path = os.path.join(output_dir, "color_palette.png")
    cv2.imwrite(palette_path, palette_img)
    print(f"✓ Color palette saved to: {palette_path}")
    
    # 创建mask演示
    mask_demo_img = create_mask_demo()
    mask_demo_path = os.path.join(output_dir, "mask_visualization_demo.png")
    cv2.imwrite(mask_demo_path, mask_demo_img)
    print(f"✓ Mask visualization demo saved to: {mask_demo_path}")
    
    # 创建对比图（旧vs新）
    # 旧方法：随机暗色
    old_style_img = background.copy()
    old_overlay = background.copy()
    
    masks = []
    mask1 = np.zeros((400, 400), dtype=np.uint8)
    cv2.circle(mask1, (100, 100), 50, 1, -1)
    mask2 = np.zeros((400, 400), dtype=np.uint8)
    mask2[150:250, 50:150] = 1
    mask3 = np.zeros((400, 400), dtype=np.uint8)
    cv2.ellipse(mask3, (300, 100), (60, 40), 0, 0, 360, 1, -1)
    masks = [mask1, mask2, mask3]
    
    # 旧方法的暗色
    np.random.seed(42)  # 固定随机种子以便对比
    for i, mask in enumerate(masks):
        old_color = np.random.randint(0, 255, 3).tolist()  # 可能很暗
        old_overlay[mask > 0] = old_color
    
    old_style_img = cv2.addWeighted(old_style_img, 0.7, old_overlay, 0.3, 0)
    
    # 新方法：鲜艳色
    new_style_img = background.copy()
    new_overlay = background.copy()
    bright_colors = generate_bright_colors(len(masks))
    
    for i, (mask, color) in enumerate(zip(masks, bright_colors)):
        new_overlay[mask > 0] = color
    
    new_style_img = cv2.addWeighted(new_style_img, 0.4, new_overlay, 0.6, 0)
    
    # 创建对比图
    comparison_img = np.hstack([old_style_img, new_style_img])
    
    # 添加标题
    cv2.putText(comparison_img, "Old Style (Random Colors)", 
               (50, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 255, 255), 2)
    cv2.putText(comparison_img, "New Style (Bright Colors)", 
               (450, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 255, 255), 2)
    
    comparison_path = os.path.join(output_dir, "old_vs_new_comparison.png")
    cv2.imwrite(comparison_path, comparison_img)
    print(f"✓ Comparison image saved to: {comparison_path}")
    
    print("\n" + "="*50)
    print("COLOR DEMONSTRATION COMPLETED")
    print("="*50)
    print(f"Output directory: {output_dir}")
    print("Files created:")
    print("  - color_palette.png: Shows all 20 predefined bright colors")
    print("  - mask_visualization_demo.png: Example of enhanced visualization")
    print("  - old_vs_new_comparison.png: Side-by-side comparison")

if __name__ == '__main__':
    main()
