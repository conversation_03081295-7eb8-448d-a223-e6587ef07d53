#!/usr/bin/env python3
"""
测试新的赋色方案（参考simplified_projection_processor）
"""

import os
import sys
import numpy as np
import cv2
from unittest.mock import Mock

# Add project root to path
ROOT_DIR = os.path.abspath(os.path.join(os.path.dirname(__file__), os.pardir, os.pardir))
if ROOT_DIR not in sys.path:
    sys.path.insert(0, ROOT_DIR)

def create_test_image():
    """创建一个测试图像"""
    img = np.ones((300, 300, 3), dtype=np.uint8) * 128  # 灰色背景
    cv2.rectangle(img, (50, 50), (150, 150), (200, 200, 200), -1)  # 浅灰色矩形
    cv2.circle(img, (200, 200), 50, (180, 180, 180), -1)  # 浅灰色圆形
    return img

def create_test_masks():
    """创建测试用的masks"""
    masks = []
    
    # Mask 1: 矩形区域
    mask1 = np.zeros((300, 300), dtype=np.uint8)
    mask1[60:140, 60:140] = 1
    masks.append(mask1)
    
    # Mask 2: 圆形区域
    mask2 = np.zeros((300, 300), dtype=np.uint8)
    cv2.circle(mask2, (200, 200), 40, 1, -1)
    masks.append(mask2)
    
    # Mask 3: 不规则区域
    mask3 = np.zeros((300, 300), dtype=np.uint8)
    points = np.array([[100, 250], [150, 220], [200, 250], [180, 280], [120, 280]], np.int32)
    cv2.fillPoly(mask3, [points], 1)
    masks.append(mask3)
    
    return np.array(masks)

def create_mock_pred_instances():
    """创建模拟的预测实例"""
    masks = create_test_masks()
    
    # 创建mock对象
    pred_instances = Mock()
    pred_instances.masks = Mock()
    pred_instances.masks.cpu.return_value.numpy.return_value = masks
    
    return pred_instances

def test_new_coloring_scheme():
    """测试新的赋色方案"""
    print("Testing new coloring scheme (reference: simplified_projection_processor)...")
    
    # 创建测试数据
    test_img = create_test_image()
    masks = create_test_masks()
    
    # 创建白色背景的RGB图像（参考simplified_projection_processor）
    rgb_image = np.full((test_img.shape[0], test_img.shape[1], 3), 255, dtype=np.uint8)
    
    # 定义颜色方案（参考simplified_projection_processor）
    colors = [
        (255, 0, 0),    # 红色
        (0, 255, 0),    # 绿色
        (0, 0, 255),    # 蓝色
        (255, 255, 0),  # 黄色
        (255, 0, 255),  # 品红
        (0, 255, 255),  # 青色
        (128, 0, 128),  # 紫色
        (255, 165, 0),  # 橙色
    ]
    
    # 为每个实例直接赋色（不混合，直接覆盖）
    for i, mask in enumerate(masks):
        if mask.sum() == 0:  # 跳过空mask
            continue
        color = colors[i % len(colors)]
        rgb_image[mask > 0] = color
    
    # 创建输出目录
    output_dir = os.path.join(ROOT_DIR, 'tools', 'debug', 'new_coloring_test')
    os.makedirs(output_dir, exist_ok=True)
    
    # 保存结果
    cv2.imwrite(os.path.join(output_dir, 'test_original.png'), test_img)
    cv2.imwrite(os.path.join(output_dir, 'test_new_coloring.png'), rgb_image)
    
    print(f"✓ New coloring scheme test completed")
    print(f"  Original image: {output_dir}/test_original.png")
    print(f"  New coloring: {output_dir}/test_new_coloring.png")
    
    return True

def test_save_clean_visualization():
    """测试新的save_clean_visualization函数"""
    print("\nTesting save_clean_visualization function...")
    
    # 导入我们的函数
    sys.path.insert(0, os.path.join(ROOT_DIR, 'tools'))
    from inference_with_evaluators import save_clean_visualization
    
    # 创建测试数据
    test_img = create_test_image()
    
    # 创建mock result对象
    result = Mock()
    result.pred_instances = create_mock_pred_instances()
    
    # 创建输出目录
    output_dir = os.path.join(ROOT_DIR, 'tools', 'debug', 'new_coloring_test')
    os.makedirs(output_dir, exist_ok=True)
    
    # 创建临时图像文件
    test_img_path = os.path.join(output_dir, 'test_input.png')
    cv2.imwrite(test_img_path, test_img)
    
    # 测试可视化函数
    save_clean_visualization(test_img_path, result, output_dir)
    
    # 检查输出文件是否存在
    output_path = os.path.join(output_dir, 'test_input_result.png')
    if os.path.exists(output_path):
        print(f"✓ Clean visualization saved successfully: {output_path}")
        
        # 验证输出图像
        result_img = cv2.imread(output_path)
        if result_img is not None:
            print(f"✓ Output image dimensions: {result_img.shape}")
            
            # 检查背景是否为白色
            background_pixels = result_img[0, 0]  # 左上角像素
            if np.array_equal(background_pixels, [255, 255, 255]):
                print("✓ Background is white as expected")
            else:
                print(f"⚠️  Background color: {background_pixels} (expected: [255, 255, 255])")
            
            print("✓ New save_clean_visualization test passed!")
            return True
        else:
            print("❌ Failed to read output image")
            return False
    else:
        print("❌ Output file not created")
        return False

def create_comparison():
    """创建新旧方案对比"""
    print("\nCreating comparison between old and new coloring schemes...")
    
    # 创建测试图像和masks
    test_img = create_test_image()
    masks = create_test_masks()
    
    # 旧方案：混合模式
    old_result = test_img.copy()
    old_overlay = test_img.copy()
    old_colors = [
        (0, 255, 255),    # 黄色 (BGR)
        (255, 0, 255),    # 品红色
        (255, 255, 0),    # 青色
    ]
    
    for i, mask in enumerate(masks):
        color = old_colors[i % len(old_colors)]
        old_overlay[mask > 0] = color
    
    old_result = cv2.addWeighted(old_result, 0.4, old_overlay, 0.6, 0)
    
    # 新方案：直接赋色，白色背景
    new_result = np.full((test_img.shape[0], test_img.shape[1], 3), 255, dtype=np.uint8)
    new_colors = [
        (255, 0, 0),    # 红色 (BGR)
        (0, 255, 0),    # 绿色
        (0, 0, 255),    # 蓝色
    ]
    
    for i, mask in enumerate(masks):
        color = new_colors[i % len(new_colors)]
        new_result[mask > 0] = color
    
    # 创建对比图
    comparison = np.hstack([old_result, new_result])
    
    # 添加标题
    cv2.putText(comparison, "Old: Mixed with Original", 
               (20, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 0), 2)
    cv2.putText(comparison, "New: Direct Color on White", 
               (320, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 0), 2)
    
    # 保存对比图
    output_dir = os.path.join(ROOT_DIR, 'tools', 'debug', 'new_coloring_test')
    os.makedirs(output_dir, exist_ok=True)
    comparison_path = os.path.join(output_dir, 'old_vs_new_coloring.png')
    cv2.imwrite(comparison_path, comparison)
    
    print(f"✓ Comparison image saved: {comparison_path}")
    return True

def main():
    """运行所有测试"""
    print("=" * 60)
    print("TESTING NEW COLORING SCHEME")
    print("=" * 60)
    
    all_passed = True
    
    try:
        if not test_new_coloring_scheme():
            all_passed = False
        
        if not test_save_clean_visualization():
            all_passed = False
            
        if not create_comparison():
            all_passed = False
        
        if all_passed:
            print("\n" + "=" * 60)
            print("ALL TESTS PASSED! ✓")
            print("=" * 60)
            print("新的赋色方案特点:")
            print("✅ 白色背景（无原图混合）")
            print("✅ 直接颜色赋值（无透明度混合）")
            print("✅ 参考simplified_projection_processor的颜色方案")
            print("✅ 清晰的实例分割可视化")
            print("✅ 无白色边界问题")
            print("\n输出文件:")
            print("  - test_new_coloring.png: 新赋色方案效果")
            print("  - test_input_result.png: save_clean_visualization输出")
            print("  - old_vs_new_coloring.png: 新旧方案对比")
            
        else:
            print("\n❌ SOME TESTS FAILED!")
            return 1
            
    except Exception as e:
        print(f"\n❌ Test failed with exception: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0

if __name__ == '__main__':
    exit(main())
