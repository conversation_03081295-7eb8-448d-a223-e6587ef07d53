#!/usr/bin/env python3
"""
验证点云处理完整性的脚本

检查是否所有场景都已成功生成点云文件
"""

import os
import sys
from pathlib import Path

def find_all_scenes(data_root):
    """查找所有应该处理的场景"""
    all_scenes = []
    
    if not os.path.exists(data_root):
        print(f"❌ 数据根目录不存在: {data_root}")
        return all_scenes
    
    # 查找所有数据部分
    data_parts = sorted([d for d in os.listdir(data_root) 
                        if os.path.isdir(os.path.join(data_root, d)) 
                        and d.startswith('Structured3D_panorama')])
    
    print(f"📦 找到 {len(data_parts)} 个数据部分")
    
    for part in data_parts:
        part_path = os.path.join(data_root, part, 'Structured3D')
        
        if not os.path.exists(part_path):
            print(f"⚠️  跳过 {part}: Structured3D目录不存在")
            continue
        
        try:
            scenes = sorted([s for s in os.listdir(part_path) 
                           if os.path.isdir(os.path.join(part_path, s))])
            
            print(f"   📂 {part}: {len(scenes)} 个场景")
            
            for scene in scenes:
                scene_path = os.path.join(part_path, scene)
                all_scenes.append({
                    'part': part,
                    'scene': scene,
                    'scene_path': scene_path,
                    'ply_path': os.path.join(scene_path, 'point_cloud.ply')
                })
                
        except Exception as e:
            print(f"   ❌ 处理 {part} 时出错: {e}")
    
    return all_scenes

def check_processing_status(all_scenes):
    """检查每个场景的处理状态"""
    print(f"\n🔍 检查 {len(all_scenes)} 个场景的处理状态...")
    
    processed_scenes = []
    missing_scenes = []
    invalid_scenes = []
    
    for scene_info in all_scenes:
        ply_path = scene_info['ply_path']
        scene_key = f"{scene_info['part']}/{scene_info['scene']}"
        
        if os.path.exists(ply_path):
            # 检查文件大小
            try:
                file_size = os.path.getsize(ply_path)
                if file_size > 1024:  # 至少1KB
                    processed_scenes.append({
                        **scene_info,
                        'file_size': file_size,
                        'status': 'success'
                    })
                else:
                    invalid_scenes.append({
                        **scene_info,
                        'file_size': file_size,
                        'status': 'too_small'
                    })
            except Exception as e:
                invalid_scenes.append({
                    **scene_info,
                    'file_size': 0,
                    'status': f'error: {e}'
                })
        else:
            missing_scenes.append({
                **scene_info,
                'status': 'missing'
            })
    
    return processed_scenes, missing_scenes, invalid_scenes

def generate_report(all_scenes, processed_scenes, missing_scenes, invalid_scenes):
    """生成完整性报告"""
    total_count = len(all_scenes)
    success_count = len(processed_scenes)
    missing_count = len(missing_scenes)
    invalid_count = len(invalid_scenes)
    
    print(f"\n" + "=" * 60)
    print(f"📊 处理完整性报告")
    print(f"=" * 60)
    print(f"总场景数: {total_count}")
    print(f"成功处理: {success_count} ({success_count/total_count*100:.1f}%)")
    print(f"缺失文件: {missing_count} ({missing_count/total_count*100:.1f}%)")
    print(f"无效文件: {invalid_count} ({invalid_count/total_count*100:.1f}%)")
    
    # 按数据部分统计
    part_stats = {}
    for scene_info in all_scenes:
        part = scene_info['part']
        if part not in part_stats:
            part_stats[part] = {'total': 0, 'success': 0, 'missing': 0, 'invalid': 0}
        part_stats[part]['total'] += 1
    
    for scene_info in processed_scenes:
        part_stats[scene_info['part']]['success'] += 1
    
    for scene_info in missing_scenes:
        part_stats[scene_info['part']]['missing'] += 1
    
    for scene_info in invalid_scenes:
        part_stats[scene_info['part']]['invalid'] += 1
    
    print(f"\n📂 按数据部分统计:")
    for part, stats in sorted(part_stats.items()):
        success_rate = stats['success'] / stats['total'] * 100
        print(f"   {part}: {stats['success']}/{stats['total']} ({success_rate:.1f}%) 成功")
        if stats['missing'] > 0:
            print(f"      缺失: {stats['missing']} 个")
        if stats['invalid'] > 0:
            print(f"      无效: {stats['invalid']} 个")
    
    # 详细列出问题场景
    if missing_scenes:
        print(f"\n❌ 缺失点云文件的场景 ({len(missing_scenes)}个):")
        for scene_info in missing_scenes[:10]:  # 只显示前10个
            print(f"   {scene_info['part']}/{scene_info['scene']}")
        if len(missing_scenes) > 10:
            print(f"   ... 还有 {len(missing_scenes) - 10} 个")
    
    if invalid_scenes:
        print(f"\n⚠️  无效点云文件的场景 ({len(invalid_scenes)}个):")
        for scene_info in invalid_scenes[:10]:  # 只显示前10个
            size_mb = scene_info.get('file_size', 0) / 1024 / 1024
            print(f"   {scene_info['part']}/{scene_info['scene']} ({size_mb:.2f}MB)")
        if len(invalid_scenes) > 10:
            print(f"   ... 还有 {len(invalid_scenes) - 10} 个")
    
    # 生成待处理列表
    if missing_scenes or invalid_scenes:
        output_dir = "../../../output/0_depth_map_display_stru3d"
        os.makedirs(output_dir, exist_ok=True)
        
        remaining_file = os.path.join(output_dir, "remaining_scenes.txt")
        with open(remaining_file, 'w') as f:
            f.write("# 需要重新处理的场景列表\n")
            f.write(f"# 生成时间: {os.popen('date').read().strip()}\n")
            f.write(f"# 缺失场景: {len(missing_scenes)}\n")
            f.write(f"# 无效场景: {len(invalid_scenes)}\n\n")
            
            f.write("# 缺失点云文件的场景:\n")
            for scene_info in missing_scenes:
                f.write(f"{scene_info['part']}/{scene_info['scene']}\n")
            
            f.write("\n# 无效点云文件的场景:\n")
            for scene_info in invalid_scenes:
                f.write(f"{scene_info['part']}/{scene_info['scene']}\n")
        
        print(f"\n📝 待处理场景列表已保存到: {remaining_file}")
    
    return success_count == total_count

def main():
    print("点云处理完整性验证")
    print("=" * 50)
    
    # 默认数据路径
    data_root = "/home/<USER>/05_DL_dataset_wuhan/03_开源数据集/structure_3D/Panorama/"
    
    if len(sys.argv) > 1:
        data_root = sys.argv[1]
    
    print(f"📁 数据根目录: {data_root}")
    
    # 查找所有场景
    all_scenes = find_all_scenes(data_root)
    
    if not all_scenes:
        print("❌ 未找到任何场景")
        return
    
    # 检查处理状态
    processed_scenes, missing_scenes, invalid_scenes = check_processing_status(all_scenes)
    
    # 生成报告
    is_complete = generate_report(all_scenes, processed_scenes, missing_scenes, invalid_scenes)
    
    print(f"\n" + "=" * 60)
    if is_complete:
        print("🎉 所有场景都已成功处理！")
    else:
        print("⚠️  仍有场景需要处理")
        print("建议:")
        print("1. 检查失败原因")
        print("2. 重新运行处理脚本")
        print("3. 使用 remaining_scenes.txt 中的列表进行针对性处理")

if __name__ == "__main__":
    main()
