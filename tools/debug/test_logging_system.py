#!/usr/bin/env python3
"""
测试改进后的日志系统
"""

import os
import sys
import time
import logging
from datetime import datetime

def setup_logging():
    """设置日志系统，同时输出到终端和文件"""
    # 创建日志目录
    log_dir = "output/0_tmp_debug_files/0_depth_map_display_stru3d"
    os.makedirs(log_dir, exist_ok=True)
    
    # 日志文件路径
    log_file = os.path.join(log_dir, "test_log.txt")
    
    # 创建logger
    logger = logging.getLogger('TestLogger')
    logger.setLevel(logging.INFO)
    
    # 清除已有的handlers
    logger.handlers.clear()
    
    # 创建formatter
    formatter = logging.Formatter(
        '%(asctime)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # 文件handler
    file_handler = logging.FileHandler(log_file, mode='w', encoding='utf-8')
    file_handler.setLevel(logging.INFO)
    file_handler.setFormatter(formatter)
    logger.addHandler(file_handler)
    
    # 控制台handler
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)
    
    return logger, log_file

def test_logging_system():
    """测试日志系统"""
    logger, log_file = setup_logging()
    
    start_time = time.time()
    
    logger.info("=" * 60)
    logger.info("🧪 测试日志系统")
    logger.info(f"🕐 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    logger.info("=" * 60)
    logger.info(f"📝 日志文件: {log_file}")
    
    # 模拟处理过程
    test_scenes = ["scene_001", "scene_002", "scene_003"]
    success_count = 0
    failed_scenes = []
    
    for i, scene in enumerate(test_scenes, 1):
        logger.info(f"\n🔄 [{i}/{len(test_scenes)}] 处理场景: {scene}")
        logger.info(f"   📂 场景路径: /fake/path/{scene}")
        
        # 模拟处理时间
        time.sleep(0.5)
        
        if scene == "scene_002":
            # 模拟失败
            logger.error(f"   ❌ 处理场景时出错: 模拟错误")
            failed_scenes.append(f"{scene} - 模拟错误")
        else:
            # 模拟成功
            logger.info("   🔧 初始化点云读取器...")
            time.sleep(0.2)
            logger.info("   💾 导出PLY文件...")
            time.sleep(0.3)
            logger.info(f"   ✅ 成功生成点云文件")
            logger.info(f"   📊 文件大小: 15.67 MB")
            logger.info(f"   ⏱️  处理时间: 1.0 秒")
            success_count += 1
    
    # 输出总结
    total_duration = time.time() - start_time
    logger.info("\n" + "=" * 60)
    logger.info("🎉 测试完成！")
    logger.info("=" * 60)
    logger.info(f"📊 处理统计:")
    logger.info(f"   总处理场景: {len(test_scenes)}")
    logger.info(f"   成功生成: {success_count}")
    logger.info(f"   失败场景: {len(failed_scenes)}")
    logger.info(f"   成功率: {success_count/len(test_scenes)*100:.1f}%")
    logger.info(f"⏱️  总耗时: {total_duration:.1f} 秒")
    
    if failed_scenes:
        logger.info(f"\n❌ 失败场景列表:")
        for failed_scene in failed_scenes:
            logger.info(f"   - {failed_scene}")
    
    logger.info(f"\n📝 详细日志已保存到: {log_file}")
    logger.info("=" * 60)
    
    # 验证日志文件
    if os.path.exists(log_file):
        with open(log_file, 'r', encoding='utf-8') as f:
            log_content = f.read()
        
        print(f"\n✅ 日志文件验证:")
        print(f"   文件路径: {log_file}")
        print(f"   文件大小: {len(log_content)} 字符")
        print(f"   行数: {log_content.count(chr(10)) + 1}")
        
        # 显示日志文件的前几行
        lines = log_content.split('\n')[:5]
        print(f"   前5行内容:")
        for line in lines:
            print(f"     {line}")
    else:
        print(f"❌ 日志文件未找到: {log_file}")

def main():
    print("日志系统测试脚本")
    print("=" * 40)
    
    test_logging_system()
    
    print("\n" + "=" * 40)
    print("测试完成！")
    print("现在可以运行改进后的 generate_point_cloud_stru3d.py")

if __name__ == "__main__":
    main()
