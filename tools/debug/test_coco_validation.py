#!/usr/bin/env python3
"""
简化的COCO生成功能验证脚本
"""

import os
import sys
import tempfile
import json

# 添加正确的路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'dataset_converters', 'stru3d'))

def test_validation_function():
    """测试数据验证函数"""
    print("🧪 测试数据验证函数...")
    
    try:
        from generate_coco_stru3d import validate_scene_data, setup_logging
        
        # 设置日志
        logger = setup_logging()
        
        # 创建临时测试目录
        with tempfile.TemporaryDirectory() as temp_dir:
            scene_path = os.path.join(temp_dir, "test_scene")
            os.makedirs(scene_path, exist_ok=True)
            
            # 测试1: 缺少点云文件
            is_valid, result = validate_scene_data(scene_path, "test_001", logger)
            test1_pass = not is_valid and result == 'missing_point_cloud'
            print(f"   缺少点云文件: {'✅' if test1_pass else '❌'}")
            
            # 测试2: 创建正常大小的点云文件和注释文件
            ply_path = os.path.join(scene_path, "point_cloud.ply")
            with open(ply_path, 'w') as f:
                f.write("ply\nformat ascii 1.0\nend_header\n" + "1.0 2.0 3.0\n" * 100)
            
            annotation_path = os.path.join(scene_path, "annotation_3d.json")
            with open(annotation_path, 'w') as f:
                json.dump({"test": "data"}, f)
            
            is_valid, result = validate_scene_data(scene_path, "test_002", logger)
            test2_pass = is_valid and result == 'valid'
            print(f"   完整数据验证: {'✅' if test2_pass else '❌'}")
            
            return test1_pass and test2_pass
            
    except Exception as e:
        print(f"   ❌ 测试失败: {e}")
        return False

def test_argument_parsing():
    """测试参数解析"""
    print("\n🧪 测试参数解析...")
    
    try:
        from generate_coco_stru3d import config
        
        # 备份原始参数
        original_argv = sys.argv.copy()
        
        # 测试默认参数
        sys.argv = ['generate_coco_stru3d.py']
        args = config()
        test1_pass = args.max_scenes is None
        print(f"   默认max_scenes: {'✅' if test1_pass else '❌'} (值: {args.max_scenes})")
        
        # 测试指定max_scenes
        sys.argv = ['generate_coco_stru3d.py', '--max_scenes', '50']
        args = config()
        test2_pass = args.max_scenes == 50
        print(f"   指定max_scenes=50: {'✅' if test2_pass else '❌'} (值: {args.max_scenes})")
        
        # 恢复原始参数
        sys.argv = original_argv
        
        return test1_pass and test2_pass
        
    except Exception as e:
        print(f"   ❌ 测试失败: {e}")
        return False

def main():
    print("COCO生成脚本功能验证")
    print("=" * 40)
    
    # 运行测试
    test1_result = test_validation_function()
    test2_result = test_argument_parsing()
    
    print("\n" + "=" * 40)
    print("🎯 测试结果")
    print("=" * 40)
    print(f"1. 数据验证功能: {'✅ 通过' if test1_result else '❌ 失败'}")
    print(f"2. 参数解析功能: {'✅ 通过' if test2_result else '❌ 失败'}")
    
    if test1_result and test2_result:
        print(f"\n🎉 核心功能验证通过！")
        print(f"\n📋 增强功能总结:")
        print(f"✅ 添加了 --max_scenes 参数用于限制处理场景数量")
        print(f"✅ 添加了数据验证功能，自动跳过缺失点云文件的场景")
        print(f"✅ 添加了详细的日志记录和处理统计")
        print(f"✅ 保持了与原有功能的完全兼容性")
        
        print(f"\n🚀 使用示例:")
        print(f"# 处理所有场景（原有功能不变）")
        print(f"python generate_coco_stru3d.py --data_root /path/to/data --output /path/to/output")
        print(f"")
        print(f"# 限制处理前10个场景（新功能）")
        print(f"python generate_coco_stru3d.py --data_root /path/to/data --output /path/to/output --max_scenes 10")
        print(f"")
        print(f"# 脚本会自动:")
        print(f"#   - 检查每个场景的点云文件是否存在")
        print(f"#   - 跳过缺失数据的场景并记录日志")
        print(f"#   - 继续处理其他有效场景")
        print(f"#   - 输出详细的处理统计信息")
    else:
        print(f"\n⚠️  部分功能验证失败")

if __name__ == "__main__":
    main()
