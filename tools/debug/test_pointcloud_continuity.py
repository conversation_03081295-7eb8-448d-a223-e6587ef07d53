#!/usr/bin/env python3
"""
测试点云连续性修复效果的脚本

比较修复前后的点云生成效果
"""

import numpy as np
import sys
import os

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'dataset_converters', 'stru3d'))

def simulate_depth_processing():
    """模拟深度图像处理过程"""
    print("=== 模拟深度图像处理过程 ===")
    
    # 创建模拟深度图像（整数类型，模拟真实情况）
    np.random.seed(42)
    depth_img = np.random.randint(500, 3000, size=(100, 200)).astype(np.uint16)
    
    print(f"模拟深度图像尺寸: {depth_img.shape}")
    print(f"深度图像数据类型: {depth_img.dtype}")
    print(f"深度值范围: [{depth_img.min()}, {depth_img.max()}]")
    
    # 模拟相机参数
    camera_center = np.array([0, 0, 0])
    x_tick = 180.0 / depth_img.shape[0]
    y_tick = 360.0 / depth_img.shape[1]
    
    coords_old = []  # 旧方法（整数深度）
    coords_new = []  # 新方法（浮点深度）
    
    # 只处理一部分像素进行快速测试
    step = 5
    for x in range(0, depth_img.shape[0], step):
        for y in range(0, depth_img.shape[1], step):
            alpha = 90 - (x * x_tick)
            beta = y * y_tick - 180
            
            # 旧方法：直接使用整数深度
            depth_old = depth_img[x, y]
            if depth_old > 500:
                z_offset_old = depth_old * np.sin(np.deg2rad(alpha))
                coords_old.append(z_offset_old + camera_center[2])
            
            # 新方法：转换为浮点数
            depth_new = float(depth_img[x, y]) + np.random.random() * 0.1  # 添加小量随机噪声
            if depth_new > 500:
                z_offset_new = depth_new * np.sin(np.deg2rad(alpha))
                coords_new.append(z_offset_new + camera_center[2])
    
    coords_old = np.array(coords_old)
    coords_new = np.array(coords_new)
    
    print(f"\n旧方法生成的z坐标数量: {len(coords_old)}")
    print(f"新方法生成的z坐标数量: {len(coords_new)}")
    
    # 分析z坐标分布
    unique_z_old = np.unique(coords_old)
    unique_z_new = np.unique(coords_new)
    
    print(f"\n旧方法唯一z坐标数量: {len(unique_z_old)}")
    print(f"新方法唯一z坐标数量: {len(unique_z_new)}")
    
    if len(unique_z_old) > 1:
        z_diffs_old = np.diff(np.sort(unique_z_old))
        print(f"旧方法z坐标最小间隔: {z_diffs_old.min():.6f}")
        print(f"旧方法z坐标平均间隔: {z_diffs_old.mean():.6f}")
    
    if len(unique_z_new) > 1:
        z_diffs_new = np.diff(np.sort(unique_z_new))
        print(f"新方法z坐标最小间隔: {z_diffs_new.min():.6f}")
        print(f"新方法z坐标平均间隔: {z_diffs_new.mean():.6f}")
    
    # 检查连续性改善
    continuity_ratio_old = len(unique_z_old) / len(coords_old)
    continuity_ratio_new = len(unique_z_new) / len(coords_new)
    
    print(f"\n连续性指标（唯一值/总数）:")
    print(f"旧方法: {continuity_ratio_old:.3f}")
    print(f"新方法: {continuity_ratio_new:.3f}")
    
    if continuity_ratio_new > continuity_ratio_old:
        print("✅ 新方法提高了点云连续性")
    else:
        print("⚠️  新方法未显著改善连续性")

def test_unique_removal_effect():
    """测试去重操作的影响"""
    print("\n=== 测试去重操作的影响 ===")
    
    # 创建包含一些重复坐标的测试数据
    np.random.seed(42)
    n_points = 1000
    coords = np.random.randn(n_points, 3) * 10
    
    # 人为添加一些重复坐标
    coords[100:110] = coords[0]  # 添加10个重复点
    coords[200:205] = coords[1]  # 添加5个重复点
    
    print(f"原始点云数量: {len(coords)}")
    
    # 应用去重
    unique_coords, unique_ind = np.unique(coords, return_index=True, axis=0)
    coords_after_unique = coords[unique_ind]
    
    print(f"去重后点云数量: {len(coords_after_unique)}")
    print(f"移除的重复点数量: {len(coords) - len(coords_after_unique)}")
    
    reduction_ratio = len(coords_after_unique) / len(coords)
    print(f"保留比例: {reduction_ratio:.3f}")
    
    if reduction_ratio < 0.95:
        print("⚠️  去重操作显著减少了点云数量")
        print("建议: 设置 remove_duplicates=False 以保持点云密度")
    else:
        print("✅ 去重操作对点云数量影响较小")

def compare_processing_methods():
    """比较不同处理方法的效果"""
    print("\n=== 比较不同处理方法 ===")
    
    # 模拟真实的深度图像数据
    np.random.seed(42)
    depth_img = np.random.randint(1000, 2000, size=(50, 100)).astype(np.uint16)
    
    methods = {
        "原始方法（整数深度+去重）": {"float_depth": False, "remove_duplicates": True},
        "改进方法1（浮点深度+去重）": {"float_depth": True, "remove_duplicates": True},
        "改进方法2（浮点深度+不去重）": {"float_depth": True, "remove_duplicates": False},
    }
    
    results = {}
    
    for method_name, config in methods.items():
        coords = []
        
        # 模拟点云生成
        for x in range(0, depth_img.shape[0], 2):
            for y in range(0, depth_img.shape[1], 2):
                alpha = 90 - (x * 180.0 / depth_img.shape[0])
                beta = y * 360.0 / depth_img.shape[1] - 180
                
                if config["float_depth"]:
                    depth = float(depth_img[x, y]) + np.random.random() * 0.1
                else:
                    depth = depth_img[x, y]
                
                if depth > 500:
                    z_offset = depth * np.sin(np.deg2rad(alpha))
                    coords.append([0, 0, z_offset])
        
        coords = np.array(coords)
        
        # 应用去重（如果需要）
        if config["remove_duplicates"] and len(coords) > 0:
            unique_coords, unique_ind = np.unique(coords, return_index=True, axis=0)
            coords = coords[unique_ind]
        
        # 分析结果
        if len(coords) > 0:
            unique_z = np.unique(coords[:, 2])
            continuity_ratio = len(unique_z) / len(coords)
            
            results[method_name] = {
                "total_points": len(coords),
                "unique_z_values": len(unique_z),
                "continuity_ratio": continuity_ratio
            }
        else:
            results[method_name] = {
                "total_points": 0,
                "unique_z_values": 0,
                "continuity_ratio": 0
            }
    
    # 显示比较结果
    print("\n方法比较结果:")
    print("-" * 80)
    print(f"{'方法':<25} {'总点数':<10} {'唯一Z值':<10} {'连续性':<10}")
    print("-" * 80)
    
    for method_name, result in results.items():
        print(f"{method_name:<25} {result['total_points']:<10} {result['unique_z_values']:<10} {result['continuity_ratio']:<10.3f}")
    
    # 推荐最佳方法
    best_method = max(results.keys(), key=lambda k: results[k]['continuity_ratio'])
    print(f"\n推荐方法: {best_method}")

def main():
    print("点云连续性测试脚本")
    print("=" * 50)
    
    # 运行各种测试
    simulate_depth_processing()
    test_unique_removal_effect()
    compare_processing_methods()
    
    print("\n" + "=" * 50)
    print("测试完成！")
    print("\n修复建议:")
    print("1. 使用浮点数深度值以提高精度")
    print("2. 添加小量随机噪声避免完全重复的坐标")
    print("3. 考虑设置 remove_duplicates=False 以保持点云密度")
    print("4. 重新运行 generate_point_cloud_stru3d.py 测试效果")

if __name__ == "__main__":
    main()
