#!/usr/bin/env python3
"""
测试TP/FP/FN计算逻辑的正确性
"""

def test_tp_fp_fn_calculation():
    """测试TP/FP/FN计算逻辑"""
    print("Testing TP/FP/FN calculation logic...")
    
    # 模拟几张图像的结果
    per_image_results = [
        # 图像1: 3个预测，2个GT，1个匹配
        {
            'tp': 1, 'fp': 2, 'fn': 1,
            'matched_pairs': [{'category_id': 1}]
        },
        # 图像2: 2个预测，2个GT，2个匹配
        {
            'tp': 2, 'fp': 0, 'fn': 0,
            'matched_pairs': [{'category_id': 1}, {'category_id': 1}]
        },
        # 图像3: 1个预测，3个GT，1个匹配
        {
            'tp': 1, 'fp': 0, 'fn': 2,
            'matched_pairs': [{'category_id': 1}]
        }
    ]
    
    # 计算总体指标
    total_tp = sum(result['tp'] for result in per_image_results)
    total_fp = sum(result['fp'] for result in per_image_results)
    total_fn = sum(result['fn'] for result in per_image_results)
    
    print(f"Total TP: {total_tp}")
    print(f"Total FP: {total_fp}")
    print(f"Total FN: {total_fn}")
    
    # 验证结果
    expected_tp = 1 + 2 + 1  # 4
    expected_fp = 2 + 0 + 0  # 2
    expected_fn = 1 + 0 + 2  # 3
    
    assert total_tp == expected_tp, f"TP error: expected {expected_tp}, got {total_tp}"
    assert total_fp == expected_fp, f"FP error: expected {expected_fp}, got {total_fp}"
    assert total_fn == expected_fn, f"FN error: expected {expected_fn}, got {total_fn}"
    
    # 计算精确率和召回率
    precision = total_tp / (total_tp + total_fp) if (total_tp + total_fp) > 0 else 0.0
    recall = total_tp / (total_tp + total_fn) if (total_tp + total_fn) > 0 else 0.0
    
    expected_precision = 4 / (4 + 2)  # 4/6 = 0.6667
    expected_recall = 4 / (4 + 3)     # 4/7 = 0.5714
    
    print(f"Precision: {precision:.4f} (expected: {expected_precision:.4f})")
    print(f"Recall: {recall:.4f} (expected: {expected_recall:.4f})")
    
    assert abs(precision - expected_precision) < 1e-4, f"Precision error"
    assert abs(recall - expected_recall) < 1e-4, f"Recall error"
    
    print("✓ TP/FP/FN calculation test passed!")

def test_edge_cases():
    """测试边界情况"""
    print("\nTesting edge cases...")
    
    # 情况1: 没有预测，有GT
    result1 = {'tp': 0, 'fp': 0, 'fn': 3}
    precision1 = result1['tp'] / (result1['tp'] + result1['fp']) if (result1['tp'] + result1['fp']) > 0 else 0.0
    recall1 = result1['tp'] / (result1['tp'] + result1['fn']) if (result1['tp'] + result1['fn']) > 0 else 0.0
    print(f"No predictions, 3 GT: Precision={precision1:.4f}, Recall={recall1:.4f}")
    assert precision1 == 0.0 and recall1 == 0.0
    
    # 情况2: 有预测，没有GT
    result2 = {'tp': 0, 'fp': 2, 'fn': 0}
    precision2 = result2['tp'] / (result2['tp'] + result2['fp']) if (result2['tp'] + result2['fp']) > 0 else 0.0
    recall2 = result2['tp'] / (result2['tp'] + result2['fn']) if (result2['tp'] + result2['fn']) > 0 else 0.0
    print(f"2 predictions, no GT: Precision={precision2:.4f}, Recall={recall2:.4f}")
    assert precision2 == 0.0 and recall2 == 0.0
    
    # 情况3: 没有预测，没有GT
    result3 = {'tp': 0, 'fp': 0, 'fn': 0}
    precision3 = result3['tp'] / (result3['tp'] + result3['fp']) if (result3['tp'] + result3['fp']) > 0 else 0.0
    recall3 = result3['tp'] / (result3['tp'] + result3['fn']) if (result3['tp'] + result3['fn']) > 0 else 0.0
    print(f"No predictions, no GT: Precision={precision3:.4f}, Recall={recall3:.4f}")
    # 这种情况下，通常认为precision和recall都是1.0（完美匹配）
    
    print("✓ Edge cases test passed!")

def test_recall_greater_than_one_issue():
    """测试召回率大于1的问题"""
    print("\nTesting recall > 1 issue...")
    
    # 这种情况不应该发生，如果发生说明TP计算有问题
    # TP不能大于GT的总数
    
    # 正确的情况
    tp, fp, fn = 4, 2, 3
    total_gt = tp + fn  # 7
    total_pred = tp + fp  # 6
    
    print(f"TP: {tp}, FP: {fp}, FN: {fn}")
    print(f"Total GT: {total_gt}, Total Predictions: {total_pred}")
    
    precision = tp / (tp + fp) if (tp + fp) > 0 else 0.0
    recall = tp / (tp + fn) if (tp + fn) > 0 else 0.0
    
    print(f"Precision: {precision:.4f}")
    print(f"Recall: {recall:.4f}")
    
    # 验证召回率不应该大于1
    assert recall <= 1.0, f"Recall should not exceed 1.0, got {recall}"
    assert precision <= 1.0, f"Precision should not exceed 1.0, got {precision}"
    
    print("✓ Recall bounds test passed!")

def main():
    """运行所有测试"""
    print("=" * 60)
    print("TESTING TP/FP/FN CALCULATION LOGIC")
    print("=" * 60)
    
    try:
        test_tp_fp_fn_calculation()
        test_edge_cases()
        test_recall_greater_than_one_issue()
        
        print("\n" + "=" * 60)
        print("ALL TESTS PASSED! ✓")
        print("The TP/FP/FN calculation logic is correct.")
        print("=" * 60)
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0

if __name__ == '__main__':
    exit(main())
