#!/usr/bin/env python3
"""
测试点云生成加速效果的脚本

比较原始方法、向量化方法和多线程方法的性能
"""

import time
import numpy as np
import os
import sys
from concurrent.futures import ThreadPoolExecutor

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'dataset_converters', 'stru3d'))

def simulate_depth_processing_original():
    """模拟原始的双重循环处理方式"""
    print("🐌 测试原始方法（双重循环）...")
    
    # 模拟深度图像
    depth_img = np.random.randint(500, 3000, size=(100, 200)).astype(np.uint16)
    rgb_img = np.random.randint(0, 255, size=(100, 200, 3)).astype(np.uint8)
    
    coords = []
    colors = []
    
    start_time = time.time()
    
    x_tick = 180.0 / depth_img.shape[0]
    y_tick = 360.0 / depth_img.shape[1]
    
    for x in range(0, depth_img.shape[0]):
        for y in range(0, depth_img.shape[1]):
            alpha = 90 - (x * x_tick)
            beta = y * y_tick - 180
            
            depth = float(depth_img[x, y])
            
            if depth > 500:
                z_offset = depth * np.sin(np.deg2rad(alpha))
                xy_offset = depth * np.cos(np.deg2rad(alpha))
                x_offset = xy_offset * np.sin(np.deg2rad(beta))
                y_offset = xy_offset * np.cos(np.deg2rad(beta))
                
                coords.append([x_offset, y_offset, z_offset])
                colors.append(rgb_img[x, y])
    
    duration = time.time() - start_time
    
    print(f"   生成点数: {len(coords)}")
    print(f"   处理时间: {duration:.3f} 秒")
    
    return len(coords), duration

def simulate_depth_processing_vectorized():
    """模拟向量化处理方式"""
    print("⚡ 测试向量化方法...")
    
    # 模拟深度图像
    depth_img = np.random.randint(500, 3000, size=(100, 200)).astype(np.uint16)
    rgb_img = np.random.randint(0, 255, size=(100, 200, 3)).astype(np.uint8)
    
    start_time = time.time()
    
    # 计算角度步长
    x_tick = 180.0 / depth_img.shape[0]
    y_tick = 360.0 / depth_img.shape[1]
    
    # 创建坐标网格
    x_indices, y_indices = np.meshgrid(
        np.arange(depth_img.shape[0]), 
        np.arange(depth_img.shape[1]), 
        indexing='ij'
    )
    
    # 向量化计算角度
    alpha = 90 - (x_indices * x_tick)
    beta = y_indices * y_tick - 180
    
    # 转换为浮点数
    depth = depth_img.astype(np.float32)
    
    # 创建有效深度掩码
    valid_mask = depth > 500.0
    
    if np.sum(valid_mask) > 0:
        # 只处理有效像素
        valid_depth = depth[valid_mask]
        valid_alpha = alpha[valid_mask]
        valid_beta = beta[valid_mask]
        valid_rgb = rgb_img[valid_mask]
        
        # 向量化计算3D坐标
        alpha_rad = np.deg2rad(valid_alpha)
        beta_rad = np.deg2rad(valid_beta)
        
        z_offset = valid_depth * np.sin(alpha_rad)
        xy_offset = valid_depth * np.cos(alpha_rad)
        x_offset = xy_offset * np.sin(beta_rad)
        y_offset = xy_offset * np.cos(beta_rad)
        
        # 组合坐标
        coords = np.column_stack([x_offset, y_offset, z_offset])
        colors = valid_rgb
    else:
        coords = np.array([])
        colors = np.array([])
    
    duration = time.time() - start_time
    
    print(f"   生成点数: {len(coords)}")
    print(f"   处理时间: {duration:.3f} 秒")
    
    return len(coords), duration

def simulate_single_scene_processing(scene_id):
    """模拟单个场景处理（用于多线程测试）"""
    # 模拟处理时间
    processing_time = np.random.uniform(0.5, 2.0)  # 0.5-2秒随机处理时间
    time.sleep(processing_time)
    
    # 模拟生成的点数
    point_count = np.random.randint(50000, 200000)
    
    return {
        'scene_id': scene_id,
        'point_count': point_count,
        'processing_time': processing_time
    }

def test_multithreading_performance():
    """测试多线程性能"""
    print("🧵 测试多线程处理...")
    
    num_scenes = 8
    thread_counts = [1, 2, 4, 8]
    
    results = {}
    
    for num_threads in thread_counts:
        print(f"\n   使用 {num_threads} 个线程处理 {num_scenes} 个场景:")
        
        start_time = time.time()
        
        if num_threads == 1:
            # 单线程处理
            scene_results = []
            for i in range(num_scenes):
                result = simulate_single_scene_processing(i)
                scene_results.append(result)
        else:
            # 多线程处理
            with ThreadPoolExecutor(max_workers=num_threads) as executor:
                futures = [executor.submit(simulate_single_scene_processing, i) for i in range(num_scenes)]
                scene_results = [future.result() for future in futures]
        
        total_duration = time.time() - start_time
        total_points = sum(result['point_count'] for result in scene_results)
        
        results[num_threads] = {
            'total_duration': total_duration,
            'total_points': total_points,
            'avg_time_per_scene': total_duration / num_scenes
        }
        
        print(f"     总耗时: {total_duration:.2f} 秒")
        print(f"     平均每场景: {total_duration/num_scenes:.2f} 秒")
        print(f"     总点数: {total_points:,}")
    
    return results

def analyze_performance_improvements():
    """分析性能改善效果"""
    print("\n" + "=" * 60)
    print("📊 性能分析报告")
    print("=" * 60)
    
    # 测试向量化改善
    print("\n1. 向量化计算性能对比:")
    original_points, original_time = simulate_depth_processing_original()
    vectorized_points, vectorized_time = simulate_depth_processing_vectorized()
    
    if original_time > 0:
        speedup_ratio = original_time / vectorized_time
        print(f"   向量化加速比: {speedup_ratio:.2f}x")
        print(f"   时间减少: {(1 - vectorized_time/original_time)*100:.1f}%")
    
    # 测试多线程改善
    print("\n2. 多线程处理性能对比:")
    threading_results = test_multithreading_performance()
    
    baseline_time = threading_results[1]['total_duration']
    
    print(f"\n   多线程加速效果:")
    for threads, result in threading_results.items():
        if threads > 1:
            speedup = baseline_time / result['total_duration']
            efficiency = speedup / threads * 100
            print(f"     {threads} 线程: {speedup:.2f}x 加速, {efficiency:.1f}% 效率")
    
    # 综合建议
    print(f"\n3. 优化建议:")
    best_vectorized_speedup = original_time / vectorized_time if original_time > 0 else 1
    best_threading_speedup = max(baseline_time / result['total_duration'] 
                                for result in threading_results.values())
    
    total_expected_speedup = best_vectorized_speedup * best_threading_speedup
    
    print(f"   向量化预期加速: {best_vectorized_speedup:.1f}x")
    print(f"   多线程预期加速: {best_threading_speedup:.1f}x")
    print(f"   综合预期加速: {total_expected_speedup:.1f}x")
    
    if total_expected_speedup > 5:
        print("   🚀 建议同时启用向量化和多线程处理")
    elif best_vectorized_speedup > 3:
        print("   ⚡ 建议优先启用向量化计算")
    elif best_threading_speedup > 2:
        print("   🧵 建议启用多线程处理")
    else:
        print("   📝 当前配置已较为优化")

def main():
    print("点云生成加速性能测试")
    print("=" * 50)
    
    print("🔧 测试环境信息:")
    print(f"   NumPy版本: {np.__version__}")
    print(f"   CPU核心数: {os.cpu_count()}")
    
    # 运行性能分析
    analyze_performance_improvements()
    
    print("\n" + "=" * 50)
    print("🎯 使用建议:")
    print("1. 启用向量化计算: --use_vectorized True")
    print("2. 设置合适的线程数: --num_workers 4")
    print("3. 测试命令示例:")
    print("   python generate_point_cloud_stru3d.py --num_workers 4 --use_vectorized True --max_scenes 2")
    print("=" * 50)

if __name__ == "__main__":
    main()
