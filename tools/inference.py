import os
import argparse
import sys

# Ensure project root is on PYTHONPATH so that `tools.custom_metrics` etc. can be imported
ROOT_DIR = os.path.abspath(os.path.join(os.path.dirname(__file__), os.pardir))
if ROOT_DIR not in sys.path:
    sys.path.insert(0, ROOT_DIR)

from mmengine.config import Config
from mmdet.apis import inference_detector, init_detector
from mmdet.visualization import DetLocalVisualizer
import cv2
import numpy as np

def parse_args():
    parser = argparse.ArgumentParser(description='Inference with Mask2Former')
    parser.add_argument('--config', default='../configs/mask2former_swin.py', help='Config file path')
    parser.add_argument('--checkpoint', help='Checkpoint file path')
    parser.add_argument('--input', help='Input image or folder path')
    parser.add_argument('--output', default='output', help='Output folder path')
    parser.add_argument('--device', default='cuda:0', help='Device used for inference')
    args = parser.parse_args()
    return args

def main():
    args = parse_args()
    
    # 初始化模型
    model = init_detector(args.config, args.checkpoint, device=args.device)
    
    # 初始化可视化工具
    visualizer = DetLocalVisualizer()
    visualizer.dataset_meta = model.dataset_meta  # 设置数据集元信息
    
    # 检查输入路径是否为文件夹
    if os.path.isdir(args.input):
        input_images = [os.path.join(args.input, f) for f in os.listdir(args.input) if f.lower().endswith(('.png', '.jpg', '.jpeg'))]
    else:
        input_images = [args.input]
    
    # 创建输出文件夹
    os.makedirs(args.output, exist_ok=True)
    
    for img_path in input_images:

        # 加载图像为 NumPy 数组
        image = cv2.imread(img_path)
        if image is None:
            print(f"无法加载图像: {img_path}")
            continue
        
        # BGR 转 RGB（因为 OpenCV 默认加载为 BGR 格式）
        image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)

        # 推理
        result = inference_detector(model, image)

        masks = result.pred_instances.get('masks')[result.pred_instances.get('scores') > 0.5]

        # 创建空白图像并保存每个mask
        h, w = image.shape[:2]
        mask_image = np.zeros((h, w), dtype=np.uint8)
        border_image = np.zeros((h, w), dtype=np.uint8)

        for i, mask in enumerate(masks):
            # 将mask转换为uint8类型
            mask_array = mask.cpu().numpy().astype(np.uint8) * 255
            # 将mask复制到空白图像
            mask_image[:] += mask_array

            # 查找轮廓
            contours, _ = cv2.findContours(mask_array, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            # 绘制轮廓到边界图像上
            cv2.drawContours(border_image, contours, -1, 255, 1)
            
        # 设置mask输出路径
        mask_filename = os.path.splitext(os.path.basename(img_path))[0]
        mask_out_file = os.path.join(args.output, f"{mask_filename}_mask.png")
        border_file = os.path.join(args.output, f"{mask_filename}_border.png")

        # 保存mask
        cv2.imwrite(mask_out_file, mask_image)
        cv2.imwrite(border_file, border_image)
        print(f"Mask {i} 已保存至: {mask_out_file}")
        
        # 设置输出文件路径
        out_file = os.path.join(args.output, os.path.basename(img_path))
        
        # 使用可视化工具保存结果
        visualizer.add_datasample(
            name=os.path.basename(img_path),
            image=image,  # 传递图像数组
            data_sample=result,
            draw_gt=False,
            draw_pred=True,
            out_file=out_file,
            pred_score_thr=0.5  # 设置分数阈值
        )
        
        print(f"结果已保存至: {out_file}")

if __name__ == '__main__':
    main()