"""
改进的像素级评价指标
提供多种像素级评价方式，解决原方案的潜在问题

Author: Assistant
Date: 2025-01-31
"""

import numpy as np
import torch
from typing import Dict, List, Optional, Sequence, Tuple, Union
from collections import defaultdict
import json
import os

from mmdet.registry import METRICS
from mmdet.evaluation.metrics import CocoMetric
from mmengine.logging import MMLogger
from mmengine.fileio import dump
from pycocotools.coco import COCO
from pycocotools import mask as mask_util


@METRICS.register_module()
class ImprovedPixelMetric(CocoMetric):
    """
    改进的像素级评价指标
    
    提供三种评价模式：
    1. pure_pixel: 纯像素级评价，不考虑实例边界
    2. instance_aware: 实例感知的像素级评价（原方案）
    3. weighted_pixel: 基于IoU质量加权的像素级评价
    """
    
    def __init__(self,
                 ann_file: str,
                 metric: Union[str, List[str]] = 'segm',
                 evaluation_mode: str = 'pure_pixel',  # 'pure_pixel', 'instance_aware', 'weighted_pixel'
                 num_classes: int = 16,
                 class_names: Optional[List[str]] = None,
                 iou_threshold: float = 0.5,
                 score_threshold: float = 0.3,
                 nms_pre: int = 1000,
                 collect_device: str = 'cpu',
                 prefix: Optional[str] = None,
                 format_only: bool = False,
                 output_dir: Optional[str] = None,
                 binary_classification: bool = False,  # 是否启用二分类模式
                 room_classes: Optional[List[int]] = None,  # 房间类别ID列表
                 non_room_classes: Optional[List[int]] = None,  # 非房间类别ID列表
                 **kwargs):
        """
        初始化改进的像素级评价器
        
        Args:
            evaluation_mode (str): 评价模式
                - 'pure_pixel': 纯像素级评价
                - 'instance_aware': 实例感知像素级评价
                - 'weighted_pixel': 加权像素级评价
        """
        super().__init__(
            ann_file=ann_file,
            metric=metric,
            collect_device=collect_device,
            prefix=prefix,
            format_only=format_only,
            **kwargs
        )
        
        self.evaluation_mode = evaluation_mode
        self.num_classes = num_classes
        self.iou_threshold = iou_threshold
        self.score_threshold = score_threshold
        self.nms_pre = nms_pre
        self.output_dir = output_dir

        # 二分类支持：房间 vs 背景
        self.binary_classification = binary_classification
        self.room_classes = room_classes or list(range(15))  # 默认前15个类别为房间
        self.non_room_classes = non_room_classes or [15, 16, 17]  # 默认后3个类别为非房间（门窗等）

        # 在二分类模式下，我们将所有房间类别视为前景，其他所有区域（包括门窗和真正的背景）视为背景

        # 获取类别信息
        if hasattr(self, '_coco_api') and self._coco_api is not None:
            try:
                self.cat_names = [
                    self._coco_api.load_cats(i)[0]['name'] for i in self.cat_ids
                ]
                self.class_names = class_names or self.cat_names
            except:
                self.class_names = class_names or [f'class_{i}' for i in range(num_classes)]
        else:
            self.class_names = class_names or [f'class_{i}' for i in range(num_classes)]

        self.logger = MMLogger.get_current_instance()
        self.detailed_results = []

        # 验证评价模式
        valid_modes = ['pure_pixel', 'instance_aware', 'weighted_pixel']
        if self.evaluation_mode not in valid_modes:
            raise ValueError(f"evaluation_mode must be one of {valid_modes}")

        # 打印二分类配置信息
        if self.binary_classification:
            print(f"  🔄 Binary classification mode enabled:")
            print(f"    Room classes: {self.room_classes}")
            print(f"    Non-room classes: {self.non_room_classes}")
    
    def process(self, data_batch: dict, data_samples: Sequence[dict]) -> None:
        """处理一个批次的预测结果"""
        for data_sample in data_samples:
            img_id = data_sample.get('img_id', 0)
            
            # 获取预测结果
            pred_instances = data_sample.get('pred_instances', None)
            if pred_instances is None:
                self.logger.warning(f"No predictions for image {img_id}")
                continue
                
            if 'masks' not in pred_instances:
                self.logger.warning(f"No masks in predictions for image {img_id}")
                continue
            
            # 提取预测信息
            pred_masks = pred_instances['masks']
            pred_labels = pred_instances['labels']
            pred_scores = pred_instances['scores']
            
            # 转换为numpy数组
            if isinstance(pred_masks, torch.Tensor):
                pred_masks = pred_masks.cpu().numpy()
            if isinstance(pred_labels, torch.Tensor):
                pred_labels = pred_labels.cpu().numpy()
            if isinstance(pred_scores, torch.Tensor):
                pred_scores = pred_scores.cpu().numpy()
            
            # 获取真实标注
            gt_anns = self._coco_api.load_anns(
                self._coco_api.get_ann_ids(img_ids=[img_id])
            )
            
            # 提取真实掩码和标签
            gt_masks, gt_labels = self._extract_gt_masks_labels(
                gt_anns, pred_masks.shape[1:3]
            )
            
            # 根据评价模式计算指标
            if self.evaluation_mode == 'pure_pixel':
                result = self._compute_pure_pixel_metrics(
                    pred_masks, pred_labels, pred_scores,
                    gt_masks, gt_labels, img_id
                )
            elif self.evaluation_mode == 'instance_aware':
                result = self._compute_instance_aware_metrics(
                    pred_masks, pred_labels, pred_scores,
                    gt_masks, gt_labels, img_id
                )
            elif self.evaluation_mode == 'weighted_pixel':
                result = self._compute_weighted_pixel_metrics(
                    pred_masks, pred_labels, pred_scores,
                    gt_masks, gt_labels, img_id
                )
            
            self.results.append(result)
    
    def _extract_gt_masks_labels(self, gt_anns, img_shape):
        """提取真实掩码和标签"""
        gt_masks = []
        gt_labels = []
        
        for ann in gt_anns:
            if isinstance(ann['segmentation'], list):
                # polygon格式
                rle = mask_util.frPyObjects(
                    ann['segmentation'], img_shape[0], img_shape[1]
                )
                mask = mask_util.decode(rle)
                if len(mask.shape) == 3:
                    mask = mask.sum(axis=2) > 0
            else:
                # RLE格式
                mask = mask_util.decode(ann['segmentation'])
                
            gt_masks.append(mask)
            # 安全地获取类别索引
            if self.cat_ids is not None:
                try:
                    gt_labels.append(self.cat_ids.index(ann['category_id']))
                except ValueError:
                    # 如果category_id不在cat_ids中，使用原始category_id
                    gt_labels.append(ann['category_id'])
            else:
                # 如果cat_ids为None，直接使用category_id
                gt_labels.append(ann['category_id'])
        
        gt_masks = np.array(gt_masks) if gt_masks else np.zeros((0, *img_shape))
        gt_labels = np.array(gt_labels) if gt_labels else np.zeros(0, dtype=np.int32)
        
        return gt_masks, gt_labels
    
    def _compute_pure_pixel_metrics(self, pred_masks, pred_labels, pred_scores,
                                   gt_masks, gt_labels, img_id):
        """纯像素级评价：直接比较语义分割结果"""
        # 过滤低置信度预测
        valid_inds = pred_scores >= self.score_threshold
        pred_masks = pred_masks[valid_inds]
        pred_labels = pred_labels[valid_inds]
        pred_scores = pred_scores[valid_inds]
        
        if len(pred_scores) > self.nms_pre:
            top_inds = np.argsort(pred_scores)[-self.nms_pre:]
            pred_masks = pred_masks[top_inds]
            pred_labels = pred_labels[top_inds]
            pred_scores = pred_scores[top_inds]
        
        # 创建语义分割图
        img_shape = pred_masks.shape[1:3] if len(pred_masks) > 0 else gt_masks.shape[1:3]
        pred_semantic = np.zeros(img_shape, dtype=np.int32)
        gt_semantic = np.zeros(img_shape, dtype=np.int32)
        
        # 构建预测语义图（后面的预测覆盖前面的，因为按分数排序）
        for i, (mask, label) in enumerate(zip(pred_masks, pred_labels)):
            if self.binary_classification:
                # 二分类模式：将所有房间类别映射为1（前景），其他保持为0（背景）
                if label in self.room_classes:
                    pred_semantic[mask > 0] = 1  # 房间（前景）
                # 非房间类别（门窗等）不设置，保持为0（背景）
            else:
                # 多分类模式：保持原始标签
                pred_semantic[mask > 0] = label + 1  # +1避免与背景0混淆

        # 构建真值语义图
        for mask, label in zip(gt_masks, gt_labels):
            if self.binary_classification:
                # 二分类模式：将所有房间类别映射为1（前景），其他保持为0（背景）
                if label in self.room_classes:
                    gt_semantic[mask > 0] = 1  # 房间（前景）
                # 非房间类别（门窗等）和真正的背景都保持为0（背景）
            else:
                # 多分类模式：保持原始标签
                gt_semantic[mask > 0] = label + 1  # +1避免与背景0混淆
        
        # 计算像素级指标
        if self.binary_classification:
            # 二分类模式：房间(1) vs 背景(0)
            # 计算房间类别的指标
            pred_room = pred_semantic == 1
            gt_room = gt_semantic == 1
            pred_bg = pred_semantic == 0
            gt_bg = gt_semantic == 0

            # 房间类别指标
            room_tp = np.sum(pred_room & gt_room)
            room_fp = np.sum(pred_room & ~gt_room)
            room_fn = np.sum(~pred_room & gt_room)

            # 背景类别指标
            bg_tp = np.sum(pred_bg & gt_bg)
            bg_fp = np.sum(pred_bg & ~gt_bg)
            bg_fn = np.sum(~pred_bg & gt_bg)

            # 整体指标（基于房间类别）
            tp = room_tp
            fp = room_fp
            fn = room_fn

            # 分类别指标
            class_stats = {
                'room': {'tp': int(room_tp), 'fp': int(room_fp), 'fn': int(room_fn)},
                'background': {'tp': int(bg_tp), 'fp': int(bg_fp), 'fn': int(bg_fn)}
            }
        else:
            # 多分类模式：只考虑前景像素（类别>0）
            pred_fg = pred_semantic > 0
            gt_fg = gt_semantic > 0

            tp = np.sum(pred_fg & gt_fg)
            fp = np.sum(pred_fg & ~gt_fg)
            fn = np.sum(~pred_fg & gt_fg)

            # 计算分类别指标
            class_stats = defaultdict(lambda: {'tp': 0, 'fp': 0, 'fn': 0})
            for class_id in range(1, self.num_classes + 1):
                pred_class = pred_semantic == class_id
                gt_class = gt_semantic == class_id

                class_tp = np.sum(pred_class & gt_class)
                class_fp = np.sum(pred_class & ~gt_class)
                class_fn = np.sum(~pred_class & gt_class)

                if class_tp + class_fp + class_fn > 0:  # 只记录有数据的类别
                    class_stats[class_id - 1] = {
                        'tp': int(class_tp),
                        'fp': int(class_fp),
                        'fn': int(class_fn)
                    }
        
        # 计算指标
        precision = tp / (tp + fp) if (tp + fp) > 0 else 0
        recall = tp / (tp + fn) if (tp + fn) > 0 else 0
        f1 = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0
        
        return {
            'img_id': img_id,
            'mode': 'pure_pixel',
            'precision': precision,
            'recall': recall,
            'f1_score': f1,
            'tp': int(tp),
            'fp': int(fp),
            'fn': int(fn),
            'n_pred': len(pred_masks),
            'n_gt': len(gt_masks),
            'class_stats': dict(class_stats)
        }
    
    def _compute_instance_aware_metrics(self, pred_masks, pred_labels, pred_scores,
                                       gt_masks, gt_labels, img_id):
        """实例感知的像素级评价（原方案）"""
        # 这里复用原来的逻辑，但简化一些
        # 过滤和排序
        valid_inds = pred_scores >= self.score_threshold
        pred_masks = pred_masks[valid_inds]
        pred_labels = pred_labels[valid_inds]
        pred_scores = pred_scores[valid_inds]
        
        if len(pred_scores) > self.nms_pre:
            top_inds = np.argsort(pred_scores)[-self.nms_pre:]
            pred_masks = pred_masks[top_inds]
            pred_labels = pred_labels[top_inds]
            pred_scores = pred_scores[top_inds]
        
        # 实例匹配
        matches, unmatched_preds, unmatched_gts = self._match_instances(
            pred_masks, pred_labels, gt_masks, gt_labels
        )
        
        # 计算像素级指标
        total_tp, total_fp, total_fn = 0, 0, 0
        class_stats = defaultdict(lambda: {'tp': 0, 'fp': 0, 'fn': 0})
        
        # 匹配的实例对
        for pred_idx, gt_idx in matches:
            pred_mask = pred_masks[pred_idx]
            gt_mask = gt_masks[gt_idx]
            label = gt_labels[gt_idx]
            
            tp = np.sum(pred_mask & gt_mask)
            fp = np.sum(pred_mask & ~gt_mask)
            fn = np.sum(~pred_mask & gt_mask)
            
            total_tp += tp
            total_fp += fp
            total_fn += fn
            
            class_stats[label]['tp'] += tp
            class_stats[label]['fp'] += fp
            class_stats[label]['fn'] += fn
        
        # 未匹配的预测
        for pred_idx in unmatched_preds:
            pred_mask = pred_masks[pred_idx]
            pred_label = pred_labels[pred_idx]
            fp = np.sum(pred_mask)
            
            total_fp += fp
            class_stats[pred_label]['fp'] += fp
        
        # 未匹配的真值
        for gt_idx in unmatched_gts:
            gt_mask = gt_masks[gt_idx]
            gt_label = gt_labels[gt_idx]
            fn = np.sum(gt_mask)
            
            total_fn += fn
            class_stats[gt_label]['fn'] += fn
        
        precision = total_tp / (total_tp + total_fp) if (total_tp + total_fp) > 0 else 0
        recall = total_tp / (total_tp + total_fn) if (total_tp + total_fn) > 0 else 0
        f1 = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0
        
        return {
            'img_id': img_id,
            'mode': 'instance_aware',
            'precision': precision,
            'recall': recall,
            'f1_score': f1,
            'tp': int(total_tp),
            'fp': int(total_fp),
            'fn': int(total_fn),
            'n_pred': len(pred_masks),
            'n_gt': len(gt_masks),
            'n_matches': len(matches),
            'match_rate': len(matches) / len(gt_masks) if len(gt_masks) > 0 else 0,
            'class_stats': dict(class_stats)
        }
    
    def _compute_weighted_pixel_metrics(self, pred_masks, pred_labels, pred_scores,
                                       gt_masks, gt_labels, img_id):
        """基于IoU质量加权的像素级评价"""
        # 过滤和排序
        valid_inds = pred_scores >= self.score_threshold
        pred_masks = pred_masks[valid_inds]
        pred_labels = pred_labels[valid_inds]
        pred_scores = pred_scores[valid_inds]
        
        if len(pred_scores) > self.nms_pre:
            top_inds = np.argsort(pred_scores)[-self.nms_pre:]
            pred_masks = pred_masks[top_inds]
            pred_labels = pred_labels[top_inds]
            pred_scores = pred_scores[top_inds]
        
        total_tp, total_fp, total_fn = 0.0, 0.0, 0.0
        class_stats = defaultdict(lambda: {'tp': 0.0, 'fp': 0.0, 'fn': 0.0})
        
        # 为每个预测找到最佳匹配的真值
        used_gt = set()
        for pred_idx, pred_mask in enumerate(pred_masks):
            pred_label = pred_labels[pred_idx]
            best_iou = 0
            best_gt_idx = -1
            
            for gt_idx, gt_mask in enumerate(gt_masks):
                if gt_idx in used_gt or gt_labels[gt_idx] != pred_label:
                    continue
                    
                intersection = np.sum(pred_mask & gt_mask)
                union = np.sum(pred_mask | gt_mask)
                iou = intersection / union if union > 0 else 0
                
                if iou > best_iou:
                    best_iou = iou
                    best_gt_idx = gt_idx
            
            if best_iou > 0 and best_gt_idx >= 0:
                # 有匹配的真值，根据IoU加权
                used_gt.add(best_gt_idx)
                gt_mask = gt_masks[best_gt_idx]
                
                tp = np.sum(pred_mask & gt_mask)
                fp = np.sum(pred_mask & ~gt_mask)
                fn = np.sum(~pred_mask & gt_mask)
                
                # IoU加权
                weight = best_iou
                total_tp += tp * weight
                total_fp += fp * weight
                total_fn += fn * weight
                
                class_stats[pred_label]['tp'] += tp * weight
                class_stats[pred_label]['fp'] += fp * weight
                class_stats[pred_label]['fn'] += fn * weight
            else:
                # 没有匹配的真值
                fp = np.sum(pred_mask)
                total_fp += fp
                class_stats[pred_label]['fp'] += fp
        
        # 处理未匹配的真值
        for gt_idx, gt_mask in enumerate(gt_masks):
            if gt_idx not in used_gt:
                gt_label = gt_labels[gt_idx]
                fn = np.sum(gt_mask)
                total_fn += fn
                class_stats[gt_label]['fn'] += fn
        
        precision = total_tp / (total_tp + total_fp) if (total_tp + total_fp) > 0 else 0
        recall = total_tp / (total_tp + total_fn) if (total_tp + total_fn) > 0 else 0
        f1 = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0
        
        return {
            'img_id': img_id,
            'mode': 'weighted_pixel',
            'precision': precision,
            'recall': recall,
            'f1_score': f1,
            'tp': float(total_tp),
            'fp': float(total_fp),
            'fn': float(total_fn),
            'n_pred': len(pred_masks),
            'n_gt': len(gt_masks),
            'class_stats': dict(class_stats)
        }
    
    def _match_instances(self, pred_masks, pred_labels, gt_masks, gt_labels):
        """实例匹配（复用原逻辑）"""
        n_pred = len(pred_masks)
        n_gt = len(gt_masks)
        
        if n_pred == 0 or n_gt == 0:
            return [], list(range(n_pred)), list(range(n_gt))
        
        # 计算IoU矩阵
        iou_matrix = np.zeros((n_pred, n_gt))
        for i in range(n_pred):
            for j in range(n_gt):
                if pred_labels[i] == gt_labels[j]:
                    intersection = np.sum(pred_masks[i] & gt_masks[j])
                    union = np.sum(pred_masks[i] | gt_masks[j])
                    iou_matrix[i, j] = intersection / union if union > 0 else 0
        
        # 贪心匹配
        matches = []
        matched_preds = set()
        matched_gts = set()
        
        iou_flat = iou_matrix.flatten()
        sorted_indices = np.argsort(iou_flat)[::-1]
        
        for idx in sorted_indices:
            pred_idx = idx // n_gt
            gt_idx = idx % n_gt
            
            if iou_matrix[pred_idx, gt_idx] < self.iou_threshold:
                break
                
            if pred_idx in matched_preds or gt_idx in matched_gts:
                continue
                
            matches.append((pred_idx, gt_idx))
            matched_preds.add(pred_idx)
            matched_gts.add(gt_idx)
            
            if len(matched_preds) == n_pred or len(matched_gts) == n_gt:
                break
        
        unmatched_preds = [i for i in range(n_pred) if i not in matched_preds]
        unmatched_gts = [j for j in range(n_gt) if j not in matched_gts]
        
        return matches, unmatched_preds, unmatched_gts

    def compute_metrics(self, results: list) -> Dict[str, float]:
        """计算最终的评价指标"""
        if self.format_only:
            self._save_results(results)
            return {}

        # 累积统计
        total_tp = sum(r['tp'] for r in results)
        total_fp = sum(r['fp'] for r in results)
        total_fn = sum(r['fn'] for r in results)

        # 整体指标
        overall_precision = total_tp / (total_tp + total_fp) if (total_tp + total_fp) > 0 else 0
        overall_recall = total_tp / (total_tp + total_fn) if (total_tp + total_fn) > 0 else 0
        overall_f1 = 2 * overall_precision * overall_recall / (overall_precision + overall_recall) \
                     if (overall_precision + overall_recall) > 0 else 0

        # Per-image统计
        precisions = [r['precision'] for r in results]
        recalls = [r['recall'] for r in results]
        f1_scores = [r['f1_score'] for r in results]

        # 分类别指标
        class_metrics = self._compute_class_metrics(results)

        # 构建指标字典
        metrics = {
            f'{self.evaluation_mode}_precision': overall_precision,
            f'{self.evaluation_mode}_recall': overall_recall,
            f'{self.evaluation_mode}_f1_score': overall_f1,
            f'{self.evaluation_mode}_precision_mean': np.mean(precisions),
            f'{self.evaluation_mode}_recall_mean': np.mean(recalls),
            f'{self.evaluation_mode}_f1_mean': np.mean(f1_scores),
            f'{self.evaluation_mode}_num_images': len(results),
        }

        # 添加模式特定的指标
        if self.evaluation_mode == 'instance_aware':
            match_rates = [r.get('match_rate', 0) for r in results]
            metrics[f'{self.evaluation_mode}_avg_match_rate'] = np.mean(match_rates)
            metrics[f'{self.evaluation_mode}_total_matches'] = sum(r.get('n_matches', 0) for r in results)

        # 添加分类别指标
        for class_name, class_metric in class_metrics.items():
            metrics[f'{self.evaluation_mode}_{class_name}_precision'] = class_metric['precision']
            metrics[f'{self.evaluation_mode}_{class_name}_recall'] = class_metric['recall']
            metrics[f'{self.evaluation_mode}_{class_name}_f1'] = class_metric['f1']

        # 为二分类添加平均指标
        if self.binary_classification and len(class_metrics) >= 2:
            avg_precision = np.mean([m['precision'] for m in class_metrics.values()])
            avg_recall = np.mean([m['recall'] for m in class_metrics.values()])
            avg_f1 = np.mean([m['f1'] for m in class_metrics.values()])
            metrics[f'{self.evaluation_mode}_avg_precision'] = avg_precision
            metrics[f'{self.evaluation_mode}_avg_recall'] = avg_recall
            metrics[f'{self.evaluation_mode}_avg_f1'] = avg_f1

        # 打印结果
        self._print_results(metrics, class_metrics)

        # 保存详细结果
        if self.output_dir:
            self._save_detailed_results(metrics, class_metrics, results)

        return metrics

    def _compute_class_metrics(self, results: list) -> dict:
        """计算分类别指标"""
        class_stats = defaultdict(lambda: {'tp': 0, 'fp': 0, 'fn': 0})

        for result in results:
            for class_id, stats in result['class_stats'].items():
                class_stats[class_id]['tp'] += stats['tp']
                class_stats[class_id]['fp'] += stats['fp']
                class_stats[class_id]['fn'] += stats['fn']

        class_metrics = {}
        for class_id, stats in class_stats.items():
            tp = stats['tp']
            fp = stats['fp']
            fn = stats['fn']

            precision = tp / (tp + fp) if (tp + fp) > 0 else 0
            recall = tp / (tp + fn) if (tp + fn) > 0 else 0
            f1 = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0

            # 处理二分类的类别名称
            if self.binary_classification:
                if class_id == 'room':
                    class_name = 'room'
                elif class_id == 'background':
                    class_name = 'background'
                else:
                    class_name = str(class_id)
            else:
                class_name = self.class_names[class_id] if isinstance(class_id, int) and class_id < len(self.class_names) else f'class_{class_id}'

            class_metrics[class_name] = {
                'precision': precision,
                'recall': recall,
                'f1': f1,
                'support': tp + fn
            }

        return class_metrics

    def _print_results(self, metrics: dict, class_metrics: dict):
        """打印格式化结果"""
        mode_name = self.evaluation_mode.replace('_', ' ').title()
        print(f"\n{'='*80}")
        print(f"{mode_name} PIXEL-LEVEL EVALUATION RESULTS")
        print(f"{'='*80}")

        # 整体指标
        if self.binary_classification:
            print(f"\n[Overall Metrics - {mode_name} (Room Class)]")
            print(f"  Precision: {metrics[f'{self.evaluation_mode}_precision']:.4f}")
            print(f"  Recall:    {metrics[f'{self.evaluation_mode}_recall']:.4f}")
            print(f"  F1-Score:  {metrics[f'{self.evaluation_mode}_f1_score']:.4f}")

            # 计算并显示平均指标
            if class_metrics and len(class_metrics) >= 2:
                avg_precision = np.mean([m['precision'] for m in class_metrics.values()])
                avg_recall = np.mean([m['recall'] for m in class_metrics.values()])
                avg_f1 = np.mean([m['f1'] for m in class_metrics.values()])
                print(f"\n[Average Metrics - Both Classes]")
                print(f"  Average Precision: {avg_precision:.4f}")
                print(f"  Average Recall:    {avg_recall:.4f}")
                print(f"  Average F1-Score:  {avg_f1:.4f}")
        else:
            print(f"\n[Overall Metrics - {mode_name}]")
            print(f"  Precision: {metrics[f'{self.evaluation_mode}_precision']:.4f}")
            print(f"  Recall:    {metrics[f'{self.evaluation_mode}_recall']:.4f}")
            print(f"  F1-Score:  {metrics[f'{self.evaluation_mode}_f1_score']:.4f}")

        # 模式特定信息
        if self.evaluation_mode == 'instance_aware':
            print(f"\n[Instance Matching Statistics]")
            print(f"  Average Match Rate: {metrics.get(f'{self.evaluation_mode}_avg_match_rate', 0):.4f}")
            print(f"  Total Matches: {metrics.get(f'{self.evaluation_mode}_total_matches', 0)}")

        # 分类别指标
        if class_metrics:
            print(f"\n[Per-Class Metrics - {mode_name}]")
            print(f"{'Class':<20} {'Precision':<10} {'Recall':<10} {'F1-Score':<10} {'Support':<12}")
            print("-" * 72)

            for class_name, class_metric in sorted(class_metrics.items()):
                if class_metric['support'] > 0:
                    print(f"{class_name:<20} "
                          f"{class_metric['precision']:<10.4f} "
                          f"{class_metric['recall']:<10.4f} "
                          f"{class_metric['f1']:<10.4f} "
                          f"{class_metric['support']:<12.0f}")

        print(f"{'='*80}\n")

    def _save_detailed_results(self, metrics: dict, class_metrics: dict, results: list):
        """保存详细结果"""
        os.makedirs(self.output_dir, exist_ok=True)

        # 保存汇总结果
        summary = {
            'evaluation_mode': self.evaluation_mode,
            'overall_metrics': metrics,
            'class_metrics': class_metrics,
            'config': {
                'iou_threshold': self.iou_threshold,
                'score_threshold': self.score_threshold,
                'num_classes': self.num_classes,
                'class_names': self.class_names
            }
        }

        summary_path = os.path.join(self.output_dir, f'{self.evaluation_mode}_metrics_summary.json')
        with open(summary_path, 'w') as f:
            json.dump(summary, f, indent=2)

        # 保存详细结果
        detailed_path = os.path.join(self.output_dir, f'{self.evaluation_mode}_metrics_detailed.json')
        with open(detailed_path, 'w') as f:
            json.dump(results, f, indent=2)

        self.logger.info(f"Results saved to {self.output_dir}")
