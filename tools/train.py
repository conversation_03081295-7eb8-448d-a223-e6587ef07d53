import os
import argparse
import sys

# Ensure project root directory is importable so that custom modules defined in configs work
ROOT_DIR = os.path.abspath(os.path.join(os.path.dirname(__file__), os.pardir))
if ROOT_DIR not in sys.path:
    sys.path.insert(0, ROOT_DIR)

# import mmdet  # 关键导入 - 激活 MMDetection 的注册机制
# from mmdet.engine import DetLocalVisualizer  # 显式导入可视化器
# from mmdet.registry import VISUALIZERS  # 导入注册表

# # 验证可视化器是否已注册
# if 'DetLocalVisualizer' not in VISUALIZERS:
#     VISUALIZERS.register_module(module=DetLocalVisualizer)
    
# print(f"可视化器已注册: {'DetLocalVisualizer' in VISUALIZERS}")
# # ---------------------------------------------------


from mmengine.config import Config
from mmengine.runner import Runner

def parse_args():
    parser = argparse.ArgumentParser(description='Train Mask2Former with Swin Transformer')
    parser.add_argument('--config', default='../configs/mask2former_swin.py', help='Config file path')
    parser.add_argument('--work-dir', help='The directory to save logs and models')
    parser.add_argument('--resume', action='store_true', help='Resume from the latest checkpoint')
    args = parser.parse_args()
    return args

def main():
    args = parse_args()
    
    # 加载配置
    cfg = Config.fromfile(args.config)
    
    # 设置工作目录
    if args.work_dir is not None:
        cfg.work_dir = args.work_dir
    
    # 创建工作目录
    os.makedirs(cfg.work_dir, exist_ok=True)
    
    # 配置保存
    cfg.dump(os.path.join(cfg.work_dir, os.path.basename(args.config)))
    
    # 初始化Runner并训练模型
    runner = Runner.from_cfg(cfg)
    runner.train()

if __name__ == '__main__':
    main()