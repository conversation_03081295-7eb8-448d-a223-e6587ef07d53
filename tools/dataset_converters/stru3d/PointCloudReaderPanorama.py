import cv2
import open3d as o3d
import os
from sklearn.preprocessing import normalize
import numpy as np
import sys

# 设置matplotlib后端避免显示问题
import matplotlib
matplotlib.use('Agg')
import matplotlib.pyplot as plt

# 添加项目根目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', '..'))
from configs.mask2former_config import set_img_size

NUM_SECTIONS = -1

class PointCloudReaderPanorama():

    def __init__(self, path, resolution="full", random_level=0, generate_color=False, generate_normal=False,
                 remove_duplicates=True, downsample_ratio=1.0, use_vectorized=True):
        self.path = path
        self.random_level = random_level
        self.resolution = resolution
        self.generate_color = generate_color
        self.generate_normal = generate_normal
        self.remove_duplicates = remove_duplicates
        self.downsample_ratio = downsample_ratio  # 降采样比例：1.0=不降采样，0.5=保留50%，0.1=保留10%
        self.use_vectorized = use_vectorized  # 是否使用向量化计算
        # 获取所有房间目录（只要数字目录）
        rendering_dir = os.path.join(path, "2D_rendering")
        sections = [p for p in os.listdir(rendering_dir)
                   if os.path.isdir(os.path.join(rendering_dir, p)) and p.isdigit()]
        sections = sorted(sections)  # 确保顺序一致

        print(f"  找到 {len(sections)} 个房间: {sections}")

        # 正确的路径构建逻辑 - 使用resolution子目录（通常是"full"）
        self.depth_paths = [os.path.join(path, "2D_rendering", p, "panorama", self.resolution, "depth.png") for p in sections]
        self.rgb_paths = [os.path.join(path, "2D_rendering", p, "panorama", self.resolution, "rgb_coldlight.png") for p in sections]
        self.normal_paths = [os.path.join(path, "2D_rendering", p, "panorama", self.resolution, "normal.png") for p in sections]
        self.camera_paths = [os.path.join(path, "2D_rendering", p, "panorama", "camera_xyz.txt") for p in sections]

        # 验证关键文件是否存在
        missing_files = []
        for i, (depth_path, rgb_path) in enumerate(zip(self.depth_paths, self.rgb_paths)):
            if not os.path.exists(depth_path):
                missing_files.append(f"房间{sections[i]}: depth.png")
            if not os.path.exists(rgb_path):
                missing_files.append(f"房间{sections[i]}: rgb_coldlight.png")

        if missing_files:
            print(f"  ⚠️  缺少文件: {missing_files}")
        else:
            print(f"  ✅ 所有必要文件都存在")
        self.camera_centers = self.read_camera_center()
        self.point_cloud = self.generate_point_cloud(self.random_level, color=self.generate_color, normal=self.generate_normal)

    def read_camera_center(self):
        camera_centers = []
        for i in range(len(self.camera_paths)):
            try:
                with open(self.camera_paths[i], 'r') as f:
                    line = f.readline()
                center = list(map(float, line.strip().split(" ")))
                camera_centers.append(np.asarray([center[0], center[1], center[2]]))
                print(f"  读取相机中心: {self.camera_paths[i]} -> {center}")
            except (FileNotFoundError, IOError, ValueError) as e:
                # 文件不存在或读取失败时使用默认相机中心 [0, 0, 0]
                default_center = np.asarray([0.0, 0.0, 0.0])
                camera_centers.append(default_center)
                print(f"  ⚠️  相机文件缺失，使用默认中心 [0,0,0]: {self.camera_paths[i]}")
                print(f"     错误详情: {e}")
        return camera_centers

    def generate_point_cloud(self, random_level=0, color=False, normal=False):
        if self.use_vectorized:
            return self.generate_point_cloud_vectorized(random_level)
        else:
            return self.generate_point_cloud_original(random_level)

    def generate_point_cloud_vectorized(self, random_level=0):
        """向量化的点云生成方法，大幅提升性能"""
        print("使用向量化计算生成点云...")
        all_coords = []
        all_colors = []
        points = {}

        for i in range(len(self.depth_paths)):
            # print(f"  处理图像 {i+1}/{len(self.depth_paths)}")

            # 直接读取图像（移除有问题的缓存机制）
            depth_img = cv2.imread(self.depth_paths[i], cv2.IMREAD_ANYDEPTH | cv2.IMREAD_ANYCOLOR)
            rgb_img = cv2.imread(self.rgb_paths[i])
            rgb_img = cv2.cvtColor(rgb_img, code=cv2.COLOR_BGR2RGB)

            # 计算角度步长
            x_tick = 180.0 / depth_img.shape[0]
            y_tick = 360.0 / depth_img.shape[1]

            # 创建坐标网格
            x_indices, y_indices = np.meshgrid(
                np.arange(depth_img.shape[0]),
                np.arange(depth_img.shape[1]),
                indexing='ij'
            )

            # 向量化计算角度
            alpha = 90 - (x_indices * x_tick)
            beta = y_indices * y_tick - 180

            # 转换为浮点数并添加随机噪声
            depth = depth_img.astype(np.float32)
            if random_level > 0:
                depth += np.random.random(depth.shape) * random_level

            # 创建有效深度掩码
            valid_mask = depth > 500.0

            if np.sum(valid_mask) == 0:
                continue

            # 只处理有效像素
            valid_depth = depth[valid_mask]
            valid_alpha = alpha[valid_mask]
            valid_beta = beta[valid_mask]
            valid_rgb = rgb_img[valid_mask]

            # 向量化计算3D坐标
            alpha_rad = np.deg2rad(valid_alpha)
            beta_rad = np.deg2rad(valid_beta)

            z_offset = valid_depth * np.sin(alpha_rad)
            xy_offset = valid_depth * np.cos(alpha_rad)
            x_offset = xy_offset * np.sin(beta_rad)
            y_offset = xy_offset * np.cos(beta_rad)

            # 组合坐标并添加相机中心偏移
            coords = np.column_stack([x_offset, y_offset, z_offset])
            coords += self.camera_centers[i]

            all_coords.append(coords)
            all_colors.append(valid_rgb)

            # print(f"    生成 {len(coords)} 个点")

        # 合并所有点云
        if all_coords:
            coords = np.vstack(all_coords)
            colors = np.vstack(all_colors) / 255.0
        else:
            coords = np.array([])
            colors = np.array([])

        points['coords'] = coords
        points['colors'] = colors

        # print(f"向量化计算完成，总点数: {len(coords)}")
        return points

    def generate_point_cloud_original(self, random_level=0):
        """原始的点云生成方法（保留用于对比）"""
        print("使用原始方法生成点云...")
        coords = []
        colors = []
        points = {}

        # Getting Coordinates
        for i in range(len(self.depth_paths)):
            depth_img = cv2.imread(self.depth_paths[i], cv2.IMREAD_ANYDEPTH | cv2.IMREAD_ANYCOLOR)
            x_tick = 180.0/depth_img.shape[0]
            y_tick = 360.0/depth_img.shape[1]

            rgb_img = cv2.imread(self.rgb_paths[i])
            rgb_img = cv2.cvtColor(rgb_img, code=cv2.COLOR_BGR2RGB)

            for x in range(0, depth_img.shape[0]):
                for y in range(0, depth_img.shape[1]):
                    # need 90 - -09
                    alpha = 90 - (x * x_tick)
                    beta = y * y_tick -180

                    # 读取深度值并转换为浮点数以提高精度
                    depth = float(depth_img[x,y]) + np.random.random()*random_level

                    if depth > 500.:
                        z_offset = depth*np.sin(np.deg2rad(alpha))
                        xy_offset = depth*np.cos(np.deg2rad(alpha))
                        x_offset = xy_offset * np.sin(np.deg2rad(beta))
                        y_offset = xy_offset * np.cos(np.deg2rad(beta))
                        point = np.asarray([x_offset, y_offset, z_offset])
                        coords.append(point + self.camera_centers[i])
                        colors.append(rgb_img[x, y])

        coords = np.asarray(coords)
        colors = np.asarray(colors) / 255.0
        # normals = np.asarray(normals)

        # # 对Z坐标的点云进行量化
        # coords[:,:2] = np.round(coords[:,:2] / 10) * 10.
        # coords[:,2] = np.round(coords[:,2] / 100) * 100.

        # 选择方案：
        # 方案1：完全移除量化（保持原始精度）
        # 方案2：使用更小的量化步长（减少精度损失）
        # coords[:,:2] = np.round(coords[:,:2] / 1) * 1.    # XY量化到1单位
        # coords[:,2] = np.round(coords[:,2] / 5) * 5.      # Z量化到5单位

        # 当前使用方案1：完全移除量化



        # 可选的去重操作
        if self.remove_duplicates:
            print("正在移除重复坐标...")
            _, unique_ind = np.unique(coords, return_index=True, axis=0)
            original_count = len(coords)
            coords = coords[unique_ind]
            colors = colors[unique_ind]
            print(f"去重后点云数量: {len(coords)} (移除了 {original_count - len(coords)} 个重复点)")
        else:
            print("保留所有点云数据（包括重复坐标）")

        # 随机降采样处理
        if self.downsample_ratio < 1.0:
            print(f"正在进行随机降采样 (保留比例: {self.downsample_ratio:.2f})...")
            coords, colors = self.random_downsample(coords, colors, self.downsample_ratio)
            print(f"降采样后点云数量: {len(coords)}")

        points['coords'] = coords
        points['colors'] = colors
        # points['normals'] = normals

        # if color:
        #     # Getting RGB color
        #     for i in range(len(self.rgb_paths)):
        #         rgb_img = cv2.imread(self.rgb_paths[i])
        #         rgb_img = cv2.cvtColor(rgb_img, code=cv2.COLOR_BGR2RGB)
        #         for x in range(0, rgb_img.shape[0], 2):
        #             for y in range(0, rgb_img.shape[1], 2):
        #                 colors.append(rgb_img[x, y])
        #     points['colors'] = np.asarray(colors)/255.0
        # if normal:
        #     # Getting Normal
        #     for i in range(len(self.normal_paths)):
        #         normal_img = cv2.imread(self.normal_paths[i])
        #         for x in range(0, normal_img.shape[0], 2):
        #             for y in range(0, normal_img.shape[1], 2):
        #                 normals.append(normalize(normal_img[x, y].reshape(-1, 1)).ravel())
        #     points['normals'] = normals

        print("Pointcloud size:", points['coords'].shape[0])
        return points









    def random_downsample(self, coords, colors, downsample_ratio):
        """随机降采样点云

        Args:
            coords: 点云坐标数组 (N, 3)
            colors: 点云颜色数组 (N, 3)
            downsample_ratio: 保留比例，范围 [0, 1]
                            1.0 = 不降采样
                            0.5 = 保留50%的点
                            0.1 = 保留10%的点

        Returns:
            downsampled_coords, downsampled_colors: 降采样后的坐标和颜色
        """
        if downsample_ratio >= 1.0:
            return coords, colors

        if downsample_ratio <= 0.0:
            print("警告: 降采样比例为0，返回空点云")
            return np.array([]), np.array([])

        coords = np.array(coords)
        colors = np.array(colors)

        if len(coords) == 0:
            return coords, colors

        # 计算需要保留的点数
        original_count = len(coords)
        target_count = int(original_count * downsample_ratio)
        target_count = max(1, target_count)  # 至少保留1个点

        print(f"  原始点数: {original_count}")
        print(f"  目标点数: {target_count}")
        print(f"  降采样比例: {downsample_ratio:.3f}")

        # 随机选择索引
        np.random.seed(42)  # 设置随机种子以确保可重复性 # 42是一个常用的任意数字（在《银河系漫游指南》中被称为"生命、宇宙以及一切的终极答案"而广为人知）
        indices = np.random.choice(original_count, size=target_count, replace=False)
        indices = np.sort(indices)  # 排序以保持某种顺序

        # 应用降采样
        downsampled_coords = coords[indices]
        downsampled_colors = colors[indices]

        print(f"  实际保留点数: {len(downsampled_coords)}")

        return downsampled_coords, downsampled_colors

    def get_point_cloud(self):
        return self.point_cloud

    def generate_density(self, width=set_img_size, height=set_img_size):

        ps = self.point_cloud["coords"] * -1
        ps[:,0] *= -1
        ps[:,1] *= -1

        pcd = o3d.geometry.PointCloud()
        pcd.points = o3d.utility.Vector3dVector(ps)
        pcd.estimate_normals()

        # zs = np.round(ps[:,2] / 100) * 100
        # zs, zs_ind = np.unique(zs, return_index=True, axis=0)
        # ps_ind = ps[:, :2] ==
        # print("Generate density...")

        image_res = np.array((width, height))

        max_coords = np.max(ps, axis=0)
        min_coords = np.min(ps, axis=0)
        max_m_min = max_coords - min_coords

        max_coords = max_coords + 0.1 * max_m_min
        min_coords = min_coords - 0.1 * max_m_min

        normalization_dict = {}
        normalization_dict["min_coords"] = min_coords
        normalization_dict["max_coords"] = max_coords
        normalization_dict["image_res"] = image_res


        # coordinates = np.round(points[:, :2] / max_coordinates[None,:2] * image_res[None])
        coordinates = \
            np.round(
                (ps[:, :2] - min_coords[None, :2]) / (max_coords[None,:2] - min_coords[None, :2]) * image_res[None])
        coordinates = np.minimum(np.maximum(coordinates, np.zeros_like(image_res)),
                                    image_res - 1)

        density = np.zeros((height, width), dtype=np.float32)

        unique_coordinates, counts = np.unique(coordinates, return_counts=True, axis=0)
        # print(np.unique(counts))
        # counts = np.minimum(counts, 1e2)

        unique_coordinates = unique_coordinates.astype(np.int32)

        density[unique_coordinates[:, 1], unique_coordinates[:, 0]] = counts
        density = density / np.max(density)
        # print(np.unique(density))

        normals = np.array(pcd.normals)
        normals_map = np.zeros((density.shape[0], density.shape[1], 3))

        import time
        start_time = time.time()
        for i, unique_coord in enumerate(unique_coordinates):
            # print(normals[unique_ind])
            normals_indcs = np.argwhere(np.all(coordinates[::10] == unique_coord, axis=1))[:,0]
            normals_map[unique_coordinates[i, 1], unique_coordinates[i, 0], :] = np.mean(normals[::10][normals_indcs, :], axis=0)

        print("Time for normals: ", time.time() - start_time)

        normals_map = (np.clip(normals_map,0,1) * 255).astype(np.uint8)

        # plt.figure()
        # plt.imshow(normals_map)
        # plt.show()

        return density, normals_map, normalization_dict

    def visualize(self, export_path=None):
        pcd = o3d.geometry.PointCloud()

        points = self.point_cloud['coords']

        print(np.max(points, axis=0))
        indices = np.where(points[:, 2] < 2000)

        points = points[indices]
        points[:,1] *= -1
        points[:,:] /= 1000
        pcd.points = o3d.utility.Vector3dVector(points)

        if self.generate_normal:
            normals = self.point_cloud['normals']
            normals = normals[indices]
            pcd.normals = o3d.utility.Vector3dVector(normals)
        if self.generate_color:
            colors = self.point_cloud['colors']
            colors = colors[indices]
            pcd.colors = o3d.utility.Vector3dVector(colors)


        # 移除硬编码的annotation_3d.json路径
        # 这个文件对点云生成不是必需的

        # wireframe_geo_list = visualize_wireframe(annos, vis=False, ret=True)
        # o3d.visualization.draw_geometries([pcd] + wireframe_geo_list)
        # o3d.visualization.draw_geometries([pcd])

        pcd.estimate_normals()

        # radii = 0.01
        # mesh = o3d.geometry.TriangleMesh.create_from_point_cloud_ball_pivoting(pcd, radii)

        # alpha = 0.1
        # tetra_mesh, pt_map = o3d.geometry.TetraMesh.create_from_point_cloud(pcd)
        # mesh = o3d.geometry.TriangleMesh.create_from_point_cloud_alpha_shape(pcd, alpha, tetra_mesh, pt_map)

        o3d.visualization.draw_geometries([pcd])

        if export_path is not None:

            # 使用二进制格式写入PLY文件（更快，标准格式）
            o3d.io.write_point_cloud(export_path, pcd, write_ascii=False)

        # o3d.visualization.draw_geometries([pcd])

    def export_ply(self, path):
        '''
        ply
        format ascii 1.0
        comment Mars model by Paul Bourke
        element vertex 259200
        property float x
        property float y
        property float z
        property uchar r
        property uchar g
        property uchar b
        property float nx
        property float ny
        property float nz
        end_header
        '''
        with open(path, "w") as f:
            f.write("ply\n")
            f.write("format ascii 1.0\n")
            f.write("element vertex %d\n" % self.point_cloud['coords'].shape[0])
            f.write("property float x\n")
            f.write("property float y\n")
            f.write("property float z\n")
            if self.generate_color:
                f.write("property uchar red\n")
                f.write("property uchar green\n")
                f.write("property uchar blue\n")
            if self.generate_normal:
                f.write("property float nx\n")
                f.write("property float ny\n")
                f.write("property float nz\n")
            f.write("end_header\n")
            for i in range(self.point_cloud['coords'].shape[0]):
                normal = []
                color = []
                coord = self.point_cloud['coords'][i].tolist()
                if self.generate_color:
                    color = list(map(int, (self.point_cloud['colors'][i]*255).tolist()))
                if self.generate_normal:
                    normal = self.point_cloud['normals'][i].tolist()
                data = coord + color + normal
                f.write(" ".join(list(map(str,data)))+'\n')
