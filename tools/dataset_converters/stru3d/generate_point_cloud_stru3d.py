import argparse
import os
import sys
import time
import logging
from datetime import datetime
from tqdm import tqdm
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading
from PointCloudReaderPanorama import PointCloudReaderPanorama


def parse_failed_scenes_file(failed_scenes_file):
    """解析失败场景列表文件"""
    failed_scenes = []

    if not os.path.exists(failed_scenes_file):
        print(f"❌ 失败场景文件不存在: {failed_scenes_file}")
        return failed_scenes

    try:
        with open(failed_scenes_file, 'r', encoding='utf-8') as f:
            content = f.read()

        # 解析格式: "- Structured3D_panorama_XX/scene_XXXXX - error message"
        import re
        pattern = r'- (Structured3D_panorama_\d+)/(scene_\d+) -'
        matches = re.findall(pattern, content)

        for part, scene in matches:
            failed_scenes.append((part, scene))

        print(f"📋 从 {failed_scenes_file} 解析到 {len(failed_scenes)} 个失败场景")
        for part, scene in failed_scenes:
            print(f"   - {part}/{scene}")

        return failed_scenes

    except Exception as e:
        print(f"❌ 解析失败场景文件时出错: {e}")
        return failed_scenes

def setup_logging():
    """设置日志系统，同时输出到终端和文件"""
    # 创建日志目录（使用相对路径避免权限问题）
    log_dir = "../../../output/0_depth_map_display_stru3d"
    os.makedirs(log_dir, exist_ok=True)

    # 日志文件路径 - 使用strftime获取当前时间
    current_time = datetime.now().strftime("%m%d%H%M")
    log_file = os.path.join(log_dir, f"log_{current_time}.txt")
    
    # 创建logger
    logger = logging.getLogger('PointCloudGenerator')
    logger.setLevel(logging.INFO)

    # 清除已有的handlers
    logger.handlers.clear()

    # 创建formatter
    formatter = logging.Formatter(
        '%(asctime)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )

    # 文件handler
    file_handler = logging.FileHandler(log_file, mode='w', encoding='utf-8')
    file_handler.setLevel(logging.INFO)
    file_handler.setFormatter(formatter)
    logger.addHandler(file_handler)

    # 控制台handler
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)

    return logger, log_file

def process_single_scene(scene_info_tuple):
    """处理单个场景的函数，用于多线程处理"""
    scene_data, args = scene_info_tuple
    part, scene, scene_path, save_path, scene_id, total_scenes = scene_data

    result = {
        'scene_id': scene_id,
        'part': part,
        'scene': scene,
        'scene_path': scene_path,
        'save_path': save_path,
        'success': False,
        'error': None,
        'file_size': 0,
        'processing_time': 0
    }

    start_time = time.time()

    try:
        # 检查场景路径是否存在
        if not os.path.exists(scene_path):
            result['error'] = "路径不存在"
            return result

        # 只检查最基本的2D_rendering目录（annotation_3d.json不是必需的）
        rendering_path = os.path.join(scene_path, "2D_rendering")
        if not os.path.exists(rendering_path):
            result['error'] = "缺少2D_rendering目录"
            return result

        # 创建点云读取器
        reader = PointCloudReaderPanorama(
            scene_path,
            random_level=0.5,
            generate_color=True,
            generate_normal=False,
            remove_duplicates=True,
            downsample_ratio=args.downsample_ratio,
            use_vectorized=args.use_vectorized
        )

        # 导出PLY文件
        reader.export_ply(save_path)

        # 检查输出文件
        if os.path.exists(save_path):
            result['file_size'] = os.path.getsize(save_path)
            result['success'] = True
        else:
            result['error'] = "输出文件未生成"

    except Exception as e:
        result['error'] = str(e)

    result['processing_time'] = time.time() - start_time
    return result

def config():
    a = argparse.ArgumentParser(description='Generate point cloud for Structured3D')
    a.add_argument('--data_root', default='/home/<USER>/05_DL_dataset_wuhan/03_开源数据集/structure_3D/Panorama/', type=str, help='path to raw Structured3D_panorama folder')
    a.add_argument('--max_scenes', default=None, type=int, help='Maximum number of scenes to process (for testing)')
    a.add_argument('--downsample_ratio', default=1.0, type=float, help='Random downsampling ratio (1.0=no downsampling, 0.5=keep 50%, 0.1=keep 10%)')
    a.add_argument('--num_workers', default=64, type=int, help='Number of worker threads for parallel processing')
    a.add_argument('--use_vectorized', default=True, type=bool, help='Use vectorized computation for faster processing')
    a.add_argument('--gpu_id', default=1, type=int, help='GPU ID to use (if GPU acceleration is enabled)')
    a.add_argument('--skip_invalid', action='store_true', help='Skip scenes with missing files instead of failing')
    a.add_argument('--retry_failed_scenes', action='store_true', help='Retry only the scenes that failed in previous run')
    a.add_argument('--failed_scenes_file', default='/home/<USER>/repos/Mask2Former_v2/output/0_depth_map_display_stru3d/log_error_list.txt',
                   help='Path to the failed scenes list file (default: /home/<USER>/repos/Mask2Former_v2/output/0_depth_map_display_stru3d/log_error_list.txt)')

    args = a.parse_args()
    return args

def main(args):
    # 设置日志
    logger, log_file = setup_logging()

    start_time = time.time()
    logger.info("=" * 80)
    logger.info("🚀 开始生成Structured3D点云数据")
    logger.info(f"🕐 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    logger.info("=" * 80)
    logger.info(f"📁 数据根目录: {args.data_root}")
    logger.info(f"📝 日志文件: {log_file}")
    logger.info(f"🎛️  降采样比例: {args.downsample_ratio}")
    logger.info(f"🧵 工作线程数: {args.num_workers}")
    logger.info(f"⚡ 向量化计算: {'启用' if args.use_vectorized else '禁用'}")
    if args.retry_failed_scenes:
        logger.info(f"🔄 重试模式: 启用")
        logger.info(f"📄 失败场景文件: {args.failed_scenes_file}")
    elif args.max_scenes:
        logger.info(f"🔢 限制场景数量: {args.max_scenes}")

    # 检查数据根目录
    if not os.path.exists(args.data_root):
        logger.error(f"❌ 数据根目录不存在: {args.data_root}")
        return

    data_root = args.data_root

    try:
        data_parts = sorted(os.listdir(data_root))  # 按文件名排序
        logger.info(f"📦 找到 {len(data_parts)} 个数据部分: {data_parts}")
    except Exception as e:
        logger.error(f"❌ 无法读取数据根目录: {e}")
        return

    # 处理重试模式或正常模式
    if args.retry_failed_scenes:
        # 重试模式：只处理失败的场景
        failed_scenes = parse_failed_scenes_file(args.failed_scenes_file)
        if not failed_scenes:
            logger.error("❌ 未找到需要重试的失败场景")
            return

        # 构建重试场景信息
        scene_info = []
        retry_scene_dict = {}

        # 按数据部分组织失败场景
        for part, scene in failed_scenes:
            if part not in retry_scene_dict:
                retry_scene_dict[part] = []
            retry_scene_dict[part].append(scene)

        # 验证失败场景是否存在
        for part, scenes in retry_scene_dict.items():
            part_path = os.path.join(data_root, part, 'Structured3D')
            if os.path.exists(part_path):
                existing_scenes = []
                for scene in scenes:
                    scene_path = os.path.join(part_path, scene)
                    if os.path.exists(scene_path):
                        existing_scenes.append(scene)
                    else:
                        logger.warning(f"⚠️  重试场景不存在: {part}/{scene}")

                if existing_scenes:
                    scene_info.append((part, sorted(existing_scenes)))
                    logger.info(f"  🔄 {part}: {len(existing_scenes)} 个重试场景")
            else:
                logger.warning(f"⚠️  重试数据部分路径不存在: {part_path}")

        total_scenes = sum(len(scenes) for _, scenes in scene_info)
        logger.info(f"🎯 重试模式：总共需要处理 {total_scenes} 个失败场景")

    else:
        # 正常模式：处理所有场景
        total_scenes = 0
        scene_info = []

        for part in data_parts:
            part_path = os.path.join(data_root, part, 'Structured3D')
            if os.path.exists(part_path):
                try:
                    scenes = os.listdir(part_path)
                    scenes = [s for s in scenes if os.path.isdir(os.path.join(part_path, s))]
                    scenes = sorted(scenes)  # 按文件名排序场景
                    total_scenes += len(scenes)
                    scene_info.append((part, scenes))
                    logger.info(f"  📂 {part}: {len(scenes)} 个场景 (已排序)")
                except Exception as e:
                    logger.warning(f"⚠️  无法读取部分 {part}: {e}")
            else:
                logger.warning(f"⚠️  路径不存在: {part_path}")

        # 应用场景数量限制（如果指定）
        if args.max_scenes:
            logger.info(f"🔢 限制处理场景数量: {args.max_scenes}")
            total_scenes = min(total_scenes, args.max_scenes)

        logger.info(f"🎯 总共需要处理 {total_scenes} 个场景")

    if total_scenes == 0:
        logger.error("❌ 未找到任何需要处理的场景")
        return
    logger.info("=" * 80)

    # 准备所有场景的处理任务
    all_tasks = []
    task_id = 0

    for part, scenes in scene_info:
        for scene in scenes:
            # 在重试模式下，忽略max_scenes限制
            if not args.retry_failed_scenes and args.max_scenes and task_id >= args.max_scenes:
                break

            task_id += 1
            scene_path = os.path.join(data_root, part, 'Structured3D', scene)
            save_path = os.path.join(scene_path, 'point_cloud.ply')

            scene_data = (part, scene, scene_path, save_path, task_id, total_scenes)
            all_tasks.append((scene_data, args))

        # 在重试模式下，忽略max_scenes限制
        if not args.retry_failed_scenes and args.max_scenes and task_id >= args.max_scenes:
            break

    if args.retry_failed_scenes:
        logger.info(f"🔄 开始重试失败场景的多线程处理 {len(all_tasks)} 个场景...")
    else:
        logger.info(f"� 开始多线程处理 {len(all_tasks)} 个场景...")

    # 记录所有待处理的场景（用于完整性验证）
    expected_scenes = set()
    for scene_data, _ in all_tasks:
        part, scene, _, _, task_id, _ = scene_data
        expected_scenes.add(f"{part}/{scene}")

    logger.info(f"📋 待处理场景列表:")
    for i, (scene_data, _) in enumerate(all_tasks, 1):
        part, scene, _, _, _, _ = scene_data
        logger.info(f"   [{i:03d}] {part}/{scene}")

    # 多线程处理
    success_count = 0
    failed_scenes = []
    completed_count = 0
    processed_scenes = set()  # 用于验证完整性

    with ThreadPoolExecutor(max_workers=args.num_workers) as executor:
        # 提交所有任务
        future_to_task = {executor.submit(process_single_scene, task): task for task in all_tasks}

        # 处理完成的任务
        for future in as_completed(future_to_task):
            completed_count += 1
            result = future.result()

            # 记录已处理的场景
            scene_key = f"{result['part']}/{result['scene']}"
            processed_scenes.add(scene_key)

            logger.info(f"\n🔄 [{completed_count}/{len(all_tasks)}] 完成场景: {result['scene']}")
            logger.info(f"   数据部分: {result['part']}")
            logger.info(f"   📂 场景路径: {result['scene_path']}")
            logger.info(f"   💾 输出路径: {result['save_path']}")

            if result['success']:
                logger.info(f"   ✅ 成功生成点云文件")
                logger.info(f"   📊 文件大小: {result['file_size'] / 1024 / 1024:.2f} MB")
                logger.info(f"   ⏱️  处理时间: {result['processing_time']:.2f} 秒")
                success_count += 1
            else:
                logger.error(f"   ❌ 处理失败: {result['error']}")
                failed_scenes.append(f"{result['part']}/{result['scene']} - {result['error']}")

    processed_count = completed_count

    # 完整性验证
    logger.info(f"\n🔍 完整性验证:")
    logger.info(f"   预期处理场景: {len(expected_scenes)}")
    logger.info(f"   实际处理场景: {len(processed_scenes)}")

    missing_scenes = expected_scenes - processed_scenes
    extra_scenes = processed_scenes - expected_scenes

    if missing_scenes:
        logger.error(f"   ❌ 遗漏场景 ({len(missing_scenes)}个): {sorted(missing_scenes)}")

    if extra_scenes:
        logger.warning(f"   ⚠️  额外场景 ({len(extra_scenes)}个): {sorted(extra_scenes)}")

    if not missing_scenes and not extra_scenes:
        logger.info(f"   ✅ 完整性验证通过：所有场景都已处理")

    # 处理完成，输出总结
    total_duration = time.time() - start_time
    logger.info("\n" + "=" * 80)
    logger.info("🎉 点云生成任务完成！")
    logger.info("=" * 80)
    logger.info(f"📊 处理统计:")
    logger.info(f"   总处理场景: {processed_count}")
    logger.info(f"   成功生成: {success_count}")
    logger.info(f"   失败场景: {len(failed_scenes)}")
    logger.info(f"   成功率: {success_count/processed_count*100:.1f}%")
    logger.info(f"⏱️  总耗时: {total_duration/60:.1f} 分钟")
    logger.info(f"⚡ 平均每场景: {total_duration/processed_count:.1f} 秒")

    if failed_scenes:
        logger.info(f"\n❌ 失败场景列表:")
        for failed_scene in failed_scenes:
            logger.info(f"   - {failed_scene}")

    logger.info(f"\n📝 详细日志已保存到: {log_file}")
    logger.info("=" * 80)
            

if __name__ == "__main__":

    main(config())


# python generate_point_cloud_stru3d.py --num_workers 4 --use_vectorized True --max_scenes 4