import argparse
import json
import os
import sys
import logging
import numpy as np
from datetime import datetime
from tqdm import tqdm
from stru3d_utils import generate_density, normalize_annotations, parse_floor_plan_polys, generate_coco_dict

sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
from common_utils import read_scene_pc, export_density

# 添加项目根目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', '..'))
from configs.mask2former_config import set_img_size

def setup_logging():
    """设置日志记录"""
    # 创建日志目录
    log_dir = "../../../output/0_depth_map_display_stru3d/coco_generation_log/"
    os.makedirs(log_dir, exist_ok=True)

    # 日志文件路径
    current_time = datetime.now().strftime("%m%d%H%M")
    log_file = os.path.join(log_dir, f"coco_generation_{current_time}.log")

    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler()
        ]
    )

    return logging.getLogger(__name__)

### Note: Some scenes have missing/wrong annotations. These are the indices that you should additionally exclude 
### to be consistent with MonteFloor and HEAT:
invalid_scenes_ids = [76, 183, 335, 491, 663, 681, 703, 728, 865, 936, 985, 986, 1009, 1104, 1155, 1221, 1282, 
                     1365, 1378, 1635, 1745, 1772, 1774, 1816, 1866, 2037, 2076, 2274, 2334, 2357, 2580, 2665, 
                     2706, 2713, 2771, 2868, 3156, 3192, 3198, 3261, 3271, 3276, 3296, 3342, 3387, 3398, 3466, 3496]

type2id = {'living room': 0, 'kitchen': 1, 'bedroom': 2, 'bathroom': 3, 'balcony': 4, 'corridor': 5,
            'dining room': 6, 'study': 7, 'studio': 8, 'store room': 9, 'garden': 10, 'laundry room': 11,
            'office': 12, 'basement': 13, 'garage': 14, 'undefined': 15, 'door': 16, 'window': 17}

def config():
    a = argparse.ArgumentParser(description='Generate coco format data for Structured3D')
    a.add_argument('--data_root', default='/home/<USER>/05_DL_dataset_wuhan/03_开源数据集/structure_3D/Panorama/', type=str, help='path to raw Structured3D_panorama folder')
    a.add_argument('--output', default='/home/<USER>/data/Structure3D/stru3d_512x512/', type=str, help='path to output folder')
    a.add_argument('--max_scenes', default=None, type=int, help='Maximum number of scenes to process (default: None, process all)')

    args = a.parse_args()
    return args

def validate_scene_data(scene_path, scene_id, logger):
    """验证场景数据的完整性"""
    # 检查点云文件是否存在
    ply_path = os.path.join(scene_path, 'point_cloud.ply')
    if not os.path.exists(ply_path):
        logger.warning(f"⚠️  场景 {scene_id}: 缺少点云文件 {ply_path}")
        return False, "missing_point_cloud"

    # 检查点云文件大小
    try:
        file_size = os.path.getsize(ply_path)
        if file_size < 1024:  # 小于1KB可能是空文件或损坏文件
            logger.warning(f"⚠️  场景 {scene_id}: 点云文件过小 ({file_size} bytes)")
            return False, "small_point_cloud"
    except Exception as e:
        logger.warning(f"⚠️  场景 {scene_id}: 无法检查点云文件大小 - {e}")
        return False, "point_cloud_access_error"

    # 检查注释文件是否存在
    annotation_path = os.path.join(scene_path, 'annotation_3d.json')
    if not os.path.exists(annotation_path):
        logger.warning(f"⚠️  场景 {scene_id}: 缺少注释文件 {annotation_path}")
        return False, "missing_annotation"

    return True, "valid"

def main(args):
    # 设置日志
    logger = setup_logging()

    logger.info("================================================================================")
    logger.info("🚀 开始生成Structured3D COCO格式数据")
    logger.info("================================================================================")
    logger.info(f"📁 数据根目录: {args.data_root}")
    logger.info(f"📂 输出目录: {args.output}")
    if args.max_scenes:
        logger.info(f"🔢 限制场景数量: {args.max_scenes}")

    data_root = args.data_root
    data_parts = [d for d in os.listdir(data_root)
                  if os.path.isdir(os.path.join(data_root, d))
                  and d.startswith('Structured3D_panorama')]
    data_parts = sorted(data_parts)

    logger.info(f"📦 找到 {len(data_parts)} 个数据部分: {data_parts}")

    ### prepare output directories
    outFolder = args.output
    if not os.path.exists(outFolder):
        os.makedirs(outFolder)

    annotation_outFolder = os.path.join(outFolder, 'annotations')
    if not os.path.exists(annotation_outFolder):
        os.makedirs(annotation_outFolder)

    train_img_folder = os.path.join(outFolder, 'train')
    val_img_folder = os.path.join(outFolder, 'val')
    test_img_folder = os.path.join(outFolder, 'test')

    for img_folder in [train_img_folder, val_img_folder, test_img_folder]:
        if not os.path.exists(img_folder):
            os.makedirs(img_folder)

    coco_train_json_path = os.path.join(annotation_outFolder, 'train.json')
    coco_val_json_path = os.path.join(annotation_outFolder, 'val.json')
    coco_test_json_path = os.path.join(annotation_outFolder, 'test.json')

    coco_train_dict = {"images":[],"annotations":[],"categories":[]}
    coco_val_dict = {"images":[],"annotations":[],"categories":[]}
    coco_test_dict = {"images":[],"annotations":[],"categories":[]}

    for key, value in type2id.items():
        type_dict = {"supercategory": "room", "id": value, "name": key}
        coco_train_dict["categories"].append(type_dict)
        coco_val_dict["categories"].append(type_dict)
        coco_test_dict["categories"].append(type_dict)

    ### begin processing with enhanced validation and logging
    instance_id = 0
    total_scenes_found = 0
    scenes_with_missing_data = 0
    scenes_processed = 0
    scenes_skipped_invalid = 0

    # 收集所有场景信息
    all_scenes = []
    for part in data_parts:
        part_path = os.path.join(data_root, part, 'Structured3D')
        if os.path.exists(part_path):
            try:
                scenes = [s for s in os.listdir(part_path)
                         if os.path.isdir(os.path.join(part_path, s))]
                scenes = sorted(scenes)
                total_scenes_found += len(scenes)

                for scene in scenes:
                    scene_path = os.path.join(part_path, scene)
                    scene_id = scene.split('_')[-1]
                    all_scenes.append((part, scene, scene_path, scene_id))

                logger.info(f"  📂 {part}: {len(scenes)} 个场景")
            except Exception as e:
                logger.warning(f"⚠️  无法读取部分 {part}: {e}")

    logger.info(f"🎯 总共找到 {total_scenes_found} 个场景")

    # 应用场景数量限制
    if args.max_scenes:
        logger.info(f"🔢 限制处理场景数量: {args.max_scenes}")
        all_scenes = all_scenes[:args.max_scenes]

    logger.info("================================================================================")
    logger.info(f"🚀 开始处理 {len(all_scenes)} 个场景...")

    for part, scene, scene_path, scene_id in tqdm(all_scenes, desc="Processing scenes"):
        try:
            # 检查是否在无效场景列表中
            if int(scene_id) in invalid_scenes_ids:
                logger.info(f"⏭️  跳过无效场景: {scene_id}")
                scenes_skipped_invalid += 1
                continue

            # 验证场景数据完整性
            is_valid, validation_result = validate_scene_data(scene_path, scene_id, logger)
            if not is_valid:
                logger.warning(f"⚠️  跳过场景 {scene_id}: {validation_result}")
                scenes_with_missing_data += 1
                continue

            # 尝试加载点云数据
            ply_path = os.path.join(scene_path, 'point_cloud.ply')
            try:
                points = read_scene_pc(ply_path)
                xyz = points[:, :3]

                # 应用Z坐标过滤（参考HC数据集的实现）
                # 计算点云的Z坐标范围
                z_min, z_max = np.min(xyz[:, 2]), np.max(xyz[:, 2])
                z_range = z_max - z_min

                original_point_count = len(xyz)

                # 只有当Z范围足够大时才进行过滤
                if z_range > 0.05:  # 如果Z范围大于5cm，才进行过滤
                    # 过滤掉过高和过低的点（保留中间部分，去除天花板和地板噪声）
                    # 类似HC数据集中的 +0.25 和 -0.25 偏移
                    z_filter_margin =  z_range * 0.25  # 动态调整过滤边距
                    z_filter = (xyz[:, 2] > z_min + z_filter_margin) & (xyz[:, 2] < z_max - z_filter_margin)

                    # 应用过滤
                    xyz = xyz[z_filter]
                    filtered_point_count = len(xyz)

                    logger.debug(f"✅ 场景 {scene_id}: Z坐标过滤 {original_point_count} -> {filtered_point_count} 个点 "
                               f"(Z范围: {z_min:.2f} ~ {z_max:.2f}, 过滤边距: {z_filter_margin:.2f})")
                else:
                    # Z范围太小，跳过过滤
                    filtered_point_count = original_point_count
                    logger.debug(f"✅ 场景 {scene_id}: Z范围过小({z_range:.3f}m)，跳过Z坐标过滤")

                logger.debug(f"✅ 场景 {scene_id}: 成功加载 {original_point_count} 个点")



                if len(xyz) == 0:
                    logger.warning(f"⚠️  场景 {scene_id}: Z坐标过滤后无剩余点，跳过处理")
                    scenes_with_missing_data += 1
                    continue

            except Exception as e:
                logger.error(f"❌ 场景 {scene_id}: 无法加载点云数据 - {e}")
                scenes_with_missing_data += 1
                continue

            # 处理场景数据
            try:
                ### project point cloud to density map
                density, normalization_dict = generate_density(xyz, width=set_img_size, height=set_img_size)

                ### rescale raw annotations
                normalized_annos = normalize_annotations(scene_path, normalization_dict)

                ### prepare coco dict
                img_id = int(scene_id)
                img_dict = {}
                img_dict["file_name"] = scene_id + '.png'
                img_dict["id"] = img_id
                img_dict["width"] = set_img_size
                img_dict["height"] = set_img_size

                ### parse annotations
                polys = parse_floor_plan_polys(normalized_annos)
                polygons_list = generate_coco_dict(normalized_annos, polys, instance_id, img_id, ignore_types=['outwall'])

                instance_id += len(polygons_list)

                ### train
                if int(scene_id) < 3000:
                    coco_train_dict["images"].append(img_dict)
                    coco_train_dict["annotations"] += polygons_list
                    export_density(density, train_img_folder, scene_id)

                ### val
                elif int(scene_id) >= 3000 and int(scene_id) < 3250:
                    coco_val_dict["images"].append(img_dict)
                    coco_val_dict["annotations"] += polygons_list
                    export_density(density, val_img_folder, scene_id)

                ### test
                else:
                    coco_test_dict["images"].append(img_dict)
                    coco_test_dict["annotations"] += polygons_list
                    export_density(density, test_img_folder, scene_id)

                scenes_processed += 1
                logger.debug(f"✅ 场景 {scene_id}: 处理完成")

            except Exception as e:
                logger.error(f"❌ 场景 {scene_id}: 处理失败 - {e}")
                scenes_with_missing_data += 1
                continue

        except Exception as e:
            logger.error(f"❌ 场景 {scene}: 处理时发生意外错误 - {e}")
            continue


    # 保存COCO格式文件
    logger.info("💾 保存COCO格式文件...")

    with open(coco_train_json_path, 'w') as f:
        json.dump(coco_train_dict, f)
    logger.info(f"✅ 训练集保存到: {coco_train_json_path}")

    with open(coco_val_json_path, 'w') as f:
        json.dump(coco_val_dict, f)
    logger.info(f"✅ 验证集保存到: {coco_val_json_path}")

    with open(coco_test_json_path, 'w') as f:
        json.dump(coco_test_dict, f)
    logger.info(f"✅ 测试集保存到: {coco_test_json_path}")

    # 输出处理统计
    logger.info("================================================================================")
    logger.info("🎉 COCO数据生成完成！")
    logger.info("================================================================================")
    logger.info("📊 处理统计:")
    logger.info(f"   总场景数: {total_scenes_found}")
    logger.info(f"   成功处理: {scenes_processed}")
    logger.info(f"   跳过无效: {scenes_skipped_invalid}")
    logger.info(f"   数据缺失: {scenes_with_missing_data}")
    logger.info(f"   成功率: {scenes_processed/(total_scenes_found-scenes_skipped_invalid)*100:.1f}%")

    logger.info("📂 输出文件:")
    logger.info(f"   训练集图像: {len(coco_train_dict['images'])} 张")
    logger.info(f"   验证集图像: {len(coco_val_dict['images'])} 张")
    logger.info(f"   测试集图像: {len(coco_test_dict['images'])} 张")
    logger.info(f"   训练集注释: {len(coco_train_dict['annotations'])} 个")
    logger.info(f"   验证集注释: {len(coco_val_dict['annotations'])} 个")
    logger.info(f"   测试集注释: {len(coco_test_dict['annotations'])} 个")

    if scenes_with_missing_data > 0:
        logger.warning(f"⚠️  {scenes_with_missing_data} 个场景因数据缺失被跳过")
        logger.info("💡 建议: 检查点云生成日志以了解缺失数据的原因")

    logger.info("================================================================================")


if __name__ == "__main__":

    main(config())