#!/usr/bin/env python3
"""
文件结构格式转换脚本
将03_done_data_file_structure.txt的树状结构格式转换为selected_filelist.txt格式

输入格式 (03_done_data_file_structure.txt):
/home/<USER>/01_3D-FAVP/01_semantic/03_done_data/RS10/
├── RS10_Batch_01
│   ├── 000_biguiyuanxingzuan_res-ff_RS10
│   ├── 001_biguiyuanxingzuan_res-ff_RS10
├── RS10_Batch_02
│   ├── 004_cuishanlantian_res-uf_RS10
│   └── 008_cuishanlantian_res-uf_RS10

输出格式 (selected_filelist.txt):
RS10_Batch_01\000_biguiyuanxingzuan_res-ff_RS10
RS10_Batch_01\001_biguiyuanxingzuan_res-ff_RS10
RS10_Batch_02\004_cuishanlantian_res-uf_RS10
RS10_Batch_02\008_cuishanlantian_res-uf_RS10

用法:
python convert_file_structure_to_selected_list.py
"""

import os
import re
import argparse
from pathlib import Path

def parse_tree_structure(input_file):
    """
    解析树状结构文件，提取批次和文件名信息
    
    Args:
        input_file (str): 输入文件路径
        
    Returns:
        list: [(batch_name, filename), ...] 格式的列表
    """
    results = []
    current_batch = None
    
    with open(input_file, 'r', encoding='utf-8') as f:
        for line_num, line in enumerate(f, 1):
            line = line.rstrip('\n\r')
            
            # 跳过空行和根目录行
            if not line.strip() or line.startswith('/home/'):
                continue
            
            # 匹配批次目录行: ├── RS10_Batch_XX 或 └── RS10_Batch_XX
            batch_match = re.match(r'^[├└]── (RS10_Batch_\d+)$', line)
            if batch_match:
                current_batch = batch_match.group(1)
                print(f"  找到批次: {current_batch}")
                continue
            
            # 匹配文件名行: │   ├── filename 或 │   └── filename 或     ├── filename
            file_match = re.match(r'^(?:│\s+|\s{4,})[├└]── (.+)$', line)
            if file_match and current_batch:
                filename = file_match.group(1).strip()

                # 验证文件名格式 (应该是: 数字_社区名_res-属性_RS10)
                if re.match(r'^\d{3}_\w+_res-\w+_RS10$', filename):
                    results.append((current_batch, filename))
                    print(f"    添加文件: {filename}")
                else:
                    print(f"  ⚠️  跳过无效文件名格式 (行{line_num}): {filename}")
                continue
            
            # 如果行不匹配任何模式，显示警告
            if line.strip() and not line.startswith('│'):
                print(f"  ⚠️  无法解析行 {line_num}: {line}")
    
    return results

def write_selected_filelist(results, output_file):
    """
    将解析结果写入selected_filelist.txt格式
    
    Args:
        results (list): [(batch_name, filename), ...] 格式的列表
        output_file (str): 输出文件路径
    """
    with open(output_file, 'w', encoding='utf-8') as f:
        for batch_name, filename in results:
            # 使用反斜杠分隔符 (Windows格式)
            f.write(f"{batch_name}\\{filename}\n")

def main():
    parser = argparse.ArgumentParser(description='转换文件结构格式')
    parser.add_argument('--input', '-i', 
                       default='tools/C++_PC2IMG/filelists/03_done_data_file_structure.txt',
                       help='输入文件路径 (默认: tools/C++_PC2IMG/filelists/03_done_data_file_structure.txt)')
    parser.add_argument('--output', '-o',
                       default='tools/C++_PC2IMG/filelists/selected_filelist.txt', 
                       help='输出文件路径 (默认: tools/C++_PC2IMG/filelists/selected_filelist.txt)')
    parser.add_argument('--dry-run', action='store_true',
                       help='预览模式，只显示结果不写入文件')
    
    args = parser.parse_args()
    
    print("🔄 文件结构格式转换工具")
    print("=" * 50)
    print(f"📁 输入文件: {args.input}")
    print(f"📁 输出文件: {args.output}")
    print(f"🔧 模式: {'预览模式' if args.dry_run else '转换模式'}")
    print()
    
    # 检查输入文件是否存在
    if not os.path.exists(args.input):
        print(f"❌ 错误: 输入文件不存在: {args.input}")
        return 1
    
    # 解析输入文件
    print("🔍 解析树状结构文件...")
    results = parse_tree_structure(args.input)
    
    if not results:
        print("❌ 错误: 没有找到有效的文件条目")
        return 1
    
    print(f"\n✅ 解析完成，找到 {len(results)} 个文件条目")
    
    # 按批次分组显示统计
    batch_stats = {}
    for batch_name, filename in results:
        if batch_name not in batch_stats:
            batch_stats[batch_name] = []
        batch_stats[batch_name].append(filename)
    
    print("\n📊 批次统计:")
    for batch_name in sorted(batch_stats.keys()):
        files = batch_stats[batch_name]
        print(f"  {batch_name}: {len(files)} 个文件")
    
    if args.dry_run:
        print("\n📋 预览结果 (前10条):")
        for batch_name, filename in results[:10]:
            print(f"  {batch_name}\\{filename}")
        if len(results) > 10:
            print(f"  ... 还有 {len(results) - 10} 条记录")
    else:
        # 创建输出目录
        output_dir = os.path.dirname(args.output)
        if output_dir:
            os.makedirs(output_dir, exist_ok=True)
        
        # 写入输出文件
        print(f"\n💾 写入输出文件: {args.output}")
        write_selected_filelist(results, args.output)
        
        print(f"✅ 转换完成！生成了 {len(results)} 条记录")
        print(f"📁 输出文件: {args.output}")

if __name__ == "__main__":
    exit(main())
