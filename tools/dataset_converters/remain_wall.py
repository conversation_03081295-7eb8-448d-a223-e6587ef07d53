import os
import cv2
import numpy as np
from glob import glob

def process_images(input_folder, output_folder):
    # 创建输出文件夹
    if not os.path.exists(output_folder):
        os.makedirs(output_folder)

    # 获取所有 *_gt.png 文件
    gt_files = glob(os.path.join(input_folder, '*_gt.png'))

    for gt_file in gt_files:
        # 读取 gt 图像
        gt_img = cv2.imread(gt_file, cv2.IMREAD_GRAYSCALE)
        
        # 获取对应的原始图像文件名
        base_name = os.path.basename(gt_file).replace('_gt.png', '.png')
        orig_file = os.path.join(input_folder, base_name)
        
        if not os.path.exists(orig_file):
            print(f"找不到对应的原始图像: {orig_file}")
            continue
            
        # 读取原始图像
        orig_img = cv2.imread(orig_file, cv2.IMREAD_GRAYSCALE)
        
        # 创建掩码：gt图像中非255的位置
        mask = (gt_img == 255) & (orig_img > 0)
        
        # 将原始图像中对应位置置0
        result_img = np.zeros_like(orig_img, dtype=np.uint8)
        result_img[mask] = 255
        
        # 保存结果
        output_name = base_name.replace('.png', '_wall.png')
        output_path = os.path.join(output_folder, output_name)
        cv2.imwrite(output_path, result_img)
        print(f"处理完成: {output_path}")

if __name__ == "__main__":
    # 设置输入和输出文件夹
    input_folder = "data/hc_rs10_q2_wo_floor_2/train"  # 替换为你的输入文件夹路径
    output_folder = "output/wall"     # 输出文件夹路径
    
    process_images(input_folder, output_folder)