import os
import json
import numpy as np
from pathlib import Path
import cv2

def parse_calibration_file(file_path):
    """
    Parses the calibration JSON file and extracts relevant data.

    Args:
        file_path (str): Path to the calibration JSON file.

    Returns:
        list[dict]: A list of dictionaries containing scene_name, rotation_angle, roi_max, and roi_min.
    """
    with open(file_path, 'r') as file:
        data = json.load(file)
    
    # Extract the 'calib' list from the JSON data
    calib_data = data.get("calib", [])
    
    # Create a list of dictionaries with the required fields
    parsed_data = [
        {
            "scene_name": entry["scene_name"],
            "rotation_angle": entry["rotation_angle"],
            "roi_max": entry["roi_max"],
            "roi_min": entry["roi_min"]
        }
        for entry in calib_data
    ]
    
    return parsed_data


def generate_density(point_cloud, max_coords, min_coords, width=256, height=256):

    ps = point_cloud
    image_res = np.array((width, height))

    normalization_dict = {}
    normalization_dict["min_coords"] = min_coords
    normalization_dict["max_coords"] = max_coords
    normalization_dict["image_res"] = image_res

    coordinates = \
        np.round(
            (ps[:, :2] - min_coords) / (max_coords - min_coords) * image_res)
    coordinates = np.minimum(np.maximum(coordinates, np.zeros_like(image_res)),
                                image_res - 1)

    density = np.zeros((height, width), dtype=np.float32)

    unique_coordinates, counts = np.unique(coordinates, return_counts=True, axis=0)

    unique_coordinates = unique_coordinates.astype(np.int32)

    density[unique_coordinates[:, 1], unique_coordinates[:, 0]] = counts
    density = density / np.max(density)

    # 非线性映射，调整 density 值
    # mask_mid = (density >= 0.2) & (density <= 0.8)
    # mask_high = density > 0.8
    # density[mask_mid] *= 1.5
    # density[mask_high] = 1.0
    density[density > 0.0] = 1.0

    return density, normalization_dict

def get_wall_points(json_file_path):
    """解析单个JSON文件中的wall点云数据"""
    try:
        with open(json_file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        wall_points = []
        
        # 遍历所有frames和instances
        for frame in data['frames']:
            for instance in frame['instances']:
                if instance['category'] == 'wall':
                    for shape in instance['shapes']:
                        if shape['type'] == 'POINTS':
                            x = shape['shapeData']['x']
                            y = shape['shapeData']['y']
                            z = shape['shapeData']['z']
                            points = np.vstack((x, y, z))
                            wall_points.append(points)
        
        if wall_points:
            return np.hstack(wall_points).T
        return None
    except Exception as e:
        print(f"Error processing {json_file_path}: {str(e)}")
        return None

def process_folder(folder_path):
    """遍历文件夹处理所有JSON文件"""
    folder_path = Path(folder_path)
    output_folder = Path("output/density_maps")  # 创建输出文件夹
    output_folder.mkdir(parents=True, exist_ok=True)

    calib_json_file = r"/home/<USER>/01_3D-FAVP/handheld_scanner_Data/01_semantic/03_done_data/tmp_qinpei/calibration.json"
    calib_data = parse_calibration_file(calib_json_file)
    
    # 遍历所有文件和子文件夹
    for pcd_folder in folder_path.glob('**/pcd'):
        scene_name = ''
        slope_angle = 0
        roi_max = [0, 0]
        roi_min = [0, 0]
        for elm in str(pcd_folder).split('/'):
            if 'RS10' in elm:
                scene_name = elm
                print("scene_name: ", scene_name)

        for calib_elm in calib_data:
            if scene_name == calib_elm['scene_name']:
                slope_angle = calib_elm['rotation_angle']
                roi_max = calib_elm['roi_max']
                roi_min = calib_elm['roi_min']

        print("slope_angle, roi_max, roi_min: ", slope_angle, " ", roi_max, " ", roi_min)

        # rotation, 计算旋转矩阵
        rotation_matrix = cv2.getRotationMatrix2D((0, 0), np.degrees(slope_angle), 1)

        wall_points_all = []
        for file_path in pcd_folder.rglob('*.json'):
            print(f"\nProcessing: {file_path}")
            
            # 对点云进行旋转
            wall_points = get_wall_points(file_path)

            if wall_points is not None:
                print(f"Wall points shape: {wall_points.shape}")
            else:
                print("No wall points found in this file")
            wall_points_all.extend(list(wall_points))

        wall_points_all = np.array(wall_points_all)

        print("*****wall_points_all shape: ", wall_points_all.shape)

        # 只取前两列 (x, y) 用于2D旋转
        points_2d = wall_points_all[:, :2]
        
        # 添加ones列以进行齐次变换
        points_homogeneous = np.column_stack((points_2d, np.ones(points_2d.shape[0])))
        
        # 进行旋转变换
        rotated_points_2d = np.dot(points_homogeneous, rotation_matrix.T)
        
        # 构建完整的旋转后的点云，包含z坐标
        rotated_point_cloud = np.column_stack((rotated_points_2d, wall_points_all[:, 2]))

        # 密度图
        density, _ = generate_density(rotated_point_cloud, np.array(roi_max), np.array(roi_min), width=1024, height=1024)

        # 将浮点数密度图转换为8位无符号整数格式
        density_uint8 = (density * 255).astype(np.uint8)
        
        # 构造输出文件名
        output_filename = output_folder / f"{scene_name}.png"
        
        # 保存密度图
        cv2.imwrite(str(output_filename), density_uint8)
        print(f"Density map saved to: {output_filename}")

# 使用示例
if __name__ == "__main__":
    # 替换为你的文件夹路径
    folder_path = r"/home/<USER>/01_3D-FAVP/handheld_scanner_Data/01_semantic/03_done_data/tmp_qinpei/gongyuanmingzhu_res_uf_RS10_7"
    
    calib_json_file = r"/home/<USER>/01_3D-FAVP/handheld_scanner_Data/01_semantic/03_done_data/tmp_qinpei/calibration.json"
    calib_data = parse_calibration_file(calib_json_file)

    # print(calib_data[0])
    process_folder(folder_path)