#!/bin/bash

# 优化版本的运行脚本
# 使用方法: ./run_optimized.sh

# 设置默认参数
DATA_ROOT="/home/<USER>/01_3D-FAVP/handheld_scanner_Data/RS10/"
FILELISTS_DIR="/home/<USER>/data/RS10_data/00_dataset_spilt/filelists"
OUTPUT_DIR="/home/<USER>/data/RS10_data/hc_rs10_optimized_$(date +%m%d_%H%M)_512x512/"
MAX_WORKERS=4
DOWNSAMPLE_FACTOR=4

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        --data_root)
            DATA_ROOT="$2"
            shift 2
            ;;
        --output)
            OUTPUT_DIR="$2"
            shift 2
            ;;
        --max_workers)
            MAX_WORKERS="$2"
            shift 2
            ;;
        --downsample_factor)
            DOWNSAMPLE_FACTOR="$2"
            shift 2
            ;;
        --help)
            echo "Usage: $0 [options]"
            echo "Options:"
            echo "  --data_root PATH          Path to RS10 data root (default: $DATA_ROOT)"
            echo "  --output PATH             Output directory (default: auto-generated)"
            echo "  --max_workers N           Number of parallel workers (default: $MAX_WORKERS)"
            echo "  --downsample_factor N     Point cloud downsampling factor (default: $DOWNSAMPLE_FACTOR)"
            echo "  --help                    Show this help message"
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            echo "Use --help for usage information"
            exit 1
            ;;
    esac
done

# 显示配置
echo "=== Optimized Point Cloud Processing ==="
echo "Data root: $DATA_ROOT"
echo "Filelists dir: $FILELISTS_DIR"
echo "Output dir: $OUTPUT_DIR"
echo "Max workers: $MAX_WORKERS"
echo "Downsample factor: $DOWNSAMPLE_FACTOR"
echo "========================================"

# 检查输入目录
if [ ! -d "$DATA_ROOT" ]; then
    echo "Error: Data root directory does not exist: $DATA_ROOT"
    exit 1
fi

if [ ! -d "$FILELISTS_DIR" ]; then
    echo "Error: Filelists directory does not exist: $FILELISTS_DIR"
    exit 1
fi

# 创建输出目录
mkdir -p "$OUTPUT_DIR"

# 记录开始时间
START_TIME=$(date +%s)
echo "Starting processing at $(date)"

# 运行优化版本
python generate_coco_hc_0722.py \
    --data_root "$DATA_ROOT" \
    --filelists_dir "$FILELISTS_DIR" \
    --output "$OUTPUT_DIR" \
    --max_workers "$MAX_WORKERS" \
    --downsample_factor "$DOWNSAMPLE_FACTOR" \
    --use_optimized_density \
    --density_height 512 \
    --density_width 512

# 记录结束时间
END_TIME=$(date +%s)
DURATION=$((END_TIME - START_TIME))

echo "========================================"
echo "Processing completed at $(date)"
echo "Total time: ${DURATION} seconds ($(($DURATION / 60)) minutes)"
echo "Output saved to: $OUTPUT_DIR"

# 显示输出统计
if [ -d "$OUTPUT_DIR" ]; then
    echo ""
    echo "Output statistics:"
    echo "- Train images: $(find "$OUTPUT_DIR/train" -name "*.png" | wc -l)"
    echo "- Val images: $(find "$OUTPUT_DIR/val" -name "*.png" | wc -l)"
    echo "- Test images: $(find "$OUTPUT_DIR/test" -name "*.png" 2>/dev/null | wc -l)"
    echo "- Total size: $(du -sh "$OUTPUT_DIR" | cut -f1)"
fi

echo "Done!"
