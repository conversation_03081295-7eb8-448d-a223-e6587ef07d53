import cv2
import numpy as np
import os
import re
from pathlib import Path

def detect_and_mark_corners(input_folder, output_folder):
    # 创建输出目录
    os.makedirs(output_folder, exist_ok=True)
    
    # 遍历输入文件夹中的所有文件
    for filename in os.listdir(input_folder):
        # 筛选包含"_border.png"的文件
        if ".png" in filename: # "_border.png" 
            file_path = os.path.join(input_folder, filename)
            
            # 读取图像
            img = cv2.imread(file_path)
            if img is None:
                print(f"无法读取图像: {file_path}")
                continue
                
            # 转换为灰度图
            img_resize = cv2.resize(img, (1024, 1024), interpolation=cv2.INTER_NEAREST)
            gray = cv2.cvtColor(img_resize, cv2.COLOR_BGR2GRAY)
            
            # 角点检测参数优化
            corners = cv2.goodFeaturesToTrack(
                gray, 
                maxCorners=200, 
                qualityLevel=0.01, 
                minDistance=3,
                blockSize=3,
                useHarrisDetector=False,
                k=0.04
            )
            
            # 检查是否检测到角点
            if corners is None:
                print(f"未检测到角点: {filename}")
                continue
                
            corners = np.int0(corners)
            
            # 用"十字架"标注角点
            for corner in corners:
                x, y = corner.ravel()
                
                # 绘制十字标记（线条长度为5像素）
                cv2.line(img_resize, (x-4, y), (x+4, y), (0, 0, 255), 2)  # 横线
                cv2.line(img_resize, (x, y-4), (x, y+4), (0, 0, 255), 2)  # 竖线
                
                # 添加中心点（增强视觉效果）
                cv2.circle(img_resize, (x, y), 1, (0, 255, 0), -1)
            
            # 保存结果
            output_path = os.path.join(output_folder, f"{Path(filename).stem}_corners.png")
            cv2.imwrite(output_path, img_resize)
            print(f"已保存结果: {output_path}")

if __name__ == "__main__":
    input_folder = "output/density_maps"  # 输入文件夹路径
    output_folder = "output/corner_maps"  # 输出文件夹路径
    
    detect_and_mark_corners(input_folder, output_folder)