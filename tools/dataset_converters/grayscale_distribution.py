import argparse
import os
from pathlib import Path
from typing import List

import cv2  # type: ignore
import matplotlib.pyplot as plt
import numpy as np


# ----------------------------- New helper -----------------------------
def save_histogram_txt(hist: np.ndarray, bin_interval: int, txt_file: Path) -> None:
    """Save histogram counts to a plain-text file.

    Each line contains the grayscale value (or range) and the pixel count, separated by a tab.
    Example (bin_interval=1):
        0   12345
        1   67890
    Example (bin_interval=4):
        0-3 9876
        4-7 4321
    """
    lines: List[str] = []
    for i, count in enumerate(hist):
        start_gray = i * bin_interval
        if bin_interval == 1:
            label = f"{start_gray}"
        else:
            end_gray = start_gray + bin_interval - 1
            label = f"{start_gray}-{end_gray}"
        lines.append(f"{label}\t{int(count)}\n")

    txt_file.parent.mkdir(parents=True, exist_ok=True)
    with open(txt_file, "w", encoding="utf-8") as f:
        f.writelines(lines)
    print(f"Histogram counts saved to {txt_file}")


# -------------------- Highlight helper --------------------
def highlight_images(
    image_paths: List[Path],
    threshold: int,
    out_root: Path,
    bin_interval: int,
    verbose: bool = True,
) -> None:
    """Highlight pixels with grayscale > *threshold* in red and save copies.

    Args:
        image_paths (List[Path]): Images to process.
        threshold (int): Grayscale threshold.
        out_root (Path): Root directory to save highlighted images.
        bin_interval (int): Used only for verbose context, not internally.
        verbose (bool): Print progress if True.
    """
    out_root.mkdir(parents=True, exist_ok=True)

    for img_path in image_paths:
        # Read in color to preserve channels; convert to grayscale for mask
        color_img = cv2.imread(str(img_path), cv2.IMREAD_COLOR)
        if color_img is None:
            if verbose:
                print(f"[Warning] Could not read {img_path} for highlighting")
            continue

        gray = cv2.cvtColor(color_img, cv2.COLOR_BGR2GRAY)
        mask = gray > threshold

        # Apply red color to pixels exceeding threshold
        color_img[mask] = (0, 0, 255)  # BGR red

        # Compose output path (flat structure or preserve-tree?)
        out_path = out_root / img_path.name
        cv2.imwrite(str(out_path), color_img)

        if verbose:
            print(f"Saved highlighted image to {out_path}")


def collect_image_paths(root: Path, exts: List[str], exclude_substr: str) -> List[Path]:
    """Recursively collect image files under *root* with suffix in *exts*.

    Args:
        root (Path): Root directory to search.
        exts (List[str]): List of lowercase extensions to match (e.g. [".jpg", ".png"]).
        exclude_substr (str): Substring appearing in filenames to exclude (case-sensitive).

    Returns:
        List[Path]: A list of image paths satisfying the criteria.
    """
    image_files: List[Path] = []
    for dirpath, _, filenames in os.walk(root):
        for fname in filenames:
            if exclude_substr and exclude_substr in fname:
                continue  # Skip visualisation/GT images
            if Path(fname).suffix.lower() in exts:
                image_files.append(Path(dirpath) / fname)
    return image_files


def compute_histogram(image_paths: List[Path], bin_interval: int = 1) -> np.ndarray:
    """Compute the aggregated grayscale histogram for multiple images.

    Args:
        image_paths (List[Path]): Paths to the images.
        bin_interval (int): Width of each histogram bin. Must divide 256.

    Returns:
        np.ndarray: Histogram counts of length 256 // bin_interval.
    """
    if 256 % bin_interval != 0:
        raise ValueError("bin_interval must be a factor of 256 (e.g. 1, 2, 4, 8, 16, 32, 64, 128, 256)")

    hist = np.zeros(256, dtype=int)

    for img_path in image_paths:
        img = cv2.imread(str(img_path), cv2.IMREAD_GRAYSCALE)
        if img is None:
            print(f"[Warning] Failed to read image: {img_path}")
            continue
        # Flatten and count values efficiently
        vals, counts = np.unique(img, return_counts=True)
        hist[vals] += counts

    if bin_interval == 1:
        return hist
    # Aggregate bins
    new_bins = 256 // bin_interval
    hist_grouped = hist.reshape(new_bins, bin_interval).sum(axis=1)
    return hist_grouped


def plot_histogram(hist: np.ndarray, bin_interval: int, out_file: Path, title: str = "Grayscale Distribution") -> None:
    """Plot and save the grayscale histogram."""
    x = np.arange(0, 256, bin_interval)
    plt.figure(figsize=(12, 6))
    plt.bar(x, hist, width=bin_interval, align="edge", edgecolor="black")
    plt.xlabel("Grayscale value")
    plt.ylabel("Pixel count")
    plt.title(title)
    plt.xlim([0, 256])
    plt.grid(axis="y", linestyle="--", alpha=0.4)
    plt.tight_layout()

    out_file.parent.mkdir(parents=True, exist_ok=True)
    plt.savefig(out_file)
    plt.close()
    print(f"Histogram saved to {out_file}")


def parse_args() -> argparse.Namespace:
    parser = argparse.ArgumentParser(description="Compute and plot grayscale distribution for an image dataset.")
    parser.add_argument("data_root", type=str, help="Root directory containing images (searched recursively)")
    parser.add_argument("--out", type=str, default="grayscale_distribution.png", help="Output filename for the histogram image")
    parser.add_argument("--bin_interval", type=int, default=1, help="Bin width for grayscale histogram (must divide 256)")
    parser.add_argument(
        "--exts",
        type=str,
        nargs="+",
        default=[".jpg", ".jpeg", ".png", ".bmp", ".tif", ".tiff"],
        help="Image extensions to consider (case-insensitive)",
    )
    parser.add_argument(
        "--exclude_substr",
        type=str,
        default="_gt",
        help="Substring in filename to exclude from statistics (e.g., '_gt'). Use '' to disable.",
    )
    parser.add_argument(
        "--txt",
        type=str,
        default="",
        help="If provided, save histogram counts to this txt file (e.g., counts.txt)",
    )
    parser.add_argument(
        "--highlight_threshold",
        type=int,
        default=None,
        help="If provided, highlight pixels with gray value greater than this threshold in red and save images.",
    )
    parser.add_argument(
        "--highlight_out_root",
        type=str,
        default="",
        help=(
            "Directory to save highlighted images. If empty, will create "
            "'/home/<USER>/data/RS10_data/tmp/highlight_walls_threshold_<threshold>/'"
        ),
    )
    return parser.parse_args()


def main() -> None:
    args = parse_args()
    data_root = Path(args.data_root).expanduser().resolve()
    if not data_root.is_dir():
        raise FileNotFoundError(f"{data_root} is not a valid directory")

    exts = [ext.lower() if ext.startswith(".") else f".{ext.lower()}" for ext in args.exts]
    image_paths = collect_image_paths(data_root, exts, args.exclude_substr)
    if not image_paths:
        raise RuntimeError(
            f"No images (excluding '*{args.exclude_substr}*') with extensions {exts} found under {data_root}"
        )

    print(
        f"Found {len(image_paths)} images (excluded substring '{args.exclude_substr}'). "
        f"Computing histogram with bin_interval={args.bin_interval}..."
    )
    hist = compute_histogram(image_paths, args.bin_interval)

    out_file = Path(args.out)
    plot_histogram(hist, args.bin_interval, out_file)

    # Optionally save txt histogram
    if args.txt:
        txt_file = Path(args.txt)
        save_histogram_txt(hist, args.bin_interval, txt_file)

    # Optionally highlight pixels above threshold
    if args.highlight_threshold is not None:
        if args.highlight_out_root:
            out_root = Path(args.highlight_out_root)
        else:
            out_root = Path("/home/<USER>/data/RS10_data/tmp") / f"highlight_walls_threshold_{args.highlight_threshold}"
        print(
            f"Highlighting pixels with grayscale > {args.highlight_threshold} "
            f"and saving to {out_root}..."
        )
        highlight_images(
            image_paths,
            threshold=args.highlight_threshold,
            out_root=out_root,
            bin_interval=args.bin_interval,
        )


if __name__ == "__main__":
    main() 