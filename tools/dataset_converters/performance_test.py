#!/usr/bin/env python3
"""
性能测试脚本，用于比较优化前后的处理速度
"""

import time
import os
import sys
import argparse
import numpy as np
from tqdm import tqdm

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from hc_utils import generate_density, generate_density_optimized
from generate_coco_hc_0722 import load_and_process_point_clouds_optimized, process_single_las_file


def benchmark_density_generation(point_cloud, max_coords, min_coords, width=512, height=512, iterations=10):
    """比较两种密度生成方法的性能"""
    
    print(f"Benchmarking density generation with {point_cloud.shape[0]} points...")
    
    # 测试原始方法
    times_original = []
    for i in range(iterations):
        start_time = time.time()
        density_orig, _ = generate_density(point_cloud, max_coords, min_coords, width, height)
        end_time = time.time()
        times_original.append(end_time - start_time)
    
    # 测试优化方法
    times_optimized = []
    for i in range(iterations):
        start_time = time.time()
        density_opt, _ = generate_density_optimized(point_cloud, max_coords, min_coords, width, height)
        end_time = time.time()
        times_optimized.append(end_time - start_time)
    
    avg_original = np.mean(times_original)
    avg_optimized = np.mean(times_optimized)
    speedup = avg_original / avg_optimized
    
    print(f"Original method: {avg_original:.4f}s ± {np.std(times_original):.4f}s")
    print(f"Optimized method: {avg_optimized:.4f}s ± {np.std(times_optimized):.4f}s")
    print(f"Speedup: {speedup:.2f}x")
    
    return speedup


def test_point_cloud_loading(data_root, scene_name, iterations=3):
    """测试点云加载性能"""
    
    print(f"Testing point cloud loading for scene: {scene_name}")
    
    # 模拟参数
    pointcloud_roi = [-10, -10, 0, 10, 10, 3]  # 示例ROI
    slope_angle = 0.1  # 示例角度
    
    # 确定LAS目录
    las_dir = 'LAS_Refined' if os.path.exists(os.path.join(data_root, scene_name, 'LAS_Refined')) else 'LAS'
    las_folder = os.path.join(data_root, scene_name, las_dir)
    
    if not os.path.exists(las_folder):
        print(f"LAS folder not found: {las_folder}")
        return
    
    las_files = [f for f in os.listdir(las_folder) if f.endswith('.las')]
    if not las_files:
        print(f"No LAS files found in {las_folder}")
        return
    
    print(f"Found {len(las_files)} LAS files")
    
    # 测试优化的并行加载
    times_parallel = []
    for i in range(iterations):
        start_time = time.time()
        points = load_and_process_point_clouds_optimized(
            data_root, scene_name, las_dir, pointcloud_roi, slope_angle,
            downsample_factor=4, max_workers=4
        )
        end_time = time.time()
        times_parallel.append(end_time - start_time)
        print(f"Iteration {i+1}: {end_time - start_time:.2f}s, loaded {points.shape[0]} points")
    
    avg_parallel = np.mean(times_parallel)
    print(f"Average parallel loading time: {avg_parallel:.2f}s ± {np.std(times_parallel):.2f}s")
    
    return avg_parallel


def generate_test_data(n_points=100000):
    """生成测试用的点云数据"""
    np.random.seed(42)
    
    # 生成随机点云
    points = np.random.randn(n_points, 2) * 5
    
    # 计算边界
    min_coords = np.min(points, axis=0) - 1
    max_coords = np.max(points, axis=0) + 1
    
    return points, max_coords, min_coords


def main():
    parser = argparse.ArgumentParser(description='Performance testing for optimized point cloud processing')
    parser.add_argument('--data_root', default='/home/<USER>/01_3D-FAVP/handheld_scanner_Data/RS10/', 
                       help='Path to data root')
    parser.add_argument('--scene_name', default=None, help='Specific scene to test (optional)')
    parser.add_argument('--test_density', action='store_true', help='Test density generation performance')
    parser.add_argument('--test_loading', action='store_true', help='Test point cloud loading performance')
    parser.add_argument('--n_points', default=100000, type=int, help='Number of test points for density benchmark')
    
    args = parser.parse_args()
    
    print("=== Point Cloud Processing Performance Test ===")
    
    if args.test_density:
        print("\n1. Testing density generation performance...")
        points, max_coords, min_coords = generate_test_data(args.n_points)
        speedup = benchmark_density_generation(points, max_coords, min_coords)
        print(f"Density generation speedup: {speedup:.2f}x")
    
    if args.test_loading and args.scene_name:
        print(f"\n2. Testing point cloud loading performance...")
        if os.path.exists(args.data_root):
            avg_time = test_point_cloud_loading(args.data_root, args.scene_name)
            print(f"Average loading time: {avg_time:.2f}s")
        else:
            print(f"Data root not found: {args.data_root}")
    
    if args.test_loading and not args.scene_name:
        print("\n2. Listing available scenes for testing...")
        if os.path.exists(args.data_root):
            scenes = [d for d in os.listdir(args.data_root) 
                     if os.path.isdir(os.path.join(args.data_root, d))]
            print(f"Available scenes: {scenes[:10]}...")  # 显示前10个
            print("Use --scene_name to specify a scene for loading test")
        else:
            print(f"Data root not found: {args.data_root}")


if __name__ == "__main__":
    main()
