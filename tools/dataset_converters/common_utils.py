import numpy as np
import os
import cv2
from plyfile import PlyData, PlyElement

"""common_utils
常用工具函数：读取点云、判断多边形顺时针、重新排序角点以及导出密度图。
提供给 generate_coco_hc_0717.py / hc_utils.py 调用。
"""

# ==================  新增：Checklist 相关配置 ==================
# checklist.txt 会记录所有处理过程中出现的特殊情况，方便后续人工检查
CHECKLIST_PATH = '/home/<USER>/data/RS10_data/00_dataset_spilt/filelists/checklist.txt'

def append_to_checklist(category: str, message: str):
    """将异常信息追加到 checklist.txt（中文）

    参数说明：
        category: 异常类别标签，例如『缺少floorplan.json』『无效多边形』『斜率坐标异常』等。
        message: 具体的场景名或补充描述。
    """
    # 确保文件存在
    os.makedirs(os.path.dirname(CHECKLIST_PATH), exist_ok=True)
    with open(CHECKLIST_PATH, 'a', encoding='utf-8') as f:
        f.write(f'[{category}] {message}\n')
# ==================  Checklist 相关配置结束 ==================

def read_scene_pc(file_path):
    """读取 .ply 点云文件并转换为 numpy 数组。

    参数:
        file_path: 点云文件路径。
    返回:
        点坐标 ndarray，列顺序与 ply 顶点属性保持一致。
    """
    with open(file_path, 'rb') as f:
        plydata = PlyData.read(f)
        dtype = plydata['vertex'].data.dtype
    print('dtype of file{}: {}'.format(file_path, dtype))

    points_data = np.array(plydata['vertex'].data.tolist())

    return points_data


def is_clockwise(points):
    """判断多边形点集是否按顺时针排列。"""
    assert len(points) > 0
    s = 0.0
    for p1, p2 in zip(points, points[1:] + [points[0]]):
        s += (p2[0] - p1[0]) * (p2[1] + p1[1])
    return s > 0.0

def resort_corners(corners):
    """重新选择起点，并确保角点按顺时针顺序排列。"""
    x_y_square_sum = corners[:,0]**2 + corners[:,1]**2 
    start_corner_idx = np.argmin(x_y_square_sum)

    corners_sorted = np.concatenate([corners[start_corner_idx:], corners[:start_corner_idx]])

    ## sort points clockwise
    if not is_clockwise(corners_sorted[:,:2].tolist()):
        corners_sorted[1:] = np.flip(corners_sorted[1:], 0)

    return corners
    

def export_density(density_map, out_folder, scene_id):
    """将密度图保存为 8bit PNG。

    参数:
        density_map: 归一化后的二维数组 (0~1)。
        out_folder: 输出目录。
        scene_id: 文件名前缀。
    """
    density_path = os.path.join(out_folder, scene_id+'.png')
    density_uint8 = (density_map * 255).astype(np.uint8)
    cv2.imwrite(density_path, density_uint8)
