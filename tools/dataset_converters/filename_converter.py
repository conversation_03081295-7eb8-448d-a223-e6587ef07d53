#!/usr/bin/env python3
"""
Filename Format Converter and Directory Mapper

This module provides functionality to convert filenames from old format to new format
and determine the corresponding parent directory path.

Old format: {community_name}_{property}_{device_name}_{house_code}
New format: {house_code_3digits}_{community_name}_{property}_{device_name}

Author: Generated for Mask2Former_v2 project
"""

import os
import re
from typing import Union, List, Dict, Tuple, Optional


class FilenameConverter:
    """
    A class to handle filename format conversion and directory mapping.
    """
    
    def __init__(self, 
                 reference_file: str = "tools/C++_PC2IMG/filelists/pred_filelists.txt",
                 directory_structure_file: str = "tools/C++_PC2IMG/filelists/03_done_data_file_structure.txt"):
        """
        Initialize the FilenameConverter.
        
        Args:
            reference_file: Path to the file containing valid filenames
            directory_structure_file: Path to the file containing directory structure
        """
        self.reference_file = reference_file
        self.directory_structure_file = directory_structure_file
        self.valid_filenames = set()
        self.directory_mapping = {}
        
        self._load_reference_files()
    
    def _load_reference_files(self):
        """Load reference files and build internal mappings."""
        # Load valid filenames
        try:
            with open(self.reference_file, 'r', encoding='utf-8') as f:
                self.valid_filenames = {line.strip() for line in f if line.strip()}
        except FileNotFoundError:
            print(f"Warning: Reference file {self.reference_file} not found. "
                  "Validation will be skipped.")
        
        # Load directory structure and build mapping
        try:
            with open(self.directory_structure_file, 'r', encoding='utf-8') as f:
                self._parse_directory_structure(f.readlines())
        except FileNotFoundError:
            print(f"Warning: Directory structure file {self.directory_structure_file} not found. "
                  "Directory mapping will not be available.")
    
    def _parse_directory_structure(self, lines: List[str]):
        """
        Parse the directory structure file and build filename to directory mapping.

        Args:
            lines: Lines from the directory structure file
        """
        current_batch = None

        for line in lines:
            original_line = line
            line = line.strip()

            # Match batch directory lines (e.g., "├── RS10_Batch_02")
            batch_match = re.search(r'├── (RS10_Batch_\d+)', line)
            if batch_match:
                current_batch = batch_match.group(1)
                continue

            # Match filename lines (e.g., "│   ├── 004_cuishanlantian_res-uf_RS10")
            # Use original line to preserve whitespace for proper matching
            filename_match = re.search(r'│\s+├── (.+)', original_line)
            if filename_match and current_batch:
                filename = filename_match.group(1).strip()
                self.directory_mapping[filename] = current_batch
                continue

            # Match last filename in batch (e.g., "│   └── 019_cuishanlantian_res-ff_RS10")
            last_filename_match = re.search(r'│\s+└── (.+)', original_line)
            if last_filename_match and current_batch:
                filename = last_filename_match.group(1).strip()
                self.directory_mapping[filename] = current_batch
    
    def _parse_old_filename(self, filename: str) -> Optional[Dict[str, str]]:
        """
        Parse old format filename into components.

        Args:
            filename: Filename in old format

        Returns:
            Dictionary with parsed components or None if parsing fails
        """
        # Pattern: {community_name}_{property}_{device_name}_{house_code}
        # We need to be more specific about the pattern to avoid greedy matching
        # The device_name is typically "RS10" and house_code is digits
        # So we work backwards: last part is house_code (digits),
        # second to last is device_name (typically RS10),
        # third to last is property (res_uf, res_ff, etc.),
        # everything before that is community_name

        parts = filename.split('_')
        if len(parts) < 4:
            return None

        # Last part should be house_code (digits)
        house_code = parts[-1]
        if not house_code.isdigit():
            return None

        # Second to last should be device_name (e.g., RS10)
        device_name = parts[-2]

        # Third to last should be property (e.g., uf, ff)
        property_part = parts[-3]

        # Everything before that should be community_name + "res"
        # We need to reconstruct this properly
        remaining_parts = parts[:-3]
        if len(remaining_parts) < 2:
            return None

        # The last part of remaining should be "res"
        if remaining_parts[-1] != 'res':
            return None

        # Community name is everything except the last "res"
        community_name = '_'.join(remaining_parts[:-1])

        # Property is "res_" + property_part
        property_field = f"res_{property_part}"

        return {
            'community_name': community_name,
            'property': property_field,
            'device_name': device_name,
            'house_code': house_code
        }
    
    def _convert_to_new_format(self, components: Dict[str, str]) -> str:
        """
        Convert parsed components to new filename format.

        Args:
            components: Dictionary with filename components

        Returns:
            Filename in new format
        """
        # Zero-pad house code to 3 digits
        house_code_padded = components['house_code'].zfill(3)

        # Convert underscores to hyphens in property field
        property_formatted = components['property'].replace('_', '-')

        # Build new filename: {house_code_3digits}_{community_name}_{property-with-hyphens}_{device_name}
        new_filename = f"{house_code_padded}_{components['community_name']}_{property_formatted}_{components['device_name']}"

        return new_filename
    
    def convert_single_filename(self, filename: str) -> Optional[Dict[str, str]]:
        """
        Convert a single filename from old format to new format.
        
        Args:
            filename: Filename in old format
            
        Returns:
            Dictionary containing 'new_filename' and 'parent_directory' or None if conversion fails
        """
        # Validate filename exists in reference list
        if self.valid_filenames and filename not in self.valid_filenames:
            print(f"⚠️  Warning: Filename '{filename}' not found in reference file. Skipping.")
            return None
        
        # Parse old filename
        components = self._parse_old_filename(filename)
        if not components:
            print(f"⚠️  Warning: Could not parse filename '{filename}'. Invalid format.")
            return None
        
        # Convert to new format
        new_filename = self._convert_to_new_format(components)
        
        # Get parent directory
        parent_directory = self.directory_mapping.get(new_filename, "Unknown")
        if parent_directory == "Unknown":
            print(f"⚠️  Warning: No directory mapping found for '{filename} -> {new_filename}'.")
        
        return {
            'new_filename': new_filename,
            'parent_directory': parent_directory
        }
    
    def convert_filenames(self, filenames: Union[str, List[str]]) -> List[Dict[str, str]]:
        """
        Convert filename(s) from old format to new format with directory mapping.
        
        Args:
            filenames: Single filename string or list of filename strings
            
        Returns:
            List of dictionaries, each containing 'new_filename' and 'parent_directory'
        """
        # Handle single filename input
        if isinstance(filenames, str):
            filenames = [filenames]
        
        results = []
        
        for filename in filenames:
            result = self.convert_single_filename(filename)
            if result:
                results.append(result)
        
        return results

    def extract_scene_name(self, filename: str) -> str:
        """
        从文件名中提取场景名称

        Args:
            filename: 文件名（可能包含扩展名）

        Returns:
            提取的场景名称
        """
        # 移除扩展名
        base_name = os.path.splitext(filename)[0]

        # 尝试解析旧格式文件名
        parsed = self._parse_old_filename(base_name)
        if parsed and all(key in parsed for key in ['community_name', 'property', 'device_name', 'house_code']):
            # 构建场景名称：community_property_device_house
            return f"{parsed['community_name']}_{parsed['property']}_{parsed['device_name']}_{parsed['house_code']}"

        # 如果解析失败，直接返回基础文件名
        print(f"⚠️  无法解析文件名格式，使用原始名称: {base_name}")
        print(f"  解析结果: {parsed}")
        return base_name


def convert_filename(filenames: Union[str, List[str]],
                    reference_file: str = "output/tmp/pred_filelists.txt",
                    directory_structure_file: str = "tools/debug/filelists/03_done_data_file_structure.txt") -> List[Dict[str, str]]:
    """
    Convenience function to convert filenames without creating a converter instance.
    
    Args:
        filenames: Single filename string or list of filename strings
        reference_file: Path to the file containing valid filenames
        directory_structure_file: Path to the file containing directory structure
        
    Returns:
        List of dictionaries, each containing 'new_filename' and 'parent_directory'
    """
    converter = FilenameConverter(reference_file, directory_structure_file)
    return converter.convert_filenames(filenames)


if __name__ == "__main__":
    # Example usage
    converter = FilenameConverter()
    
    # Test with example filenames
    test_filenames = [
        "cuishanlantian_res_uf_RS10_4",
        "changyinguanlinfu_res_uf_RS10_0",
        "invalid_filename_format",
        "nonexistent_file_name_test_99"
    ]
    
    print("Testing filename conversion:")
    print("=" * 50)
    
    results = converter.convert_filenames(test_filenames)
    
    for result in results:
        print(f"New filename: {result['new_filename']}")
        print(f"Parent directory: {result['parent_directory']}")
        print("-" * 30)
    
    print(f"\nProcessed {len(results)} out of {len(test_filenames)} filenames successfully.")

'''
# Import the converter
from tools.dataset_converters.filename_converter import FilenameConverter, convert_filename

# Method 1: Using the class
converter = FilenameConverter()
result = converter.convert_single_filename("cuishanlantian_res_uf_RS10_4")
# Returns: {'new_filename': '004_cuishanlantian_res-uf_RS10', 'parent_directory': 'RS10_Batch_02'}

# Method 2: Using the convenience function
results = convert_filename(["cuishanlantian_res_uf_RS10_4", "changyinguanlinfu_res_uf_RS10_0"])
# Returns list of dictionaries with new_filename and parent_directory

# Method 3: Batch processing
filenames = ["file1", "file2", "file3"]
results = converter.convert_filenames(filenames)
'''
