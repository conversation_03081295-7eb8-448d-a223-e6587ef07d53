#!/bin/bash

# 批量处理脚本 - 根据train.txt处理RS10数据集

echo "🚀 RS10数据集批量密度图生成"
echo "=================================="

# 默认参数
DATA_ROOT="/home/<USER>/01_3D-FAVP/handheld_scanner_Data/RS10/"
FILELIST="/home/<USER>/data/RS10_data/00_dataset_spilt/filelists/train.txt"
OUTPUT_DIR="output/0730"
IMAGE_SIZE="256x256"

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        --data-root)
            DATA_ROOT="$2"
            shift 2
            ;;
        --filelist)
            FILELIST="$2"
            shift 2
            ;;
        --output)
            OUTPUT_DIR="$2"
            shift 2
            ;;
        --size)
            IMAGE_SIZE="$2"
            shift 2
            ;;
        -h|--help)
            echo "用法: $0 [选项]"
            echo ""
            echo "选项:"
            echo "  --data-root PATH    数据根目录 (默认: $DATA_ROOT)"
            echo "  --filelist PATH     文件列表路径 (默认: $FILELIST)"
            echo "  --output PATH       输出目录 (默认: $OUTPUT_DIR)"
            echo "  --size WxH          图像尺寸 (默认: $IMAGE_SIZE)"
            echo "  -h, --help          显示帮助信息"
            echo ""
            echo "示例:"
            echo "  $0  # 使用默认参数"
            echo "  $0 --size 512x512  # 生成512x512密度图"
            echo "  $0 --output output/custom --size 1024x1024"
            exit 0
            ;;
        *)
            echo "未知参数: $1"
            echo "使用 -h 或 --help 查看帮助"
            exit 1
            ;;
    esac
done

echo "📋 处理参数:"
echo "  数据根目录: $DATA_ROOT"
echo "  文件列表: $FILELIST"
echo "  输出目录: $OUTPUT_DIR"
echo "  图像尺寸: $IMAGE_SIZE"
echo ""

# 检查必要文件和目录
if [ ! -d "$DATA_ROOT" ]; then
    echo "❌ 数据根目录不存在: $DATA_ROOT"
    exit 1
fi

if [ ! -f "$FILELIST" ]; then
    echo "❌ 文件列表不存在: $FILELIST"
    exit 1
fi

if [ ! -f "tools/debug/build/pointcloud_processor" ]; then
    echo "❌ C++处理器未构建，请先运行: cd tools/debug && ./build.sh"
    exit 1
fi

# 创建输出目录
mkdir -p "$OUTPUT_DIR"

# 显示要处理的场景数量
SCENE_COUNT=$(wc -l < "$FILELIST")
echo "📊 将处理 $SCENE_COUNT 个场景"
echo ""

# 运行C++批量处理器
echo "🚀 开始批量处理..."
echo ""

./tools/debug/build/pointcloud_processor \
    --batch \
    --data-root "$DATA_ROOT" \
    --filelist "$FILELIST" \
    --output "$OUTPUT_DIR" \
    --size "$IMAGE_SIZE"

RESULT=$?

echo ""
if [ $RESULT -eq 0 ]; then
    echo "🎉 批量处理完成！"
    echo "📁 输出目录: $OUTPUT_DIR"
    echo "📊 生成的文件:"
    ls -la "$OUTPUT_DIR"/*.png 2>/dev/null | wc -l | xargs echo "  密度图文件数量:"
    echo ""
    echo "💡 查看结果:"
    echo "  ls $OUTPUT_DIR/*.png"
else
    echo "❌ 批量处理失败！"
    echo "🔧 请检查:"
    echo "  1. 数据路径是否正确"
    echo "  2. 文件列表是否存在"
    echo "  3. C++处理器是否正确构建"
    exit 1
fi
