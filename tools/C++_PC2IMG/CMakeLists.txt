cmake_minimum_required(VERSION 3.10)
project(PointCloudDensityProcessor)

# Set C++ standard
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Find required packages
find_package(OpenCV REQUIRED)
find_package(PCL REQUIRED)

# LAS file support is built-in (SimpleLASReader class)

# Include directories
include_directories(${OpenCV_INCLUDE_DIRS})
include_directories(${PCL_INCLUDE_DIRS})

# Link directories
link_directories(${PCL_LIBRARY_DIRS})

# Add definitions
add_definitions(${PCL_DEFINITIONS})

# Create executable
add_executable(pointcloud_processor pointcloud_to_density_processor.cpp)

# Link libraries
target_link_libraries(pointcloud_processor ${OpenCV_LIBS} ${PCL_LIBRARIES})

# Print configuration info
message(STATUS "OpenCV version: ${OpenCV_VERSION}")
message(STATUS "PCL version: ${PCL_VERSION}")
message(STATUS "LAS file support: ENABLED (built-in SimpleLASReader)")
