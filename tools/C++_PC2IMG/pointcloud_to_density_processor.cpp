/**
 * Complete C++ implementation for point cloud to density image projection
 * Replicates the exact preprocessing pipeline from Python code
 * 
 * Dependencies:
 * - OpenCV 4.x
 * - PCL (Point Cloud Library) 1.8+
 *
 * Compile with:
 * g++ -std=c++17 pointcloud_to_density_processor.cpp -o processor \
 *     `pkg-config --cflags --libs opencv4` \
 *     -lpcl_common -lpcl_io
 */

#include <iostream>
#include <fstream>
#include <string>
#include <vector>
#include <map>
#include <set>
#include <cmath>
#include <algorithm>
#include <memory>
#include <filesystem>
#include <cstdlib>
#include <regex>
#include <sstream>
#include <cstring>

// OpenCV for image processing and rotation
#include <opencv2/opencv.hpp>

// PCL for point cloud handling
#include <pcl/point_cloud.h>
#include <pcl/point_types.h>
#include <pcl/io/pcd_io.h>

// 注意：LAS文件读取使用内置的SimpleLASReader类，无需外部依赖

#ifndef M_PI
#define M_PI 3.14159265358979323846
#endif

struct MappingParams {
    std::string scene_name;
    float rotation_angle;        // 已计算好的旋转角度（弧度）
    float roi_min_x, roi_min_y;  // ROI最小坐标
    float roi_max_x, roi_max_y;  // ROI最大坐标
    float pixel_resolution;      // 米/像素
    float roi_width, roi_height; // ROI尺寸（米）
    int image_width, image_height; // 图像尺寸（像素）
    std::vector<float> pointcloud_roi; // 原始pointcloud_roi [min_x, min_y, min_z, max_x, max_y, max_z]
};

/**
 * 简单的LAS文件读取器 - 纯C++实现
 * 支持LAS 1.2格式的基本点数据读取
 */
class SimpleLASReader {
private:
    struct LASHeader {
        char file_signature[4];
        uint16_t file_source_id;
        uint16_t global_encoding;
        uint32_t guid_data_1;
        uint16_t guid_data_2;
        uint16_t guid_data_3;
        uint8_t guid_data_4[8];
        uint8_t version_major;
        uint8_t version_minor;
        char system_identifier[32];
        char generating_software[32];
        uint16_t creation_day;
        uint16_t creation_year;
        uint16_t header_size;
        uint32_t point_data_offset;
        uint32_t number_of_variable_length_records;
        uint8_t point_data_format;
        uint16_t point_data_record_length;
        uint32_t number_of_point_records;
        uint32_t number_of_points_by_return[5];
        double x_scale_factor;
        double y_scale_factor;
        double z_scale_factor;
        double x_offset;
        double y_offset;
        double z_offset;
        double max_x;
        double min_x;
        double max_y;
        double min_y;
        double max_z;
        double min_z;
    } __attribute__((packed));

    struct LASPoint {
        int32_t x;
        int32_t y;
        int32_t z;
        uint16_t intensity;
        uint8_t return_info;
        uint8_t classification;
        int8_t scan_angle_rank;
        uint8_t user_data;
        uint16_t point_source_id;
    } __attribute__((packed));

public:
    static pcl::PointCloud<pcl::PointXYZ>::Ptr readLASFile(const std::string& filename) {
        auto cloud = pcl::PointCloud<pcl::PointXYZ>::Ptr(new pcl::PointCloud<pcl::PointXYZ>);

        std::ifstream file(filename, std::ios::binary);
        if (!file.is_open()) {
            throw std::runtime_error("无法打开LAS文件: " + filename);
        }

        // 读取LAS头部
        LASHeader header;
        file.read(reinterpret_cast<char*>(&header), sizeof(LASHeader));

        if (file.gcount() != sizeof(LASHeader)) {
            throw std::runtime_error("LAS文件头部读取失败");
        }

        // 验证文件签名
        if (strncmp(header.file_signature, "LASF", 4) != 0) {
            throw std::runtime_error("不是有效的LAS文件格式");
        }

        std::cout << "  LAS文件信息:" << std::endl;
        std::cout << "    版本: " << (int)header.version_major << "." << (int)header.version_minor << std::endl;
        std::cout << "    点数: " << header.number_of_point_records << std::endl;
        std::cout << "    点格式: " << (int)header.point_data_format << std::endl;
        std::cout << "    范围: X[" << header.min_x << ", " << header.max_x << "]" << std::endl;
        std::cout << "          Y[" << header.min_y << ", " << header.max_y << "]" << std::endl;
        std::cout << "          Z[" << header.min_z << ", " << header.max_z << "]" << std::endl;

        // 跳转到点数据开始位置
        file.seekg(header.point_data_offset);

        // 读取点数据
        cloud->points.reserve(header.number_of_point_records);

        for (uint32_t i = 0; i < header.number_of_point_records; ++i) {
            LASPoint las_point;
            file.read(reinterpret_cast<char*>(&las_point), sizeof(LASPoint));

            if (file.gcount() != sizeof(LASPoint)) {
                std::cout << "  ⚠️  警告: 在第 " << i << " 个点处读取不完整，停止读取" << std::endl;
                break;
            }

            // 转换为实际坐标
            pcl::PointXYZ point;
            point.x = static_cast<float>(las_point.x * header.x_scale_factor + header.x_offset);
            point.y = static_cast<float>(las_point.y * header.y_scale_factor + header.y_offset);
            point.z = static_cast<float>(las_point.z * header.z_scale_factor + header.z_offset);

            cloud->points.push_back(point);
        }

        cloud->width = cloud->points.size();
        cloud->height = 1;
        cloud->is_dense = false;

        std::cout << "  ✅ 成功读取 " << cloud->points.size() << " 个点" << std::endl;

        return cloud;
    }
};

/**
 * 智能文件查找器
 * 扫描目录结构并建立文件索引
 */
class SmartFileFinder {
private:
    std::string base_directory;

public:
    std::map<std::string, std::string> mapping_file_index; // filename -> full_path
    SmartFileFinder(const std::string& base_dir = "/home/<USER>/data/RS10_data/04_to_yuhui/")
        : base_directory(base_dir) {
        scanDirectory();
    }

    void scanDirectory() {
        std::cout << "🔍 扫描目录结构: " << base_directory << std::endl;

        if (!std::filesystem::exists(base_directory)) {
            std::cout << "⚠️  基础目录不存在: " << base_directory << std::endl;
            std::cout << "💡 请检查目录路径是否正确，或者mapping文件是否已生成" << std::endl;
            return;
        }

        try {
            int total_files = 0;
            int mapping_files = 0;

            // 递归扫描所有文件
            for (const auto& entry : std::filesystem::recursive_directory_iterator(base_directory)) {
                if (entry.is_regular_file()) {
                    total_files++;
                    std::string filename = entry.path().filename().string();

                    // 查找所有以_mapping.txt结尾的文件
                    if (filename.size() > 12 && filename.substr(filename.size() - 12) == "_mapping.txt") {
                        // 提取基础文件名（去掉_mapping.txt后缀）
                        std::string base_name = filename.substr(0, filename.size() - 12);
                        mapping_file_index[base_name] = entry.path().string();
                        mapping_files++;

                        if (mapping_files <= 5) { // 只显示前5个
                            std::cout << "  找到mapping文件: " << base_name << " -> " << entry.path().string() << std::endl;
                        }
                    }
                }
            }

            std::cout << "✅ 扫描完成，总文件数: " << total_files << "，找到 " << mapping_files << " 个mapping文件" << std::endl;

            if (mapping_files == 0) {
                std::cout << "⚠️  没有找到任何mapping文件！" << std::endl;
                std::cout << "💡 请确保已运行Python脚本生成mapping文件:" << std::endl;
                std::cout << "    python tools/dataset_converters/simplified_projection_processor.py --batch ..." << std::endl;
            } else if (mapping_files > 5) {
                std::cout << "    ... 还有 " << (mapping_files - 5) << " 个mapping文件" << std::endl;
            }

        } catch (const std::exception& e) {
            std::cout << "❌ 扫描目录时出错: " << e.what() << std::endl;
        }
    }

    std::string findMappingFile(const std::string& scene_name) {
        auto it = mapping_file_index.find(scene_name);
        if (it != mapping_file_index.end()) {
            return it->second;
        }
        return "";
    }

    void listAvailableFiles() {
        std::cout << "📋 可用的mapping文件:" << std::endl;
        for (const auto& pair : mapping_file_index) {
            std::cout << "  " << pair.first << " -> " << pair.second << std::endl;
        }
    }
};

/**
 * 文件名转换器 - C++版本
 * 复现Python的filename_converter.py功能
 */
class FilenameConverter {
private:
    std::set<std::string> valid_filenames;
    std::map<std::string, std::string> directory_mapping;

public:
    FilenameConverter(const std::string& reference_file = "/home/<USER>/repos/Mask2Former_v2/tools/debug/filelists/pred_filelists.txt",
                     const std::string& directory_structure_file = "/home/<USER>/repos/Mask2Former_v2/tools/debug/filelists/02_to_label_file_structure.txt") {
        loadReferenceFiles(reference_file, directory_structure_file);
    }

    void loadReferenceFiles(const std::string& reference_file, const std::string& directory_structure_file) {
        // 加载有效文件名列表
        std::ifstream ref_file(reference_file);
        if (ref_file.is_open()) {
            std::string line;
            while (std::getline(ref_file, line)) {
                line.erase(0, line.find_first_not_of(" \t\r\n"));
                line.erase(line.find_last_not_of(" \t\r\n") + 1);
                if (!line.empty()) {
                    valid_filenames.insert(line);
                }
            }
            std::cout << "✅ 加载了 " << valid_filenames.size() << " 个有效文件名" << std::endl;
        } else {
            std::cout << "⚠️  警告: 无法打开参考文件 " << reference_file << std::endl;
        }

        // 加载目录结构映射
        std::ifstream dir_file(directory_structure_file);
        if (dir_file.is_open()) {
            parseDirectoryStructure(dir_file);
            std::cout << "✅ 加载了 " << directory_mapping.size() << " 个目录映射" << std::endl;
        } else {
            std::cout << "⚠️  警告: 无法打开目录结构文件 " << directory_structure_file << std::endl;
        }
    }

    void parseDirectoryStructure(std::ifstream& file) {
        std::string line;
        std::string current_batch;

        while (std::getline(file, line)) {
            // 匹配批次目录行 (e.g., "├── RS10_Batch_02")
            std::regex batch_regex(R"(├── (RS10_Batch_\d+))");
            std::smatch batch_match;
            if (std::regex_search(line, batch_match, batch_regex)) {
                current_batch = batch_match[1].str();
                continue;
            }

            // 匹配文件名行 (e.g., "│   ├── 004_cuishanlantian_res-uf_RS10")
            std::regex filename_regex(R"(│\s+├── (.+))");
            std::smatch filename_match;
            if (std::regex_search(line, filename_match, filename_regex) && !current_batch.empty()) {
                std::string filename = filename_match[1].str();
                filename.erase(0, filename.find_first_not_of(" \t\r\n"));
                filename.erase(filename.find_last_not_of(" \t\r\n") + 1);
                directory_mapping[filename] = current_batch;
                continue;
            }

            // 匹配最后一个文件名 (e.g., "│   └── 019_cuishanlantian_res-ff_RS10")
            std::regex last_filename_regex(R"(│\s+└── (.+))");
            std::smatch last_filename_match;
            if (std::regex_search(line, last_filename_match, last_filename_regex) && !current_batch.empty()) {
                std::string filename = last_filename_match[1].str();
                filename.erase(0, filename.find_first_not_of(" \t\r\n"));
                filename.erase(filename.find_last_not_of(" \t\r\n") + 1);
                directory_mapping[filename] = current_batch;
            }
        }
    }

    struct FilenameComponents {
        std::string community_name;
        std::string property;
        std::string device_name;
        std::string house_code;
        bool valid = false;
    };

    FilenameComponents parseOldFilename(const std::string& filename) {
        FilenameComponents components;

        // 分割文件名 (去掉扩展名)
        std::string base_filename = filename;
        size_t dot_pos = base_filename.find_last_of('.');
        if (dot_pos != std::string::npos) {
            base_filename = base_filename.substr(0, dot_pos);
        }

        // 按下划线分割
        std::vector<std::string> parts;
        std::stringstream ss(base_filename);
        std::string part;
        while (std::getline(ss, part, '_')) {
            parts.push_back(part);
        }

        if (parts.size() < 4) {
            return components;
        }

        // 最后一部分应该是house_code (数字)
        std::string house_code = parts.back();
        if (!std::all_of(house_code.begin(), house_code.end(), ::isdigit)) {
            return components;
        }

        // 倒数第二部分应该是device_name (e.g., RS10)
        std::string device_name = parts[parts.size() - 2];

        // 倒数第三部分应该是property (e.g., uf, ff)
        std::string property_part = parts[parts.size() - 3];

        // 剩余部分应该是community_name + "res"
        std::vector<std::string> remaining_parts(parts.begin(), parts.end() - 3);
        if (remaining_parts.size() < 2 || remaining_parts.back() != "res") {
            return components;
        }

        // Community name是除了最后的"res"之外的所有部分
        remaining_parts.pop_back(); // 移除"res"
        std::string community_name;
        for (size_t i = 0; i < remaining_parts.size(); ++i) {
            if (i > 0) community_name += "_";
            community_name += remaining_parts[i];
        }

        // Property是"res_" + property_part
        std::string property = "res_" + property_part;

        components.community_name = community_name;
        components.property = property;
        components.device_name = device_name;
        components.house_code = house_code;
        components.valid = true;

        return components;
    }

    std::string convertToNewFormat(const FilenameComponents& components) {
        if (!components.valid) {
            return "";
        }

        // 零填充house_code到3位
        std::string house_code_padded = components.house_code;
        while (house_code_padded.length() < 3) {
            house_code_padded = "0" + house_code_padded;
        }

        // 将property中的下划线转换为连字符
        std::string property_formatted = components.property;
        std::replace(property_formatted.begin(), property_formatted.end(), '_', '-');

        // 构建新文件名: {house_code_3digits}_{community_name}_{property-with-hyphens}_{device_name}
        return house_code_padded + "_" + components.community_name + "_" + property_formatted + "_" + components.device_name;
    }

    struct ConversionResult {
        std::string new_filename;
        std::string parent_directory;
        bool success = false;
    };

    ConversionResult convertSingleFilename(const std::string& filename) {
        ConversionResult result;

        // 验证文件名是否在参考列表中
        if (!valid_filenames.empty() && valid_filenames.find(filename) == valid_filenames.end()) {
            std::cout << "⚠️  警告: 文件名 '" << filename << "' 不在参考文件中，跳过" << std::endl;
            return result;
        }

        // 解析旧文件名
        FilenameComponents components = parseOldFilename(filename);
        if (!components.valid) {
            std::cout << "⚠️  警告: 无法解析文件名 '" << filename << "'，格式无效" << std::endl;
            return result;
        }

        // 转换为新格式
        std::string new_filename = convertToNewFormat(components);
        if (new_filename.empty()) {
            std::cout << "⚠️  警告: 转换文件名失败 '" << filename << "'" << std::endl;
            return result;
        }

        // 获取父目录
        std::string parent_directory = "Unknown";
        auto it = directory_mapping.find(new_filename);
        if (it != directory_mapping.end()) {
            parent_directory = it->second;
        } else {
            std::cout << "⚠️  警告: 未找到目录映射 '" << filename << " -> " << new_filename << "'" << std::endl;
        }

        result.new_filename = new_filename;
        result.parent_directory = parent_directory;
        result.success = true;

        return result;
    }
};

class PointCloudDensityProcessor {
private:
    MappingParams params;
    
public:
    PointCloudDensityProcessor(const MappingParams& mapping_params) 
        : params(mapping_params) {}
    
    /**
     * 加载mapping.txt参数文件
     * 复现simplified_projection_processor.py的参数读取逻辑
     */
    static MappingParams loadMapping(const std::string& mapping_file) {
        MappingParams params;
        std::ifstream file(mapping_file);
        
        if (!file.is_open()) {
            throw std::runtime_error("无法打开mapping文件: " + mapping_file);
        }
        
        std::getline(file, params.scene_name);
        file >> params.rotation_angle;
        file >> params.roi_min_x >> params.roi_min_y;
        file >> params.roi_max_x >> params.roi_max_y;
        file >> params.pixel_resolution;
        file >> params.roi_width >> params.roi_height;
        file >> params.image_width >> params.image_height;
        
        // 读取原始pointcloud_roi（6个值）
        params.pointcloud_roi.resize(6);
        for (int i = 0; i < 6; i++) {
            file >> params.pointcloud_roi[i];
        }
        
        std::cout << "✅ 加载mapping参数完成:" << std::endl;
        std::cout << "  场景: " << params.scene_name << std::endl;
        std::cout << "  图像尺寸: " << params.image_width << "×" << params.image_height << std::endl;
        std::cout << "  像素分辨率: " << params.pixel_resolution << " 米/像素" << std::endl;
        std::cout << "  旋转角度: " << params.rotation_angle << " 弧度 (" 
                  << params.rotation_angle * 180.0 / M_PI << "°)" << std::endl;
        std::cout << "  pointcloud_roi: [" << params.pointcloud_roi[0] << ", " 
                  << params.pointcloud_roi[1] << ", " << params.pointcloud_roi[2] << "] -> ["
                  << params.pointcloud_roi[3] << ", " << params.pointcloud_roi[4] << ", " 
                  << params.pointcloud_roi[5] << "]" << std::endl;
        
        return params;
    }
    
    /**
     * 从点云文件加载数据
     * 支持多种格式：LAS (内置读取器), PCD, PLY, XYZ文本格式
     * 复现generate_coco_hc_0722.py第244行的laspy.read()逻辑
     */
    pcl::PointCloud<pcl::PointXYZ>::Ptr loadPointCloudFile(const std::string& point_cloud_file) {
        auto cloud = std::make_shared<pcl::PointCloud<pcl::PointXYZ>>();

        std::cout << "📁 加载点云文件: " << point_cloud_file << std::endl;

        // 获取文件扩展名
        std::string extension = point_cloud_file.substr(point_cloud_file.find_last_of(".") + 1);
        std::transform(extension.begin(), extension.end(), extension.begin(), ::tolower);

        if (extension == "las" || extension == "laz") {
            // 使用纯C++的LAS读取器
            std::cout << "  🔄 使用纯C++读取LAS文件..." << std::endl;

            if (extension == "laz") {
                throw std::runtime_error("LAZ压缩格式暂不支持，请使用未压缩的LAS文件");
            }

            try {
                cloud = SimpleLASReader::readLASFile(point_cloud_file);
            } catch (const std::exception& e) {
                throw std::runtime_error("LAS文件读取失败: " + std::string(e.what()));
            }
        } else if (extension == "pcd") {
            // 使用PCL加载PCD文件
            if (pcl::io::loadPCDFile<pcl::PointXYZ>(point_cloud_file, *cloud) == -1) {
                throw std::runtime_error("无法加载PCD文件: " + point_cloud_file);
            }
        } else if (extension == "ply") {
            // PLY文件需要特殊处理，暂时不支持，建议转换为PCD或TXT格式
            throw std::runtime_error("PLY文件暂不支持，请转换为PCD或TXT格式: " + point_cloud_file);
        } else if (extension == "txt" || extension == "xyz") {
            // 加载文本格式点云文件 (X Y Z 每行一个点)
            std::ifstream file(point_cloud_file);
            if (!file.is_open()) {
                throw std::runtime_error("无法打开文件: " + point_cloud_file);
            }

            std::string line;
            bool has_header = false;

            // 检查是否有头部行
            if (std::getline(file, line)) {
                std::istringstream iss(line);
                float x, y, z;
                if (!(iss >> x >> y >> z)) {
                    // 第一行不是数字，可能是头部
                    has_header = true;
                } else {
                    // 第一行是数字，添加这个点
                    pcl::PointXYZ point(x, y, z);
                    cloud->points.push_back(point);
                }
            }

            // 读取剩余的点
            while (std::getline(file, line)) {
                std::istringstream iss(line);
                float x, y, z;
                if (iss >> x >> y >> z) {
                    pcl::PointXYZ point(x, y, z);
                    cloud->points.push_back(point);
                }
            }

            cloud->width = cloud->points.size();
            cloud->height = 1;
            cloud->is_dense = false;

        } else {
            throw std::runtime_error("不支持的文件格式: " + extension +
                                   " (支持: las, pcd, txt, xyz)");
        }

        std::cout << "✅ 成功加载 " << cloud->points.size() << " 个点" << std::endl;

        if (cloud->points.empty()) {
            throw std::runtime_error("点云文件为空或格式错误");
        }

        return cloud;
    }
    
    /**
     * 步骤1：点云过滤
     * 完全复现generate_coco_hc_0722.py第247-250行的过滤逻辑
     */
    std::vector<cv::Point2f> filterPointCloud(const pcl::PointCloud<pcl::PointXYZ>::Ptr& cloud) {
        std::vector<cv::Point2f> filtered_points;
        
        // 从pointcloud_roi提取边界（从floorplan.json读取的原始boundingBox）
        float min_x = params.pointcloud_roi[0], min_y = params.pointcloud_roi[1], min_z = params.pointcloud_roi[2];
        float max_x = params.pointcloud_roi[3], max_y = params.pointcloud_roi[4], max_z = params.pointcloud_roi[5];
        
        std::cout << "🔍 点云过滤参数:" << std::endl;
        std::cout << "  X范围: " << min_x << " < x < " << max_x << std::endl;
        std::cout << "  Y范围: " << min_y << " < y < " << max_y << std::endl;
        std::cout << "  Z范围: " << min_z + 0.25f << " < z < " << max_z - 0.25f << " (±0.25m)" << std::endl;
        
        for (const auto& point : cloud->points) {
            // 复现Python中的过滤条件：
            // (las.z > pointcloud_roi[2] + 0.25) & (las.z < pointcloud_roi[5] - 0.25)
            // & (las.x > pointcloud_roi[0]) & (las.x < pointcloud_roi[3])
            // & (las.y > pointcloud_roi[1]) & (las.y < pointcloud_roi[4])
            if (point.z > (min_z + 0.25f) && point.z < (max_z - 0.25f) &&
                point.x > min_x && point.x < max_x &&
                point.y > min_y && point.y < max_y) {
                filtered_points.emplace_back(point.x, point.y);
            }
        }
        
        std::cout << "✅ 过滤后点云数量: " << filtered_points.size() 
                  << " (原始: " << cloud->points.size() << ")" << std::endl;
        return filtered_points;
    }
    
    /**
     * 步骤2：点云旋转
     * 完全复现generate_coco_hc_0722.py第252-258行的旋转逻辑
     */
    std::vector<cv::Point2f> rotatePointCloud(const std::vector<cv::Point2f>& points) {
        std::vector<cv::Point2f> rotated_points;
        
        // 复现Python中的旋转逻辑：
        // rotation_matrix = cv2.getRotationMatrix2D((0, 0), np.degrees(slope_angle), 1)
        // rotated_point_cloud = np.dot(np.column_stack((filtered_point_cloud, np.ones(...))), rotation_matrix.T)
        
        // 注意：Python中使用degrees，这里需要转换
        float angle_degrees = params.rotation_angle * 180.0f / M_PI;
        cv::Mat rotation_matrix = cv::getRotationMatrix2D(cv::Point2f(0, 0), angle_degrees, 1.0);
        
        std::cout << "🔄 应用旋转变换: " << angle_degrees << "°" << std::endl;
        
        for (const auto& point : points) {
            // 构建齐次坐标 [x, y, 1] - 使用double类型匹配rotation_matrix
            cv::Mat point_homogeneous = (cv::Mat_<double>(3, 1) << point.x, point.y, 1.0);

            // 应用旋转变换
            cv::Mat rotated = rotation_matrix * point_homogeneous;

            rotated_points.emplace_back(static_cast<float>(rotated.at<double>(0, 0)),
                                       static_cast<float>(rotated.at<double>(1, 0)));
        }
        
        std::cout << "✅ 旋转后点云数量: " << rotated_points.size() << std::endl;
        return rotated_points;
    }

    /**
     * 步骤3：生成密度图
     * 完全复现hc_utils.py的generate_density函数（第62-116行）
     */
    cv::Mat generateDensityMap(const std::vector<cv::Point2f>& points) {
        // 复现Python中的逻辑：
        // coordinates = np.round((ps[:, :2] - min_coords) / (max_coords - min_coords) * image_res)

        // 注意：Python使用(height, width)，OpenCV使用(rows, cols) = (height, width)
        cv::Mat density = cv::Mat::zeros(params.image_height, params.image_width, CV_32F);

        // 使用ROI边界进行归一化（复现Python中的max_coords, min_coords）
        cv::Point2f min_coords(params.roi_min_x, params.roi_min_y);
        cv::Point2f max_coords(params.roi_max_x, params.roi_max_y);
        cv::Point2f coords_range = max_coords - min_coords;

        std::cout << "📐 密度图生成参数:" << std::endl;
        std::cout << "  图像尺寸: " << params.image_width << "×" << params.image_height << std::endl;
        std::cout << "  坐标范围: [" << min_coords.x << ", " << min_coords.y << "] -> ["
                  << max_coords.x << ", " << max_coords.y << "]" << std::endl;

        // 用于统计每个像素的点数（复现np.unique(..., return_counts=True)）
        std::map<std::pair<int, int>, int> pixel_counts;

        for (const auto& point : points) {
            // 归一化到[0,1]范围（复现Python逻辑）
            float norm_x = (point.x - min_coords.x) / coords_range.x;
            float norm_y = (point.y - min_coords.y) / coords_range.y;

            // 转换为像素坐标并四舍五入（复现np.round）
            int pixel_x = static_cast<int>(std::round(norm_x * params.image_width));
            int pixel_y = static_cast<int>(std::round(norm_y * params.image_height));

            // 边界检查（复现np.minimum(np.maximum(...))）
            pixel_x = std::max(0, std::min(pixel_x, params.image_width - 1));
            pixel_y = std::max(0, std::min(pixel_y, params.image_height - 1));

            // 统计每个像素的点数
            pixel_counts[{pixel_x, pixel_y}]++;
        }

        // 将计数填入密度图（复现density[unique_coordinates[:, 1], unique_coordinates[:, 0]] = counts）
        float max_count = 0;
        for (const auto& pair : pixel_counts) {
            int x = pair.first.first;
            int y = pair.first.second;
            int count = pair.second;
            density.at<float>(y, x) = static_cast<float>(count);  // 注意：(y, x)顺序
            max_count = std::max(max_count, static_cast<float>(count));
        }

        // 归一化到[0,1]（复现density = density / np.max(density)）
        if (max_count > 0) {
            density /= max_count;
        }

        // 应用非线性映射（复现Python中的mask操作）
        // mask_mid = (density >= 0.001) & (density <= 0.04)
        // mask_high = density > 0.04
        // density[mask_mid] *= 25
        // density[mask_high] = 1.0
        for (int y = 0; y < params.image_height; y++) {
            for (int x = 0; x < params.image_width; x++) {
                float& val = density.at<float>(y, x);
                if (val >= 0.001f && val <= 0.04f) {
                    val *= 25.0f;
                } else if (val > 0.04f) {
                    val = 1.0f;
                }
            }
        }

        std::cout << "✅ 生成密度图完成: " << params.image_width << "×" << params.image_height
                  << " (非零像素: " << pixel_counts.size() << ")" << std::endl;
        return density;
    }

    /**
     * 保存密度图为8位PNG
     * 复现common_utils.py的export_density函数（第68-78行）
     */
    void saveDensityMap(const cv::Mat& density_map, const std::string& output_path) {
        // 复现Python中的逻辑：
        // density_uint8 = (density_map * 255).astype(np.uint8)
        cv::Mat density_uint8;
        density_map.convertTo(density_uint8, CV_8U, 255.0);

        if (cv::imwrite(output_path, density_uint8)) {
            std::cout << "✅ 密度图已保存: " << output_path << std::endl;
        } else {
            throw std::runtime_error("保存密度图失败: " + output_path);
        }
    }

    /**
     * 批量处理多个LAS文件（复现generate_coco_hc_0722.py第237-262行的逻辑）
     * 将多个LAS文件的点云合并后生成一个密度图
     */
    cv::Mat processBatchPointClouds(const std::vector<std::string>& point_cloud_files) {
        std::cout << "🚀 开始批量点云处理流程" << std::endl;
        std::cout << "📁 待处理文件数量: " << point_cloud_files.size() << std::endl;

        // 合并所有点云（复现Python中的all_points逻辑）
        std::vector<cv::Point2f> all_points;

        for (const auto& file : point_cloud_files) {
            std::cout << "📂 处理文件: " << file << std::endl;

            try {
                // 加载点云
                auto cloud = loadPointCloudFile(file);

                // 步骤1：过滤点云
                auto filtered_points = filterPointCloud(cloud);

                // 步骤2：旋转点云
                auto rotated_points = rotatePointCloud(filtered_points);

                // 合并到总点云中（复现Python中的np.vstack((all_points, rotated_point_cloud))）
                all_points.insert(all_points.end(), rotated_points.begin(), rotated_points.end());

                std::cout << "  ✅ 已合并 " << rotated_points.size() << " 个点，总计: " << all_points.size() << std::endl;

            } catch (const std::exception& e) {
                std::cerr << "  ❌ 处理文件失败: " << e.what() << std::endl;
                continue;
            }
        }

        if (all_points.empty()) {
            throw std::runtime_error("没有有效的点云数据");
        }

        std::cout << "📊 合并完成，总点数: " << all_points.size() << std::endl;

        // 步骤3：对合并后的点云生成密度图
        auto density_map = generateDensityMap(all_points);

        std::cout << "🎉 批量处理流程完成！" << std::endl;
        return density_map;
    }

    /**
     * 批量处理指定目录下的所有LAS文件
     * 复现generate_coco_hc_0722.py第228-262行的目录扫描和批量处理逻辑
     */
    cv::Mat processLASDirectory(const std::string& las_directory) {
        std::cout << "🔍 扫描LAS目录: " << las_directory << std::endl;

        // 扫描目录中的所有LAS文件
        std::vector<std::string> las_files;

        // 使用filesystem扫描目录（C++17）
        try {
            for (const auto& entry : std::filesystem::directory_iterator(las_directory)) {
                if (entry.is_regular_file()) {
                    std::string filename = entry.path().filename().string();
                    std::string extension = filename.substr(filename.find_last_of(".") + 1);
                    std::transform(extension.begin(), extension.end(), extension.begin(), ::tolower);

                    // 复现Python中的scene.endswith('.las')检查
                    if (extension == "las" || extension == "laz" ||
                        extension == "pcd" ||
                        extension == "txt" || extension == "xyz") {
                        las_files.push_back(entry.path().string());
                    }
                }
            }
        } catch (const std::exception& e) {
            throw std::runtime_error("扫描目录失败: " + std::string(e.what()));
        }

        if (las_files.empty()) {
            throw std::runtime_error("目录中没有找到支持的点云文件: " + las_directory);
        }

        // 排序文件名（保证处理顺序一致）
        std::sort(las_files.begin(), las_files.end());

        std::cout << "✅ 找到 " << las_files.size() << " 个点云文件" << std::endl;
        for (const auto& file : las_files) {
            std::cout << "  - " << std::filesystem::path(file).filename().string() << std::endl;
        }

        // 批量处理所有文件
        return processBatchPointClouds(las_files);
    }

    /**
     * 单个点云处理流程（保持向后兼容）
     */
    cv::Mat processPointCloud(const pcl::PointCloud<pcl::PointXYZ>::Ptr& cloud) {
        std::cout << "🚀 开始单个点云处理流程" << std::endl;

        // 步骤1：过滤点云
        auto filtered_points = filterPointCloud(cloud);

        // 步骤2：旋转点云
        auto rotated_points = rotatePointCloud(filtered_points);

        // 步骤3：生成密度图
        auto density_map = generateDensityMap(rotated_points);

        std::cout << "🎉 处理流程完成！" << std::endl;
        return density_map;
    }

    /**
     * 处理单个点云文件的完整流程（向后兼容）
     */
    cv::Mat processPointCloudFile(const std::string& point_cloud_file, const std::string& output_path = "") {
        // 加载点云
        auto cloud = loadPointCloudFile(point_cloud_file);

        // 处理点云
        auto density_map = processPointCloud(cloud);

        // 保存结果（如果指定了输出路径）
        if (!output_path.empty()) {
            saveDensityMap(density_map, output_path);
        }

        return density_map;
    }

    /**
     * 批量处理点云文件或目录（复现generate_coco_hc_0722.py的主要逻辑）
     */
    cv::Mat processBatchPointCloudFiles(const std::string& input_path, const std::string& output_path = "") {
        cv::Mat density_map;

        // 检查输入是文件还是目录
        if (std::filesystem::is_directory(input_path)) {
            // 目录模式：批量处理目录中的所有点云文件
            density_map = processLASDirectory(input_path);
        } else if (std::filesystem::is_regular_file(input_path)) {
            // 单文件模式：处理单个文件
            density_map = processPointCloudFile(input_path);
        } else {
            throw std::runtime_error("输入路径不存在或不是有效的文件/目录: " + input_path);
        }

        // 保存结果（如果指定了输出路径）
        if (!output_path.empty()) {
            saveDensityMap(density_map, output_path);
        }

        return density_map;
    }
};

/**
 * 简化的使用接口函数
 * 复现generate_coco_hc_0722.py和simplified_projection_processor.py的主要功能
 * 支持单文件和批量处理
 */
cv::Mat generateDensityFromPointCloud(const std::string& input_path,
                                     const std::string& mapping_file,
                                     const std::string& output_path = "") {
    // 1. 加载mapping参数
    MappingParams params = PointCloudDensityProcessor::loadMapping(mapping_file);

    // 2. 初始化处理器
    PointCloudDensityProcessor processor(params);

    // 3. 处理点云文件或目录（自动检测批量模式）
    cv::Mat density_map = processor.processBatchPointCloudFiles(input_path, output_path);

    std::cout << "🎯 处理完成！" << std::endl;
    std::cout << "  场景: " << params.scene_name << std::endl;
    std::cout << "  图像尺寸: " << params.image_width << "×" << params.image_height << std::endl;
    std::cout << "  像素分辨率: " << params.pixel_resolution << " 米/像素" << std::endl;

    return density_map;
}

/**
 * 读取文件列表（复现generate_coco_hc_0722.py的_read_list函数）
 * 支持selected_filelist.txt格式：RS10_Batch_02\004_cuishanlantian_res-uf_RS10
 */
std::set<std::string> readFileList(const std::string& file_path) {
    std::set<std::string> file_set;
    std::ifstream file(file_path);

    if (!file.is_open()) {
        std::cout << "⚠️  无法打开文件列表: " << file_path << std::endl;
        return file_set;
    }

    std::string line;
    while (std::getline(file, line)) {
        // 去除首尾空白字符
        line.erase(0, line.find_first_not_of(" \t\r\n"));
        line.erase(line.find_last_not_of(" \t\r\n") + 1);

        if (!line.empty() && line[0] != '#') {
            // 处理selected_filelist.txt格式：RS10_Batch_02\004_cuishanlantian_res-uf_RS10
            if (line.find('\\') != std::string::npos) {
                // 分割批次目录和文件名
                size_t backslash_pos = line.find('\\');
                std::string batch_dir = line.substr(0, backslash_pos);
                std::string filename = line.substr(backslash_pos + 1);

                // 只使用文件名部分
                file_set.insert(filename);
                std::cout << "  解析: " << line << " -> " << filename << std::endl;
            } else {
                // 普通格式，直接使用
                file_set.insert(line);
            }
        }
    }

    std::cout << "📋 读取文件列表: " << file_path << " (" << file_set.size() << " 个场景)" << std::endl;
    return file_set;
}

/**
 * 批量处理数据集（完全复现generate_coco_hc_0722.py的主要逻辑）
 */
int processBatchDataset(const std::string& data_root,
                       const std::string& filelist_path,
                       const std::string& output_dir,
                       int image_width = 256,
                       int image_height = 256) {

    std::cout << "🚀 批量数据集处理模式" << std::endl;
    std::cout << "=" << std::string(60, '=') << std::endl;
    std::cout << "📁 数据根目录: " << data_root << std::endl;
    std::cout << "📋 文件列表: " << filelist_path << std::endl;
    std::cout << "📂 输出目录: " << output_dir << std::endl;
    std::cout << "🖼️  图像尺寸: " << image_width << "×" << image_height << std::endl;
    std::cout << std::endl;

    // 初始化智能文件查找器
    std::cout << "🔧 初始化智能文件查找器..." << std::endl;
    SmartFileFinder file_finder;
    std::cout << std::endl;

    // 初始化文件名转换器
    std::cout << "🔧 初始化文件名转换器..." << std::endl;
    FilenameConverter filename_converter;
    std::cout << std::endl;

    // 读取要处理的场景列表
    auto scenes_to_process = readFileList(filelist_path);

    if (scenes_to_process.empty()) {
        std::cerr << "❌ 没有找到要处理的场景" << std::endl;
        return 1;
    }

    // 创建输出目录
    std::filesystem::create_directories(output_dir);

    int processed_count = 0;
    int success_count = 0;

    for (const auto& scene_name : scenes_to_process) {
        processed_count++;

        std::cout << std::endl;
        std::cout << "***** 处理场景 [" << processed_count << "/" << scenes_to_process.size() << "]: " << scene_name << std::endl;

        // 检查场景名称格式，决定是否需要转换
        std::string converted_scene_name = scene_name;
        std::string original_scene_name = scene_name;  // 用于floorplan.json路径
        std::string batch_directory = "Unknown";

        // 如果场景名称已经是新格式（以数字开头），需要反向查找原始名称
        if (std::regex_match(scene_name, std::regex(R"(\d{3}_\w+_res-\w+_RS10)"))) {
            std::cout << "✅ 场景名称已是新格式: " << scene_name << std::endl;
            converted_scene_name = scene_name;

            // 尝试反向查找原始场景名称（用于floorplan.json路径）
            // 从新格式推导原始格式：004_cuishanlantian_res-uf_RS10 -> cuishanlantian_res_uf_RS10_4
            std::regex new_format_regex(R"((\d{3})_(\w+)_res-(\w+)_RS10)");
            std::smatch matches;
            if (std::regex_match(scene_name, matches, new_format_regex)) {
                std::string house_code = matches[1].str();
                std::string community_name = matches[2].str();
                std::string property_type = matches[3].str();

                // 移除前导零
                int house_num = std::stoi(house_code);

                // 构建原始格式
                original_scene_name = community_name + "_res_" + property_type + "_RS10_" + std::to_string(house_num);
                std::cout << "🔄 反向推导原始场景名称: " << scene_name << " -> " << original_scene_name << std::endl;
            } else {
                std::cout << "⚠️  无法反向推导原始场景名称，使用新格式名称" << std::endl;
                original_scene_name = scene_name;
            }
        } else {
            // 旧格式，需要转换
            original_scene_name = scene_name;  // 已经是原始格式
            auto conversion_result = filename_converter.convertSingleFilename(scene_name);
            if (conversion_result.success) {
                converted_scene_name = conversion_result.new_filename;
                batch_directory = conversion_result.parent_directory;
                std::cout << "🔄 文件名转换: " << scene_name << " -> " << converted_scene_name << std::endl;
                std::cout << "📁 批次目录: " << batch_directory << std::endl;
            } else {
                std::cout << "⚠️  使用原始场景名称: " << scene_name << std::endl;
            }
        }

        try {
            // 构建场景路径 - 使用原始场景名称（用于floorplan.json）
            std::string scene_path = data_root + "/" + original_scene_name;
            std::cout << "📁 场景路径: " << scene_path << std::endl;
            std::string floorplan_path = scene_path + "/Annotations/floorplan.json";

            // 检查floorplan.json是否存在
            if (!std::filesystem::exists(floorplan_path)) {
                std::cout << "⚠️  跳过场景 " << scene_name << ": 缺少floorplan.json" << std::endl;
                continue;
            }

            // 查找LAS目录
            std::string las_dir_path;
            if (std::filesystem::exists(scene_path + "/LAS_Refined")) {
                las_dir_path = scene_path + "/LAS_Refined";
                std::cout << "📁 使用LAS_Refined目录" << std::endl;
            } else if (std::filesystem::exists(scene_path + "/LAS")) {
                las_dir_path = scene_path + "/LAS";
                std::cout << "📁 使用LAS目录" << std::endl;
            } else {
                std::cout << "⚠️  跳过场景 " << scene_name << ": 没有找到LAS目录" << std::endl;
                continue;
            }

            // 使用智能文件查找器查找mapping文件
            std::string mapping_file = file_finder.findMappingFile(converted_scene_name);

            if (mapping_file.empty()) {
                std::cout << "⚠️  未找到mapping文件，跳过场景: " << scene_name << std::endl;
                std::cout << "🔍 查找的文件名: " << converted_scene_name << "_mapping.txt" << std::endl;
                std::cout << "💡 请确保mapping文件存在于 /home/<USER>/data/RS10_data/04_to_yuhui/ 目录下" << std::endl;

                // 显示所有可用的mapping文件
                std::cout << "📋 所有可用的mapping文件:" << std::endl;
                for (const auto& pair : file_finder.mapping_file_index) {
                    std::cout << "    " << pair.first << "_mapping.txt -> " << pair.second << std::endl;
                }

                continue;
            }

            std::cout << "📋 找到mapping文件: " << mapping_file << std::endl;

            MappingParams params;
            try {
                // 从mapping.txt加载参数（按照文档格式）
                params = PointCloudDensityProcessor::loadMapping(mapping_file);

                // 覆盖图像尺寸参数（如果用户指定了不同的尺寸）
                if (params.image_width != image_width || params.image_height != image_height) {
                    std::cout << "⚙️  覆盖图像尺寸: " << params.image_width << "×" << params.image_height
                              << " -> " << image_width << "×" << image_height << std::endl;

                    // 重新计算像素分辨率
                    float roi_width = params.roi_max_x - params.roi_min_x;
                    float roi_height = params.roi_max_y - params.roi_min_y;
                    params.pixel_resolution = std::max(roi_width / image_width, roi_height / image_height);
                    params.roi_width = roi_width;
                    params.roi_height = roi_height;
                    params.image_width = image_width;
                    params.image_height = image_height;
                }

                std::cout << "✅ 从mapping.txt读取参数:" << std::endl;
                std::cout << "    场景名称: " << params.scene_name << std::endl;
                std::cout << "    ROI范围: [" << params.roi_min_x << ", " << params.roi_min_y
                          << "] -> [" << params.roi_max_x << ", " << params.roi_max_y << "]" << std::endl;
                std::cout << "    旋转角度: " << params.rotation_angle << " 弧度 ("
                          << params.rotation_angle * 180.0 / M_PI << "°)" << std::endl;
                std::cout << "    像素分辨率: " << params.pixel_resolution << " 米/像素" << std::endl;
                std::cout << "    pointcloud_roi: [" << params.pointcloud_roi[0] << ", " << params.pointcloud_roi[1]
                          << ", " << params.pointcloud_roi[2] << "] -> [" << params.pointcloud_roi[3]
                          << ", " << params.pointcloud_roi[4] << ", " << params.pointcloud_roi[5] << "]" << std::endl;

            } catch (const std::exception& e) {
                std::cout << "❌ 读取mapping文件失败: " << e.what() << std::endl;
                std::cout << "⚠️  将跳过场景: " << scene_name << std::endl;
                continue;
            }

            // 初始化处理器
            PointCloudDensityProcessor processor(params);

            // 批量处理LAS目录中的所有文件
            cv::Mat density_map = processor.processLASDirectory(las_dir_path);

            // 保存密度图（使用转换后的文件名）
            std::string output_file = output_dir + "/" + converted_scene_name + ".png";
            processor.saveDensityMap(density_map, output_file);

            success_count++;
            std::cout << "✅ 场景处理完成: " << scene_name << " -> " << converted_scene_name << ".png" << std::endl;

        } catch (const std::exception& e) {
            std::cerr << "❌ 处理场景失败 " << scene_name << ": " << e.what() << std::endl;
            continue;
        }
    }

    std::cout << std::endl;
    std::cout << "🎉 批量处理完成！" << std::endl;
    std::cout << "📊 处理统计: " << success_count << "/" << processed_count << " 个场景成功" << std::endl;

    return (success_count > 0) ? 0 : 1;
}

/**
 * 主函数 - 支持批量数据集处理
 */
int main(int argc, char* argv[]) {
    if (argc < 2) {
        std::cout << "点云到密度图处理器（无Python依赖）" << std::endl;
        std::cout << "=================================" << std::endl;
        std::cout << std::endl;
        std::cout << "用法:" << std::endl;
        std::cout << "  # 批量处理模式（复现generate_coco_hc_0722.py）" << std::endl;
        std::cout << "  " << argv[0] << " --batch --data-root <data_root> --filelist <train.txt> --output <output_dir> [--size WxH]" << std::endl;
        std::cout << std::endl;
        std::cout << "参数说明:" << std::endl;
        std::cout << "  --batch           批量数据集处理模式" << std::endl;
        std::cout << "  --data-root       数据根目录" << std::endl;
        std::cout << "  --filelist        文件列表路径（如train.txt）" << std::endl;
        std::cout << "  --output          输出目录" << std::endl;
        std::cout << "  --size WxH        图像尺寸（默认256x256）" << std::endl;
        std::cout << std::endl;
        std::cout << "内置默认参数:" << std::endl;
        std::cout << "  ROI范围: 30m×30m（-15到+15米）" << std::endl;
        std::cout << "  旋转角度: 0弧度（无旋转）" << std::endl;
        std::cout << "  高度过滤: 1.25-3.25米" << std::endl;
        std::cout << "  像素分辨率: 根据图像尺寸自动计算" << std::endl;
        std::cout << std::endl;
        std::cout << "示例:" << std::endl;
        std::cout << "  # 批量处理train.txt中的场景" << std::endl;
        std::cout << "  " << argv[0] << " --batch \\" << std::endl;
        std::cout << "    --data-root /home/<USER>/01_3D-FAVP/handheld_scanner_Data/RS10/ \\" << std::endl;
        std::cout << "    --filelist /home/<USER>/data/RS10_data/00_dataset_spilt/filelists/train.txt \\" << std::endl;
        std::cout << "    --output output/0730" << std::endl;
        std::cout << std::endl;
        std::cout << "  # 生成512x512密度图" << std::endl;
        std::cout << "  " << argv[0] << " --batch \\" << std::endl;
        std::cout << "    --data-root /home/<USER>/01_3D-FAVP/handheld_scanner_Data/RS10/ \\" << std::endl;
        std::cout << "    --filelist /home/<USER>/data/RS10_data/00_dataset_spilt/filelists/train.txt \\" << std::endl;
        std::cout << "    --output output/0730 \\" << std::endl;
        std::cout << "    --size 512x512" << std::endl;
        return 1;
    }

    // 解析命令行参数
    std::vector<std::string> args(argv, argv + argc);

    // 检查是否为批量处理模式
    bool batch_mode = false;
    std::string data_root, filelist_path, output_dir;
    int image_width = 256, image_height = 256;

    for (size_t i = 1; i < args.size(); i++) {
        if (args[i] == "--batch") {
            batch_mode = true;
        } else if (args[i] == "--data-root" && i + 1 < args.size()) {
            data_root = args[++i];
        } else if (args[i] == "--filelist" && i + 1 < args.size()) {
            filelist_path = args[++i];
        } else if (args[i] == "--output" && i + 1 < args.size()) {
            output_dir = args[++i];
        } else if (args[i] == "--size" && i + 1 < args.size()) {
            std::string size_str = args[++i];
            size_t x_pos = size_str.find('x');
            if (x_pos != std::string::npos) {
                image_width = std::stoi(size_str.substr(0, x_pos));
                image_height = std::stoi(size_str.substr(x_pos + 1));
            }
        }
    }

    // 只支持批量处理模式
    if (!batch_mode) {
        std::cerr << "❌ 请使用 --batch 参数进行批量处理" << std::endl;
        return 1;
    }

    if (data_root.empty() || filelist_path.empty() || output_dir.empty()) {
        std::cerr << "❌ 批量模式需要指定 --data-root, --filelist, --output 参数" << std::endl;
        return 1;
    }

    return processBatchDataset(data_root, filelist_path, output_dir, image_width, image_height);
}
