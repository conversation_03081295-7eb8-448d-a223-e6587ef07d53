# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.26

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /opt/conda/bin/cmake

# The command to remove a file.
RM = /opt/conda/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/repos/Mask2Former_v2/tools/debug

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/repos/Mask2Former_v2/tools/debug/build

# Include any dependencies generated for this target.
include CMakeFiles/pointcloud_processor.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/pointcloud_processor.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/pointcloud_processor.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/pointcloud_processor.dir/flags.make

CMakeFiles/pointcloud_processor.dir/pointcloud_to_density_processor.cpp.o: CMakeFiles/pointcloud_processor.dir/flags.make
CMakeFiles/pointcloud_processor.dir/pointcloud_to_density_processor.cpp.o: /home/<USER>/repos/Mask2Former_v2/tools/debug/pointcloud_to_density_processor.cpp
CMakeFiles/pointcloud_processor.dir/pointcloud_to_density_processor.cpp.o: CMakeFiles/pointcloud_processor.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/repos/Mask2Former_v2/tools/debug/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/pointcloud_processor.dir/pointcloud_to_density_processor.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/pointcloud_processor.dir/pointcloud_to_density_processor.cpp.o -MF CMakeFiles/pointcloud_processor.dir/pointcloud_to_density_processor.cpp.o.d -o CMakeFiles/pointcloud_processor.dir/pointcloud_to_density_processor.cpp.o -c /home/<USER>/repos/Mask2Former_v2/tools/debug/pointcloud_to_density_processor.cpp

CMakeFiles/pointcloud_processor.dir/pointcloud_to_density_processor.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/pointcloud_processor.dir/pointcloud_to_density_processor.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/repos/Mask2Former_v2/tools/debug/pointcloud_to_density_processor.cpp > CMakeFiles/pointcloud_processor.dir/pointcloud_to_density_processor.cpp.i

CMakeFiles/pointcloud_processor.dir/pointcloud_to_density_processor.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/pointcloud_processor.dir/pointcloud_to_density_processor.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/repos/Mask2Former_v2/tools/debug/pointcloud_to_density_processor.cpp -o CMakeFiles/pointcloud_processor.dir/pointcloud_to_density_processor.cpp.s

# Object files for target pointcloud_processor
pointcloud_processor_OBJECTS = \
"CMakeFiles/pointcloud_processor.dir/pointcloud_to_density_processor.cpp.o"

# External object files for target pointcloud_processor
pointcloud_processor_EXTERNAL_OBJECTS =

pointcloud_processor: CMakeFiles/pointcloud_processor.dir/pointcloud_to_density_processor.cpp.o
pointcloud_processor: CMakeFiles/pointcloud_processor.dir/build.make
pointcloud_processor: /usr/lib/x86_64-linux-gnu/libopencv_stitching.so.4.5.4d
pointcloud_processor: /usr/lib/x86_64-linux-gnu/libopencv_alphamat.so.4.5.4d
pointcloud_processor: /usr/lib/x86_64-linux-gnu/libopencv_aruco.so.4.5.4d
pointcloud_processor: /usr/lib/x86_64-linux-gnu/libopencv_barcode.so.4.5.4d
pointcloud_processor: /usr/lib/x86_64-linux-gnu/libopencv_bgsegm.so.4.5.4d
pointcloud_processor: /usr/lib/x86_64-linux-gnu/libopencv_bioinspired.so.4.5.4d
pointcloud_processor: /usr/lib/x86_64-linux-gnu/libopencv_ccalib.so.4.5.4d
pointcloud_processor: /usr/lib/x86_64-linux-gnu/libopencv_dnn_objdetect.so.4.5.4d
pointcloud_processor: /usr/lib/x86_64-linux-gnu/libopencv_dnn_superres.so.4.5.4d
pointcloud_processor: /usr/lib/x86_64-linux-gnu/libopencv_dpm.so.4.5.4d
pointcloud_processor: /usr/lib/x86_64-linux-gnu/libopencv_face.so.4.5.4d
pointcloud_processor: /usr/lib/x86_64-linux-gnu/libopencv_freetype.so.4.5.4d
pointcloud_processor: /usr/lib/x86_64-linux-gnu/libopencv_fuzzy.so.4.5.4d
pointcloud_processor: /usr/lib/x86_64-linux-gnu/libopencv_hdf.so.4.5.4d
pointcloud_processor: /usr/lib/x86_64-linux-gnu/libopencv_hfs.so.4.5.4d
pointcloud_processor: /usr/lib/x86_64-linux-gnu/libopencv_img_hash.so.4.5.4d
pointcloud_processor: /usr/lib/x86_64-linux-gnu/libopencv_intensity_transform.so.4.5.4d
pointcloud_processor: /usr/lib/x86_64-linux-gnu/libopencv_line_descriptor.so.4.5.4d
pointcloud_processor: /usr/lib/x86_64-linux-gnu/libopencv_mcc.so.4.5.4d
pointcloud_processor: /usr/lib/x86_64-linux-gnu/libopencv_quality.so.4.5.4d
pointcloud_processor: /usr/lib/x86_64-linux-gnu/libopencv_rapid.so.4.5.4d
pointcloud_processor: /usr/lib/x86_64-linux-gnu/libopencv_reg.so.4.5.4d
pointcloud_processor: /usr/lib/x86_64-linux-gnu/libopencv_rgbd.so.4.5.4d
pointcloud_processor: /usr/lib/x86_64-linux-gnu/libopencv_saliency.so.4.5.4d
pointcloud_processor: /usr/lib/x86_64-linux-gnu/libopencv_shape.so.4.5.4d
pointcloud_processor: /usr/lib/x86_64-linux-gnu/libopencv_stereo.so.4.5.4d
pointcloud_processor: /usr/lib/x86_64-linux-gnu/libopencv_structured_light.so.4.5.4d
pointcloud_processor: /usr/lib/x86_64-linux-gnu/libopencv_superres.so.4.5.4d
pointcloud_processor: /usr/lib/x86_64-linux-gnu/libopencv_surface_matching.so.4.5.4d
pointcloud_processor: /usr/lib/x86_64-linux-gnu/libopencv_tracking.so.4.5.4d
pointcloud_processor: /usr/lib/x86_64-linux-gnu/libopencv_videostab.so.4.5.4d
pointcloud_processor: /usr/lib/x86_64-linux-gnu/libopencv_viz.so.4.5.4d
pointcloud_processor: /usr/lib/x86_64-linux-gnu/libopencv_wechat_qrcode.so.4.5.4d
pointcloud_processor: /usr/lib/x86_64-linux-gnu/libopencv_xobjdetect.so.4.5.4d
pointcloud_processor: /usr/lib/x86_64-linux-gnu/libopencv_xphoto.so.4.5.4d
pointcloud_processor: /usr/lib/x86_64-linux-gnu/libpcl_apps.so
pointcloud_processor: /usr/lib/x86_64-linux-gnu/libpcl_outofcore.so
pointcloud_processor: /usr/lib/x86_64-linux-gnu/libpcl_people.so
pointcloud_processor: /usr/lib/libOpenNI.so
pointcloud_processor: /usr/lib/x86_64-linux-gnu/libusb-1.0.so
pointcloud_processor: /usr/lib/x86_64-linux-gnu/libOpenNI2.so
pointcloud_processor: /usr/lib/x86_64-linux-gnu/libusb-1.0.so
pointcloud_processor: /usr/lib/x86_64-linux-gnu/libflann_cpp.so
pointcloud_processor: /usr/lib/x86_64-linux-gnu/libopencv_highgui.so.4.5.4d
pointcloud_processor: /usr/lib/x86_64-linux-gnu/libopencv_datasets.so.4.5.4d
pointcloud_processor: /usr/lib/x86_64-linux-gnu/libopencv_plot.so.4.5.4d
pointcloud_processor: /usr/lib/x86_64-linux-gnu/libopencv_text.so.4.5.4d
pointcloud_processor: /usr/lib/x86_64-linux-gnu/libopencv_ml.so.4.5.4d
pointcloud_processor: /usr/lib/x86_64-linux-gnu/libopencv_phase_unwrapping.so.4.5.4d
pointcloud_processor: /usr/lib/x86_64-linux-gnu/libopencv_optflow.so.4.5.4d
pointcloud_processor: /usr/lib/x86_64-linux-gnu/libopencv_ximgproc.so.4.5.4d
pointcloud_processor: /usr/lib/x86_64-linux-gnu/libopencv_video.so.4.5.4d
pointcloud_processor: /usr/lib/x86_64-linux-gnu/libopencv_videoio.so.4.5.4d
pointcloud_processor: /usr/lib/x86_64-linux-gnu/libopencv_imgcodecs.so.4.5.4d
pointcloud_processor: /usr/lib/x86_64-linux-gnu/libopencv_objdetect.so.4.5.4d
pointcloud_processor: /usr/lib/x86_64-linux-gnu/libopencv_calib3d.so.4.5.4d
pointcloud_processor: /usr/lib/x86_64-linux-gnu/libopencv_dnn.so.4.5.4d
pointcloud_processor: /usr/lib/x86_64-linux-gnu/libopencv_features2d.so.4.5.4d
pointcloud_processor: /usr/lib/x86_64-linux-gnu/libopencv_flann.so.4.5.4d
pointcloud_processor: /usr/lib/x86_64-linux-gnu/libopencv_photo.so.4.5.4d
pointcloud_processor: /usr/lib/x86_64-linux-gnu/libopencv_imgproc.so.4.5.4d
pointcloud_processor: /usr/lib/x86_64-linux-gnu/libopencv_core.so.4.5.4d
pointcloud_processor: /usr/lib/x86_64-linux-gnu/libpcl_surface.so
pointcloud_processor: /usr/lib/x86_64-linux-gnu/libpcl_keypoints.so
pointcloud_processor: /usr/lib/x86_64-linux-gnu/libpcl_tracking.so
pointcloud_processor: /usr/lib/x86_64-linux-gnu/libpcl_recognition.so
pointcloud_processor: /usr/lib/x86_64-linux-gnu/libpcl_registration.so
pointcloud_processor: /usr/lib/x86_64-linux-gnu/libpcl_stereo.so
pointcloud_processor: /usr/lib/x86_64-linux-gnu/libpcl_segmentation.so
pointcloud_processor: /usr/lib/x86_64-linux-gnu/libpcl_features.so
pointcloud_processor: /usr/lib/x86_64-linux-gnu/libpcl_filters.so
pointcloud_processor: /usr/lib/x86_64-linux-gnu/libpcl_sample_consensus.so
pointcloud_processor: /usr/lib/x86_64-linux-gnu/libpcl_ml.so
pointcloud_processor: /usr/lib/x86_64-linux-gnu/libpcl_visualization.so
pointcloud_processor: /usr/lib/x86_64-linux-gnu/libpcl_search.so
pointcloud_processor: /usr/lib/x86_64-linux-gnu/libpcl_kdtree.so
pointcloud_processor: /usr/lib/x86_64-linux-gnu/libpcl_io.so
pointcloud_processor: /usr/lib/x86_64-linux-gnu/libpcl_octree.so
pointcloud_processor: /usr/lib/x86_64-linux-gnu/libpng.so
pointcloud_processor: /usr/lib/x86_64-linux-gnu/libz.so
pointcloud_processor: /usr/lib/libOpenNI.so
pointcloud_processor: /usr/lib/x86_64-linux-gnu/libusb-1.0.so
pointcloud_processor: /usr/lib/x86_64-linux-gnu/libOpenNI2.so
pointcloud_processor: /usr/lib/x86_64-linux-gnu/libvtkChartsCore-9.1.so.9.1.0
pointcloud_processor: /usr/lib/x86_64-linux-gnu/libvtkInteractionImage-9.1.so.9.1.0
pointcloud_processor: /usr/lib/x86_64-linux-gnu/libvtkIOGeometry-9.1.so.9.1.0
pointcloud_processor: /usr/lib/x86_64-linux-gnu/libjsoncpp.so
pointcloud_processor: /usr/lib/x86_64-linux-gnu/libvtkIOPLY-9.1.so.9.1.0
pointcloud_processor: /usr/lib/x86_64-linux-gnu/libvtkRenderingLOD-9.1.so.9.1.0
pointcloud_processor: /usr/lib/x86_64-linux-gnu/libvtkViewsContext2D-9.1.so.9.1.0
pointcloud_processor: /usr/lib/x86_64-linux-gnu/libvtkViewsCore-9.1.so.9.1.0
pointcloud_processor: /usr/lib/x86_64-linux-gnu/libvtkGUISupportQt-9.1.so.9.1.0
pointcloud_processor: /usr/lib/x86_64-linux-gnu/libvtkInteractionWidgets-9.1.so.9.1.0
pointcloud_processor: /usr/lib/x86_64-linux-gnu/libvtkFiltersModeling-9.1.so.9.1.0
pointcloud_processor: /usr/lib/x86_64-linux-gnu/libvtkInteractionStyle-9.1.so.9.1.0
pointcloud_processor: /usr/lib/x86_64-linux-gnu/libvtkFiltersExtraction-9.1.so.9.1.0
pointcloud_processor: /usr/lib/x86_64-linux-gnu/libvtkIOLegacy-9.1.so.9.1.0
pointcloud_processor: /usr/lib/x86_64-linux-gnu/libvtkIOCore-9.1.so.9.1.0
pointcloud_processor: /usr/lib/x86_64-linux-gnu/libvtkRenderingAnnotation-9.1.so.9.1.0
pointcloud_processor: /usr/lib/x86_64-linux-gnu/libvtkRenderingContext2D-9.1.so.9.1.0
pointcloud_processor: /usr/lib/x86_64-linux-gnu/libvtkRenderingFreeType-9.1.so.9.1.0
pointcloud_processor: /usr/lib/x86_64-linux-gnu/libfreetype.so
pointcloud_processor: /usr/lib/x86_64-linux-gnu/libvtkImagingSources-9.1.so.9.1.0
pointcloud_processor: /usr/lib/x86_64-linux-gnu/libvtkIOImage-9.1.so.9.1.0
pointcloud_processor: /usr/lib/x86_64-linux-gnu/libvtkImagingCore-9.1.so.9.1.0
pointcloud_processor: /usr/lib/x86_64-linux-gnu/libvtkRenderingOpenGL2-9.1.so.9.1.0
pointcloud_processor: /usr/lib/x86_64-linux-gnu/libvtkRenderingUI-9.1.so.9.1.0
pointcloud_processor: /usr/lib/x86_64-linux-gnu/libvtkRenderingCore-9.1.so.9.1.0
pointcloud_processor: /usr/lib/x86_64-linux-gnu/libvtkCommonColor-9.1.so.9.1.0
pointcloud_processor: /usr/lib/x86_64-linux-gnu/libvtkFiltersGeometry-9.1.so.9.1.0
pointcloud_processor: /usr/lib/x86_64-linux-gnu/libvtkFiltersSources-9.1.so.9.1.0
pointcloud_processor: /usr/lib/x86_64-linux-gnu/libvtkFiltersGeneral-9.1.so.9.1.0
pointcloud_processor: /usr/lib/x86_64-linux-gnu/libvtkCommonComputationalGeometry-9.1.so.9.1.0
pointcloud_processor: /usr/lib/x86_64-linux-gnu/libvtkFiltersCore-9.1.so.9.1.0
pointcloud_processor: /usr/lib/x86_64-linux-gnu/libvtkCommonExecutionModel-9.1.so.9.1.0
pointcloud_processor: /usr/lib/x86_64-linux-gnu/libvtkCommonDataModel-9.1.so.9.1.0
pointcloud_processor: /usr/lib/x86_64-linux-gnu/libvtkCommonMisc-9.1.so.9.1.0
pointcloud_processor: /usr/lib/x86_64-linux-gnu/libvtkCommonTransforms-9.1.so.9.1.0
pointcloud_processor: /usr/lib/x86_64-linux-gnu/libvtkCommonMath-9.1.so.9.1.0
pointcloud_processor: /usr/lib/x86_64-linux-gnu/libvtkkissfft-9.1.so.9.1.0
pointcloud_processor: /usr/lib/x86_64-linux-gnu/libGLEW.so
pointcloud_processor: /usr/lib/x86_64-linux-gnu/libX11.so
pointcloud_processor: /usr/lib/x86_64-linux-gnu/libQt5OpenGL.so.5.15.3
pointcloud_processor: /usr/lib/x86_64-linux-gnu/libQt5Widgets.so.5.15.3
pointcloud_processor: /usr/lib/x86_64-linux-gnu/libQt5Gui.so.5.15.3
pointcloud_processor: /usr/lib/x86_64-linux-gnu/libQt5Core.so.5.15.3
pointcloud_processor: /usr/lib/x86_64-linux-gnu/libvtkCommonCore-9.1.so.9.1.0
pointcloud_processor: /usr/lib/x86_64-linux-gnu/libtbb.so.12.5
pointcloud_processor: /usr/lib/x86_64-linux-gnu/libvtksys-9.1.so.9.1.0
pointcloud_processor: /usr/lib/x86_64-linux-gnu/libpcl_common.so
pointcloud_processor: /usr/lib/x86_64-linux-gnu/libboost_system.so.1.74.0
pointcloud_processor: /usr/lib/x86_64-linux-gnu/libboost_filesystem.so.1.74.0
pointcloud_processor: /usr/lib/x86_64-linux-gnu/libboost_date_time.so.1.74.0
pointcloud_processor: /usr/lib/x86_64-linux-gnu/libboost_iostreams.so.1.74.0
pointcloud_processor: /usr/lib/x86_64-linux-gnu/libboost_serialization.so.1.74.0
pointcloud_processor: /usr/lib/x86_64-linux-gnu/libqhull_r.so.8.0.2
pointcloud_processor: CMakeFiles/pointcloud_processor.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/home/<USER>/repos/Mask2Former_v2/tools/debug/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking CXX executable pointcloud_processor"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/pointcloud_processor.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/pointcloud_processor.dir/build: pointcloud_processor
.PHONY : CMakeFiles/pointcloud_processor.dir/build

CMakeFiles/pointcloud_processor.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/pointcloud_processor.dir/cmake_clean.cmake
.PHONY : CMakeFiles/pointcloud_processor.dir/clean

CMakeFiles/pointcloud_processor.dir/depend:
	cd /home/<USER>/repos/Mask2Former_v2/tools/debug/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/repos/Mask2Former_v2/tools/debug /home/<USER>/repos/Mask2Former_v2/tools/debug /home/<USER>/repos/Mask2Former_v2/tools/debug/build /home/<USER>/repos/Mask2Former_v2/tools/debug/build /home/<USER>/repos/Mask2Former_v2/tools/debug/build/CMakeFiles/pointcloud_processor.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : CMakeFiles/pointcloud_processor.dir/depend

