# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.26

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /opt/conda/bin/cmake

# The command to remove a file.
RM = /opt/conda/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/repos/Mask2Former_v2/tools/debug

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/repos/Mask2Former_v2/tools/debug/build

#=============================================================================
# Directory level rules for the build root directory

# The main recursive "all" target.
all: CMakeFiles/pointcloud_processor.dir/all
.PHONY : all

# The main recursive "preinstall" target.
preinstall:
.PHONY : preinstall

# The main recursive "clean" target.
clean: CMakeFiles/pointcloud_processor.dir/clean
.PHONY : clean

#=============================================================================
# Target rules for target CMakeFiles/pointcloud_processor.dir

# All Build rule for target.
CMakeFiles/pointcloud_processor.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/pointcloud_processor.dir/build.make CMakeFiles/pointcloud_processor.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/pointcloud_processor.dir/build.make CMakeFiles/pointcloud_processor.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/repos/Mask2Former_v2/tools/debug/build/CMakeFiles --progress-num=1,2 "Built target pointcloud_processor"
.PHONY : CMakeFiles/pointcloud_processor.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/pointcloud_processor.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/repos/Mask2Former_v2/tools/debug/build/CMakeFiles 2
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/pointcloud_processor.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/repos/Mask2Former_v2/tools/debug/build/CMakeFiles 0
.PHONY : CMakeFiles/pointcloud_processor.dir/rule

# Convenience name for target.
pointcloud_processor: CMakeFiles/pointcloud_processor.dir/rule
.PHONY : pointcloud_processor

# clean rule for target.
CMakeFiles/pointcloud_processor.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/pointcloud_processor.dir/build.make CMakeFiles/pointcloud_processor.dir/clean
.PHONY : CMakeFiles/pointcloud_processor.dir/clean

#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

