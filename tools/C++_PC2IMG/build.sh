#!/bin/bash

# Build script for PointCloudDensityProcessor

echo "🚀 Building Point Cloud Density Processor"
echo "=========================================="

# Check for PDAL support
USE_PDAL=${1:-OFF}
if [ "$USE_PDAL" = "ON" ] || [ "$USE_PDAL" = "on" ] || [ "$USE_PDAL" = "true" ]; then
    echo "🔧 Building with PDAL support (LAS files enabled)"
    CMAKE_OPTIONS="-DUSE_PDAL=ON"
else
    echo "🔧 Building without PDAL support (PCD/PLY/TXT files only)"
    CMAKE_OPTIONS="-DUSE_PDAL=OFF"
fi

# Create build directory
mkdir -p build
cd build

# Configure with CMake
echo "⚙️  Configuring with CMake..."
cmake $CMAKE_OPTIONS ..

# Build
echo "🔨 Building..."
make -j4

# Check if build was successful
if [ $? -eq 0 ]; then
    echo ""
    echo "✅ Build successful!"
    echo ""
    echo "📋 Usage:"
    echo "  ./pointcloud_processor <mapping.txt> <input_pointcloud> [output.png]"
    echo ""
    echo "📋 Supported formats:"
    if [ "$USE_PDAL" = "ON" ] || [ "$USE_PDAL" = "on" ] || [ "$USE_PDAL" = "true" ]; then
        echo "  - .las/.laz (LAS format via PDAL)"
    fi
    echo "  - .pcd (PCL format)"
    echo "  - .ply (PLY format)"
    echo "  - .txt/.xyz (Text format: X Y Z per line)"
    echo ""
    echo "📋 Examples:"
    echo "  ./pointcloud_processor mapping.txt pointcloud.pcd density.png"
    echo "  ./pointcloud_processor mapping.txt pointcloud.txt density.png"
    if [ "$USE_PDAL" = "ON" ] || [ "$USE_PDAL" = "on" ] || [ "$USE_PDAL" = "true" ]; then
        echo "  ./pointcloud_processor mapping.txt pointcloud.las density.png"
    fi
    echo ""
    echo "💡 For LAS files without PDAL, use:"
    echo "  python tools/debug/convert_las_to_txt.py input.las output.txt"
else
    echo ""
    echo "❌ Build failed!"
    echo ""
    echo "🔧 Try installing dependencies:"
    echo "  sudo apt-get install -y build-essential cmake libopencv-dev libpcl-dev"
    echo ""
    echo "🔧 For LAS support, install PDAL and rebuild:"
    echo "  sudo apt-get install -y libpdal-dev pdal"
    echo "  ./build.sh ON"
fi

# Return to original directory
cd ..
